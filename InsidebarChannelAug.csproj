<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Platforms>AnyCPU</Platforms>
    <AlgoType>Indicator</AlgoType>
    <AssemblyName>InsidebarChannelAug</AssemblyName>
    <RootNamespace>InsidebarChannelAug</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.4\..\..\Settings\Scripts\Indicators\InsidebarChannelAug</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.4\..\..\Settings\Scripts\Indicators\InsidebarChannelAug</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="TradingPlatform.BusinessLayer">
      <HintPath>D:\Quantower\TradingPlatform\v1.144.4\bin\TradingPlatform.BusinessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="參考\**" />
    <EmbeddedResource Remove="參考\**" />
    <None Remove="參考\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
  </ItemGroup>
</Project>