<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Platforms>AnyCPU</Platforms>
    <AlgoType>Strategy</AlgoType>
    <AssemblyName>InsideBarFiboStrategy</AssemblyName>
    <RootNamespace>Strategy1k</RootNamespace>
    <StartAction>Program</StartAction>
    <StartProgram>D:\Quantower\TradingPlatform\v1.144.4\Console.StarterNew.exe</StartProgram>
    <StartArguments>--address 127.0.0.1 --port 58437</StartArguments>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.4\..\..\Settings\Scripts\Strategies\InsideBarFiboStrategy</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.4\..\..\Settings\Scripts\Strategies\InsideBarFiboStrategy</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="TradingPlatform.BusinessLayer">
      <HintPath>D:\Quantower\TradingPlatform\v1.144.4\bin\TradingPlatform.BusinessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="參考\**" />
    <Compile Remove="InsidebarChannelAug.cs" />
    <EmbeddedResource Remove="參考\**" />
    <None Remove="參考\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
  </ItemGroup>
</Project>