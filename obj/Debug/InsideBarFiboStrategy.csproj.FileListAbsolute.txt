C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.AssemblyInfo.cs
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.csproj.CoreCompileInputs.cache
D:\Quantower\Settings\Scripts\Strategies\InsideBarFiboStrategy\InsideBarFiboStrategy.deps.json
D:\Quantower\Settings\Scripts\Strategies\InsideBarFiboStrategy\InsideBarFiboStrategy.dll
D:\Quantower\Settings\Scripts\Strategies\InsideBarFiboStrategy\InsideBarFiboStrategy.pdb
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.dll
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\refint\InsideBarFiboStrategy.dll
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\InsideBarFiboStrategy.pdb
C:\Users\<USER>\source\repos\InsidebarChannelAug\obj\Debug\ref\InsideBarFiboStrategy.dll
