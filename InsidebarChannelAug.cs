// Copyright QUANTOWER LLC. © 2017-2023. All rights reserved.

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using TradingPlatform.BusinessLayer;

namespace InsidebarChannelAug;

public class InsidebarChannelAugIndicator : Indicator
{
    #region Parameters
    private int barIndex = 1;
    private List<Box> listboxes = null;
    private HistoricalDataCustom insidebarSourceHD;

    // Golden ratio calculations (from reference)
    private double goldenRatio = ((1 + Math.Sqrt(5)) / 2);
    private double goldenFractionComplement = 1 - 1 / ((1 + Math.Sqrt(5)) / 2); // 0.381966011250105

    // Channel values - initialize to NaN
    private double topChannel1382 = double.NaN;
    private double topChannel1618 = double.NaN;
    private double bottomChannel1382 = double.NaN;
    private double bottomChannel1618 = double.NaN;

    // Recent top and bottom tracking with their boxes
    private List<TopBottomRecord> recentTops = null;    // 記錄近期確立的頂部及其對應的 Box
    private List<TopBottomRecord> recentBottoms = null; // 記錄近期確立的底部及其對應的 Box
    private bool hasValidPair = false;

    // 買賣標記記錄列表（基於 MarkBuySell 指標）
    private List<BuySellMarkRecord> recentBuyMarks = null;  // 記錄近期的做多標記
    private List<BuySellMarkRecord> recentSellMarks = null; // 記錄近期的做空標記

    // Current best pairing
    private TopBottomRecord bestTop = null;
    private TopBottomRecord bestBottom = null;

    // Time-based matching settings (參考 MarkBuySellLine.cs)
    private const int TIME_CHECK1_START = 1200;  // 12:00
    private const int TIME_CHECK1_END = 1300;    // 13:00
    private const int TIME_CHECK2_START = 1500;  // 15:00
    private const int TIME_CHECK2_END = 1600;    // 16:00
    private const int TIME_CHECK3_START = 2000;  // 20:00
    private const int TIME_CHECK3_END = 2300;    // 23:00
    private DateTime lastMatchTime = DateTime.MinValue;

    // 動態調整參數設置
    [InputParameter("Minimum Tops Required", 6, 1, 20, 1, 0)]
    public int MinTopsRequired { get; set; } = 3;

    [InputParameter("Minimum Bottoms Required", 7, 1, 20, 1, 0)]
    public int MinBottomsRequired { get; set; } = 3;

    [InputParameter("Enable Dynamic Adjustment", 8)]
    public bool EnableDynamicAdjustment { get; set; } = true;

    // 動態調整的內部變量
    private int currentPeriod = 5;  // 當前檢測週期
    private int[] lookbackLevels = { 10, 20, 30, 40, 50 };  // 可調整的回看週期
    private int currentLookbackIndex = 0;  // 上次匹配時間

    #endregion Parameters

    public InsidebarChannelAugIndicator()
    {
        this.Name = "Inside Bar Aug Channel indicator";

        // Line series for strategy access (order matters!)
        this.AddLineSeries("BOTTOM_1382", Color.Green, 2, LineStyle.Solid);      // Index 0
        this.AddLineSeries("BOTTOM_1618", Color.DarkGreen, 2, LineStyle.Solid);  // Index 1
        this.AddLineSeries("TOP_1382", Color.Red, 2, LineStyle.Solid);           // Index 2
        this.AddLineSeries("TOP_1618", Color.DarkRed, 2, LineStyle.Solid);       // Index 3

        // Box boundary lines for strategy TP levels
        this.AddLineSeries("BOTTOM_BOX_OC", Color.Cyan, 1, LineStyle.Dash);      // Index 4 - Bottom Box min(o,c)
        this.AddLineSeries("TOP_BOX_OC", Color.Magenta, 1, LineStyle.Dash);      // Index 5 - Top Box max(o,c)

        this.SeparateWindow = false;
    }

    #region Base overrides

    protected override void OnInit()
    {
        this.listboxes = new List<Box>();
        this.recentTops = new List<TopBottomRecord>();
        this.recentBottoms = new List<TopBottomRecord>();
        this.recentBuyMarks = new List<BuySellMarkRecord>();
        this.recentSellMarks = new List<BuySellMarkRecord>();
        this.insidebarSourceHD = new HistoricalDataCustom(this);
    }
    protected override void OnUpdate(UpdateArgs args)
    {
        if (this.Count < 50)
            return;

        // Check for inside bar pattern
        if (IsInsideBar(1, barIndex + 1))
        {
            barIndex++;
        }

        // Detect end of inside bar sequence and create box
        if (IsInsideBar(1, barIndex) && !IsInsideBar(0, barIndex))
        {
            double previousHigh = Math.Max(Open(barIndex + 1), Close(barIndex + 1));
            double previousLow = Math.Min(Open(barIndex + 1), Close(barIndex + 1));

            if (barIndex > 1)
            {
                Box box = new Box
                {
                    PreviusHigh = previousHigh,
                    PreviusLow = previousLow,
                    BarCount = barIndex,
                    CurrentTime = Time(1),
                    PreviusTime = Time(barIndex + 1)
                };

                listboxes.Add(box);

                // Update insidebarSourceHD like in reference
                this.insidebarSourceHD[PriceType.High] = previousHigh;
                this.insidebarSourceHD[PriceType.Low] = previousLow;

                // Debug output
                string boxType = box.IsLargeBox ? "Large" : (box.IsSmallBox ? "Small" : "Medium");
                Core.Instance.Loggers.Log($"New {boxType} Box: BarCount={barIndex}, High={previousHigh:F4}, Low={previousLow:F4}", LoggingLevel.Trading);

                barIndex = 1;
            }
        }
        else
        {
            // Update insidebarSourceHD with previous values when no new box
            this.insidebarSourceHD[PriceType.High] = this.insidebarSourceHD.GetPrice(PriceType.High, 1);
            this.insidebarSourceHD[PriceType.Low] = this.insidebarSourceHD.GetPrice(PriceType.Low, 1);
        }

        // Detect confirmed tops and bottoms using lookback
        DetectConfirmedTopsBottoms();

        // Check if it's time for daily matching
        CheckDailyMatching();

        // Always update channel calculations
        UpdateChannelLevels();

        // Set channel values to line series (match the AddLineSeries order)
        SetValue(bottomChannel1382, 0);  // BOTTOM_1382
        SetValue(bottomChannel1618, 1);  // BOTTOM_1618
        SetValue(topChannel1382, 2);     // TOP_1382
        SetValue(topChannel1618, 3);     // TOP_1618

        // Calculate and set Box boundary values for strategy TP levels
        double bottomBoxOC = double.NaN;
        double topBoxOC = double.NaN;

        if (hasValidPair)
        {
            // Bottom Box min(o,c) for buy TP
            if (bestBottom?.AssociatedBox != null)
            {
                bottomBoxOC = bestBottom.AssociatedBox.MinOC;
            }

            // Top Box max(o,c) for sell TP
            if (bestTop?.AssociatedBox != null)
            {
                topBoxOC = bestTop.AssociatedBox.MaxOC;
            }
        }

        SetValue(bottomBoxOC, 4);  // BOTTOM_BOX_OC
        SetValue(topBoxOC, 5);     // TOP_BOX_OC
    }

    private void UpdateChannelLevels()
    {
        // Find the best pairing from all available top and bottom boxes
        FindBestPairing();

        if (hasValidPair && bestTop != null && bestBottom != null)
        {
            // Use the best paired top and bottom boxes directly (no smoothing)
            var bestTopBox = bestTop.AssociatedBox;
            var bestBottomBox = bestBottom.AssociatedBox;

            double BOX_TOP = Math.Max(bestTopBox.MaxOC, bestBottomBox.MaxOC);
            double BOX_BOT = Math.Min(bestTopBox.MinOC, bestBottomBox.MinOC);
            double range = BOX_TOP - BOX_BOT;

            // Calculate Fibonacci channels using the correct formula:
            // 上通道 = 底部 + range * fibo_level
            // 下通道 = 頂部 - range * fibo_level
            topChannel1382 = BOX_BOT + range * goldenRatio;           // 1.618
            topChannel1618 = BOX_BOT + range / goldenFractionComplement; // 2.618
            bottomChannel1382 = BOX_TOP - range * goldenRatio;        // 1.618
            bottomChannel1618 = BOX_TOP - range / goldenFractionComplement; // 2.618

            // Debug output
            string topType = bestTopBox.IsLargeBox ? "Large" : "Small";
            string bottomType = bestBottomBox.IsLargeBox ? "Large" : "Small";
            Core.Instance.Loggers.Log($"Best Pair: Top({topType}, {bestTopBox.BarCount} bars) + Bottom({bottomType}, {bestBottomBox.BarCount} bars), Range: {range:F4}", LoggingLevel.Trading);
            Core.Instance.Loggers.Log($"BOX_TOP: {BOX_TOP:F4}, BOX_BOT: {BOX_BOT:F4}", LoggingLevel.Trading);
            Core.Instance.Loggers.Log($"Channels - Top1382: {topChannel1382:F4}, Top1618: {topChannel1618:F4}, Bot1382: {bottomChannel1382:F4}, Bot1618: {bottomChannel1618:F4}", LoggingLevel.Trading);
        }
        else
        {
            // No valid pair, set to NaN so lines don't display
            topChannel1382 = double.NaN;
            topChannel1618 = double.NaN;
            bottomChannel1382 = double.NaN;
            bottomChannel1618 = double.NaN;

            Core.Instance.Loggers.Log($"No valid pairing available (recentTops: {recentTops.Count}, recentBottoms: {recentBottoms.Count}), setting channels to NaN", LoggingLevel.Trading);
        }
    }

    private void DetectConfirmedTopsBottoms()
    {
        // 使用動態調整的 lookback 週期
        int[] lookbacks = GetDynamicLookbacks();

        foreach (int lookback in lookbacks)
        {
            if (Count < lookback + 10) continue; // 確保有足夠的數據

            // 檢查是否為確認的頂部（使用動態調整的 period）
            if (IsConfirmedTop(lookback, currentPeriod))
            {
                // 找到當時對應的 Box
                var associatedBox = FindBoxAtTime(lookback);
                if (associatedBox != null)  // 所有 Box 都是有效的（大或小）
                {
                    var topRecord = new TopBottomRecord
                    {
                        Time = Time(lookback),
                        Price = High(lookback),  // 使用 High 價格
                        AssociatedBox = associatedBox,
                        IsTop = true,
                        ConfirmationOffset = lookback
                    };

                    // 避免重複添加相同的頂部（增加時間間隔）
                    if (!recentTops.Any(t => Math.Abs((t.Time - topRecord.Time).TotalMinutes) < 120)) // 2小時間隔
                    {
                        recentTops.Add(topRecord);
                        string boxType = associatedBox.IsLargeBox ? "Large" : "Small";
                        Core.Instance.Loggers.Log($"Confirmed Top: {boxType} Box (BarCount={associatedBox.BarCount}) at {topRecord.Time:HH:mm}, Price={topRecord.Price:F4}", LoggingLevel.Trading);

                        // 清理過舊的記錄（超過 24 小時的）
                        CleanOldRecords();
                    }
                }
            }

            // 檢查是否為確認的底部（使用動態調整的 period）
            if (IsConfirmedBottom(lookback, currentPeriod))
            {
                // 找到當時對應的 Box
                var associatedBox = FindBoxAtTime(lookback);
                if (associatedBox != null)  // 所有 Box 都是有效的（大或小）
                {
                    var bottomRecord = new TopBottomRecord
                    {
                        Time = Time(lookback),
                        Price = Low(lookback),  // 使用 Low 價格
                        AssociatedBox = associatedBox,
                        IsTop = false,
                        ConfirmationOffset = lookback
                    };

                    // 避免重複添加相同的底部（增加時間間隔）
                    if (!recentBottoms.Any(b => Math.Abs((b.Time - bottomRecord.Time).TotalMinutes) < 120)) // 2小時間隔
                    {
                        recentBottoms.Add(bottomRecord);
                        string boxType = associatedBox.IsLargeBox ? "Large" : "Small";
                        Core.Instance.Loggers.Log($"Confirmed Bottom: {boxType} Box (BarCount={associatedBox.BarCount}) at {bottomRecord.Time:HH:mm}, Price={bottomRecord.Price:F4}", LoggingLevel.Trading);

                        // 清理過舊的記錄已在上面調用
                    }
                }
            }
        }
    }

    private int[] GetDynamicLookbacks()
    {
        // 基礎 lookback 週期
        int[] baseLookbacks = { 10, 20, 30 };

        // 如果啟用動態調整，添加更多週期
        if (EnableDynamicAdjustment)
        {
            List<int> dynamicLookbacks = new List<int>(baseLookbacks);

            // 根據當前 lookback 索引添加額外週期
            if (currentLookbackIndex < lookbackLevels.Length)
            {
                for (int i = 0; i <= currentLookbackIndex; i++)
                {
                    if (!dynamicLookbacks.Contains(lookbackLevels[i]))
                    {
                        dynamicLookbacks.Add(lookbackLevels[i]);
                    }
                }
            }

            dynamicLookbacks.Sort();
            return dynamicLookbacks.ToArray();
        }

        return baseLookbacks;
    }

    private void CheckAndAdjustParameters()
    {
        if (!EnableDynamicAdjustment) return;

        int currentTops = recentTops.Count;
        int currentBottoms = recentBottoms.Count;

        // 檢查是否需要降低門檻
        bool needMoreTops = currentTops < MinTopsRequired;
        bool needMoreBottoms = currentBottoms < MinBottomsRequired;

        if (needMoreTops || needMoreBottoms)
        {
            // 降低檢測門檻
            if (currentPeriod > 2)
            {
                currentPeriod--;
                Core.Instance.Loggers.Log($"Lowered detection period to {currentPeriod} (Tops: {currentTops}/{MinTopsRequired}, Bottoms: {currentBottoms}/{MinBottomsRequired})", LoggingLevel.Trading);
            }
            else if (currentLookbackIndex < lookbackLevels.Length - 1)
            {
                currentLookbackIndex++;
                Core.Instance.Loggers.Log($"Increased lookback range to level {currentLookbackIndex} (max: {lookbackLevels[currentLookbackIndex]}) (Tops: {currentTops}/{MinTopsRequired}, Bottoms: {currentBottoms}/{MinBottomsRequired})", LoggingLevel.Trading);
            }
        }
        else if (currentTops >= MinTopsRequired * 2 && currentBottoms >= MinBottomsRequired * 2)
        {
            // 如果頂底數量過多，可以提高門檻
            if (currentLookbackIndex > 0)
            {
                currentLookbackIndex--;
                Core.Instance.Loggers.Log($"Decreased lookback range to level {currentLookbackIndex} (max: {lookbackLevels[currentLookbackIndex]}) (Tops: {currentTops}, Bottoms: {currentBottoms})", LoggingLevel.Trading);
            }
            else if (currentPeriod < 7)
            {
                currentPeriod++;
                Core.Instance.Loggers.Log($"Raised detection period to {currentPeriod} (Tops: {currentTops}, Bottoms: {currentBottoms})", LoggingLevel.Trading);
            }
        }
    }

    private bool IsConfirmedTop(int centerOffset, int period = 3)
    {
        if (Count < centerOffset + period + 1) return false;

        double baseHigh = High(centerOffset);
        int maxTrendValue = 0;

        // 向右檢查 period 個 bar
        for (int i = 0; i <= period; i++)
        {
            if (centerOffset + i < Count && baseHigh > High(centerOffset + i))
                maxTrendValue++;
        }

        // 向左檢查 period 個 bar
        for (int i = 0; i <= period; i++)
        {
            if (centerOffset - i >= 0 && baseHigh > High(centerOffset - i))
                maxTrendValue++;
        }

        // 如果左右兩邊所有點都比中心點低，就是頂部
        return maxTrendValue == period * 2;
    }

    private bool IsConfirmedBottom(int centerOffset, int period = 3)
    {
        if (Count < centerOffset + period + 1) return false;

        double baseLow = Low(centerOffset);
        int minTrendValue = 0;

        // 向右檢查 period 個 bar
        for (int i = 0; i <= period; i++)
        {
            if (centerOffset + i < Count && baseLow < Low(centerOffset + i))
                minTrendValue++;
        }

        // 向左檢查 period 個 bar
        for (int i = 0; i <= period; i++)
        {
            if (centerOffset - i >= 0 && baseLow < Low(centerOffset - i))
                minTrendValue++;
        }

        // 如果左右兩邊所有點都比中心點高，就是底部
        return minTrendValue == period * 2;
    }

    private void CheckDailyMatching()
    {
        // 使用參考代碼的時間檢查邏輯
        DateTime cTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(Time(0), Core.Instance.TimeUtils.SelectedTimeZone);
        int currentTime = cTime.Hour * 100 + cTime.Minute;

        // 檢查是否在指定時間範圍內
        bool ISCHECKTIME1 = currentTime >= TIME_CHECK1_START && currentTime <= TIME_CHECK1_END;
        bool ISCHECKTIME2 = currentTime >= TIME_CHECK2_START && currentTime <= TIME_CHECK2_END;
        bool ISCHECKTIME3 = currentTime >= TIME_CHECK3_START && currentTime <= TIME_CHECK3_END;
        bool ISCHECKTIME = ISCHECKTIME1 || ISCHECKTIME2 || ISCHECKTIME3;

        // 檢查是否到了匹配時間，且今天還沒有匹配過
        if (ISCHECKTIME && lastMatchTime.Date < cTime.Date)
        {
            Core.Instance.Loggers.Log($"Daily matching triggered at {cTime:yyyy-MM-dd HH:mm} (time code: {currentTime})", LoggingLevel.Trading);

            // 執行每日匹配
            PerformDailyMatching();

            // 更新最後匹配時間
            lastMatchTime = cTime;
        }
    }

    private void PerformDailyMatching()
    {
        // 清理舊記錄（超過36小時的）
        CleanOldRecords();

        // 檢查並調整參數以確保有足夠的頂底
        CheckAndAdjustParameters();

        // 如果調整後仍然不夠，進行額外檢測
        if (EnableDynamicAdjustment && (recentTops.Count < MinTopsRequired || recentBottoms.Count < MinBottomsRequired))
        {
            Core.Instance.Loggers.Log($"Insufficient tops/bottoms after adjustment. Performing additional detection with relaxed parameters.", LoggingLevel.Trading);
            PerformAdditionalDetection();
        }

        // 強制重新計算最佳配對
        FindBestPairing();

        Core.Instance.Loggers.Log($"Daily matching completed. Available records from 1.5 days: {recentTops.Count} tops, {recentBottoms.Count} bottoms (Required: {MinTopsRequired}/{MinBottomsRequired})", LoggingLevel.Trading);

        if (hasValidPair)
        {
            string topType = bestTop.AssociatedBox.IsLargeBox ? "Large" : "Small";
            string bottomType = bestBottom.AssociatedBox.IsLargeBox ? "Large" : "Small";
            DateTime topTime = bestTop.Time;
            DateTime bottomTime = bestBottom.Time;
            Core.Instance.Loggers.Log($"Daily best pairing: Top({topType}, {topTime:MM-dd HH:mm}) + Bottom({bottomType}, {bottomTime:MM-dd HH:mm})", LoggingLevel.Trading);
        }
        else
        {
            Core.Instance.Loggers.Log("No valid pairing found in daily matching", LoggingLevel.Trading);
        }
    }

    private void PerformAdditionalDetection()
    {
        // 使用更寬鬆的參數進行額外檢測
        int originalPeriod = currentPeriod;
        int originalLookbackIndex = currentLookbackIndex;

        // 臨時降低門檻
        currentPeriod = Math.Max(2, currentPeriod - 1);
        currentLookbackIndex = Math.Min(lookbackLevels.Length - 1, currentLookbackIndex + 1);

        Core.Instance.Loggers.Log($"Additional detection with relaxed parameters: period={currentPeriod}, lookback_level={currentLookbackIndex}", LoggingLevel.Trading);

        // 擴大檢測範圍
        int[] extendedLookbacks = GetDynamicLookbacks();

        foreach (int lookback in extendedLookbacks)
        {
            if (Count < lookback + 10) continue;

            // 檢測頂部
            if (recentTops.Count < MinTopsRequired && IsConfirmedTop(lookback, currentPeriod))
            {
                var associatedBox = FindBoxAtTime(lookback);
                if (associatedBox != null)
                {
                    var topRecord = new TopBottomRecord
                    {
                        Time = Time(lookback),
                        Price = High(lookback),
                        AssociatedBox = associatedBox,
                        IsTop = true,
                        ConfirmationOffset = lookback
                    };

                    if (!recentTops.Any(t => Math.Abs((t.Time - topRecord.Time).TotalMinutes) < 60)) // 1小時間隔
                    {
                        recentTops.Add(topRecord);
                        Core.Instance.Loggers.Log($"Additional Top detected: {associatedBox.IsLargeBox} Box at {topRecord.Time:HH:mm}", LoggingLevel.Trading);
                    }
                }
            }

            // 檢測底部
            if (recentBottoms.Count < MinBottomsRequired && IsConfirmedBottom(lookback, currentPeriod))
            {
                var associatedBox = FindBoxAtTime(lookback);
                if (associatedBox != null)
                {
                    var bottomRecord = new TopBottomRecord
                    {
                        Time = Time(lookback),
                        Price = Low(lookback),
                        AssociatedBox = associatedBox,
                        IsTop = false,
                        ConfirmationOffset = lookback
                    };

                    if (!recentBottoms.Any(b => Math.Abs((b.Time - bottomRecord.Time).TotalMinutes) < 60)) // 1小時間隔
                    {
                        recentBottoms.Add(bottomRecord);
                        Core.Instance.Loggers.Log($"Additional Bottom detected: {associatedBox.IsLargeBox} Box at {bottomRecord.Time:HH:mm}", LoggingLevel.Trading);
                    }
                }
            }

            // 如果已經達到最低要求，停止檢測
            if (recentTops.Count >= MinTopsRequired && recentBottoms.Count >= MinBottomsRequired)
            {
                break;
            }
        }

        // 恢復原始參數
        currentPeriod = originalPeriod;
        currentLookbackIndex = originalLookbackIndex;

        Core.Instance.Loggers.Log($"Additional detection completed. Final counts: {recentTops.Count} tops, {recentBottoms.Count} bottoms", LoggingLevel.Trading);
    }

    private void CleanOldRecords()
    {
        DateTime cutoffTime = Time(0).AddHours(-36); // 保留一天半（36小時）的記錄

        // 清理超過36小時的頂部記錄
        recentTops.RemoveAll(t => t.Time < cutoffTime);

        // 清理超過36小時的底部記錄
        recentBottoms.RemoveAll(b => b.Time < cutoffTime);

        // 不限制數量，只按時間清理
        Core.Instance.Loggers.Log($"Cleaned old records (1.5 days). Remaining: {recentTops.Count} tops, {recentBottoms.Count} bottoms", LoggingLevel.Trading);
    }

    private Box FindBoxAtTime(int offset)
    {
        DateTime targetTime = Time(offset);

        // 找到時間最接近的 Box（擴大搜索範圍）
        var candidateBoxes = listboxes
            .Where(b => Math.Abs((b.CurrentTime - targetTime).TotalMinutes) <= 60) // 1小時內的Box
            .OrderBy(b => Math.Abs((b.CurrentTime - targetTime).TotalMinutes))
            .ToList();

        var selectedBox = candidateBoxes.FirstOrDefault();

        if (selectedBox != null)
        {
            string boxType = selectedBox.IsLargeBox ? "Large" : "Small";
            Core.Instance.Loggers.Log($"Found {boxType} Box for time {targetTime:HH:mm}, Box time: {selectedBox.CurrentTime:HH:mm}, BarCount: {selectedBox.BarCount}", LoggingLevel.Trading);
        }
        else
        {
            Core.Instance.Loggers.Log($"No Box found for time {targetTime:HH:mm}, Total boxes: {listboxes.Count}", LoggingLevel.Trading);
        }

        return selectedBox;
    }

    private void FindBestPairing()
    {
        hasValidPair = false;
        bestTop = null;
        bestBottom = null;

        if (recentTops.Count == 0 || recentBottoms.Count == 0)
        {
            Core.Instance.Loggers.Log($"Insufficient records for pairing: tops={recentTops.Count}, bottoms={recentBottoms.Count}", LoggingLevel.Trading);
            return;
        }

        double bestRange = 0;
        TopBottomRecord bestTopRecord = null;
        TopBottomRecord bestBottomRecord = null;

        // Try all combinations of recent tops and bottoms
        foreach (var topRecord in recentTops)
        {
            foreach (var bottomRecord in recentBottoms)
            {
                var topBox = topRecord.AssociatedBox;
                var bottomBox = bottomRecord.AssociatedBox;

                // Check pairing rules: 大配小, 小配大, 大配大 (允許大BOX配大BOX)
                bool validPair = (topBox.IsLargeBox && bottomBox.IsSmallBox) ||
                                (topBox.IsSmallBox && bottomBox.IsLargeBox) ||
                                (topBox.IsLargeBox && bottomBox.IsLargeBox);

                if (validPair)
                {
                    // Calculate range for this pairing
                    double boxTop = Math.Max(topBox.MaxOC, bottomBox.MaxOC);
                    double boxBot = Math.Min(topBox.MinOC, bottomBox.MinOC);
                    double range = boxTop - boxBot;

                    // Keep the pairing with the largest range
                    if (range > bestRange)
                    {
                        bestRange = range;
                        bestTopRecord = topRecord;
                        bestBottomRecord = bottomRecord;
                    }
                }
            }
        }

        if (bestTopRecord != null && bestBottomRecord != null)
        {
            hasValidPair = true;
            bestTop = bestTopRecord;
            bestBottom = bestBottomRecord;

            string topType = bestTopRecord.AssociatedBox.IsLargeBox ? "Large" : "Small";
            string bottomType = bestBottomRecord.AssociatedBox.IsLargeBox ? "Large" : "Small";
            string pairType = (bestTopRecord.AssociatedBox.IsLargeBox && bestBottomRecord.AssociatedBox.IsLargeBox) ? "Large-Large" :
                             (bestTopRecord.AssociatedBox.IsSmallBox && bestBottomRecord.AssociatedBox.IsSmallBox) ? "Small-Small" : "Large-Small";
            Core.Instance.Loggers.Log($"Best pairing ({pairType}): Top({topType}, {bestTopRecord.AssociatedBox.BarCount} bars) + Bottom({bottomType}, {bestBottomRecord.AssociatedBox.BarCount} bars), Range: {bestRange:F4}", LoggingLevel.Trading);
        }
        else
        {
            Core.Instance.Loggers.Log($"No valid pairings found among {recentTops.Count} tops and {recentBottoms.Count} bottoms", LoggingLevel.Trading);
        }
    }

    private bool IsPivotHigh(int lookback)
    {
        if (Count < lookback * 2 + 1)
            return false;

        double currentHigh = Math.Max(Open(lookback), Close(lookback));

        for (int i = 1; i <= lookback; i++)
        {
            if (currentHigh <= Math.Max(Open(lookback - i), Close(lookback - i)) ||
                currentHigh <= Math.Max(Open(lookback + i), Close(lookback + i)))
                return false;
        }
        return true;
    }

    private bool IsPivotLow(int lookback)
    {
        if (Count < lookback * 2 + 1)
            return false;

        double currentLow = Math.Min(Open(lookback), Close(lookback));

        for (int i = 1; i <= lookback; i++)
        {
            if (currentLow >= Math.Min(Open(lookback - i), Close(lookback - i)) ||
                currentLow >= Math.Min(Open(lookback + i), Close(lookback + i)))
                return false;
        }
        return true;
    }

    private bool IsInsideBar(int currentbarp, int previusbarp)
    {
        int currentbar = currentbarp + 1;
        int previusbar = previusbarp + 1;
        double hp = Math.Max(Open(previusbar), Close(previusbar));
        double lp = Math.Min(Open(previusbar), Close(previusbar));
        double boxrange = hp - lp;
        double fibo2_hp = lp + boxrange * goldenRatio;
        double fibo2_lp = hp - boxrange * goldenRatio;
        bool isIB = (Close(currentbar) <= fibo2_hp && Close(currentbar) >= fibo2_lp) &&
                    (Open(currentbar) <= fibo2_hp && Open(currentbar) >= fibo2_lp);
        return isIB;
    }

    private double HHVInsideBar(PriceType priceType, int startOffset, int count)
    {
        int maxValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.insidebarSourceHD.GetPrice(priceType, maxValueOffset) < this.insidebarSourceHD.GetPrice(priceType, startOffset + i))
                maxValueOffset = startOffset + i;
        }
        return this.insidebarSourceHD.GetPrice(priceType, maxValueOffset);
    }

    private double LLVInsideBar(PriceType priceType, int startOffset, int count)
    {
        int minValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.insidebarSourceHD.GetPrice(priceType, minValueOffset) > this.insidebarSourceHD.GetPrice(priceType, startOffset + i))
                minValueOffset = startOffset + i;
        }
        return this.insidebarSourceHD.GetPrice(priceType, minValueOffset);
    }

    protected override void OnClear()
    {
        listboxes?.Clear();
        recentTops?.Clear();
        recentBottoms?.Clear();
        this.insidebarSourceHD?.Dispose();
    }

    public override void OnPaintChart(PaintChartEventArgs args)
    {
        Graphics gr = args.Graphics;

        // Draw all boxes with different colors based on size
        if (listboxes.Count > 0)
        {
            foreach (var box in listboxes)
            {
                // Choose color based on box size
                Color boxColor = box.IsLargeBox ? LargeBoxColor : SmallBoxColor;

                using (Pen boxPen = new Pen(boxColor, 2))
                {
                    var Y1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(box.PreviusHigh);
                    var Y2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(box.PreviusLow);
                    var iX1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(box.PreviusTime);
                    var iX2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(box.CurrentTime);

                    Rectangle rectangle = new Rectangle(iX1, Math.Min(Y1, Y2), Math.Abs(iX2 - iX1), Math.Abs(Y1 - Y2));
                    gr.DrawRectangle(boxPen, rectangle);
                }
            }
        }

        // Draw confirmed tops
        if (recentTops.Count > 0)
        {
            using (Brush topBrush = new SolidBrush(TopMarkerColor))
            {
                foreach (var topRecord in recentTops)
                {
                    var Y = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(topRecord.Price);
                    var X = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(topRecord.Time);

                    // Draw triangle pointing down for tops
                    Point[] topTriangle = {
                        new Point(X, Y - 8),
                        new Point(X - 6, Y + 2),
                        new Point(X + 6, Y + 2)
                    };
                    gr.FillPolygon(topBrush, topTriangle);
                }
            }
        }

        // Draw confirmed bottoms
        if (recentBottoms.Count > 0)
        {
            using (Brush bottomBrush = new SolidBrush(BottomMarkerColor))
            {
                foreach (var bottomRecord in recentBottoms)
                {
                    var Y = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(bottomRecord.Price);
                    var X = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(bottomRecord.Time);

                    // Draw triangle pointing up for bottoms
                    Point[] bottomTriangle = {
                        new Point(X, Y + 8),
                        new Point(X - 6, Y - 2),
                        new Point(X + 6, Y - 2)
                    };
                    gr.FillPolygon(bottomBrush, bottomTriangle);
                }
            }
        }

        // Highlight best pairing if available
        if (hasValidPair && bestTop != null && bestBottom != null)
        {
            var bestTopBox = bestTop.AssociatedBox;
            var bestBottomBox = bestBottom.AssociatedBox;

            // Highlight best top box
            using (Pen highlightPen = new Pen(BestPairColor, 4))
            {
                var Y1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(bestTopBox.PreviusHigh);
                var Y2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(bestTopBox.PreviusLow);
                var iX1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(bestTopBox.PreviusTime);
                var iX2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(bestTopBox.CurrentTime);

                Rectangle rectangle = new Rectangle(iX1, Math.Min(Y1, Y2), Math.Abs(iX2 - iX1), Math.Abs(Y1 - Y2));
                gr.DrawRectangle(highlightPen, rectangle);
            }

            // Highlight best bottom box
            using (Pen highlightPen = new Pen(BestPairColor, 4))
            {
                var Y1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(bestBottomBox.PreviusHigh);
                var Y2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(bestBottomBox.PreviusLow);
                var iX1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(bestBottomBox.PreviusTime);
                var iX2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(bestBottomBox.CurrentTime);

                Rectangle rectangle = new Rectangle(iX1, Math.Min(Y1, Y2), Math.Abs(iX2 - iX1), Math.Abs(Y1 - Y2));
                gr.DrawRectangle(highlightPen, rectangle);
            }
        }
    }



    public class Box
    {
        public DateTime CurrentTime { get; set; }
        public DateTime PreviusTime { get; set; }
        public Double PreviusHigh { get; set; }
        public Double PreviusLow { get; set; }
        public int BarCount { get; set; }
        public bool IsLargeBox => BarCount >= 15;
        public bool IsSmallBox => !IsLargeBox;  // 非大BOX就是小BOX
        public double MaxOC => PreviusHigh;  // 這裡應該是 max(open, close)
        public double MinOC => PreviusLow;   // 這裡應該是 min(open, close)
    }

    public class TopBottomRecord
    {
        public DateTime Time { get; set; }          // 頂部/底部確立的時間
        public double Price { get; set; }           // 頂部/底部的價格
        public Box AssociatedBox { get; set; }      // 當時對應的 Box
        public bool IsTop { get; set; }             // true=頂部, false=底部
        public int ConfirmationOffset { get; set; } // 確認時的回看偏移量
    }

    public class BuySellMarkRecord
    {
        public DateTime Time { get; set; }          // 買賣標記的時間
        public double Price { get; set; }           // 買賣標記的價格
        public Box AssociatedBox { get; set; }      // 最接近的 Box
        public bool IsBuyMark { get; set; }         // 是否為買入標記（false 為賣出標記）
        public int ConfirmationOffset { get; set; } // 確認時的 offset
    }

    // 公開方法供策略使用
    public Box GetCurrentTopBox()
    {
        return bestTop?.AssociatedBox;
    }

    public Box GetCurrentBottomBox()
    {
        return bestBottom?.AssociatedBox;
    }

    public bool HasValidPairing()
    {
        return hasValidPair;
    }

    // Color parameters for visual display
    [InputParameter("Large Box Color", 1)]
    public Color LargeBoxColor = Color.Orange;

    [InputParameter("Small Box Color", 2)]
    public Color SmallBoxColor = Color.LightBlue;

    [InputParameter("Top Marker Color", 3)]
    public Color TopMarkerColor = Color.Red;

    [InputParameter("Bottom Marker Color", 4)]
    public Color BottomMarkerColor = Color.Green;

    [InputParameter("Best Pair Highlight Color", 5)]
    public Color BestPairColor = Color.Yellow;

    #endregion Base overrides
}