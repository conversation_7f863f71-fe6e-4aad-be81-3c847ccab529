// Copyright QUANTOWER LLC. © 2017-2023. All rights reserved.

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using TradingPlatform.BusinessLayer;
using TradingPlatform.BusinessLayer.Utils;

namespace MovingAverageIndicators;

public class DoubleSMAIndicator : Indicator
{
    #region Parameters
    private HistoricalDataCustom insidebarSourceHD;
    private const PriceType BOXTOPP2_EMA_SOURCE_TYPE = PriceType.Close;
    private const PriceType BOXBOTP2_EMA_SOURCE_TYPE = PriceType.Close;
    private const PriceType ROCMA_SMA_SOURCE_TYPE = PriceType.Close;

    private const PriceType MAX_OPEN_CLOSE_SOURCE_TYPE = PriceType.High;
    private const PriceType MIN_OPEN_CLOSE_SOURCE_TYPE = PriceType.Low;
    //private HistoricalData H3data;

    private HistoricalDataCustom ocSourceHD;


    private Indicator boxtopp2Ema;
    private Indicator boxbotp2Ema;
    private Indicator rocmaSma;
    private HistoricalDataCustom boxtopp2SourceHD;
    private HistoricalDataCustom boxbotp2SourceHD;
    private HistoricalDataCustom rocmaSourceHD;
    private Indicator roc;
    private double BOT_FIBO_TOP_BY_SHOCK;
    private double BOT_FIBO_BOT_BY_SHOCK;
    private double BOT_FIBO_TOPTP_BY_SHOCK;
    private double BOT_FIBO_BOTTP_BY_SHOCK;

    private double BOT_FIBO_TOP_BY_SHOCK2;
    private double BOT_FIBO_BOT_BY_SHOCK2;
    private double BOT_FIBO_TOPTP_BY_SHOCK2;
    private double BOT_FIBO_BOTTP_BY_SHOCK2;

    private double BOT_FIBO_TOP_BY_SHOCK0;
    private double BOT_FIBO_BOT_BY_SHOCK0;

    private double BOT_FIBO_TOP_BY_SHOCK02;
    private double BOT_FIBO_BOT_BY_SHOCK02;

    private const PriceType BOT_FIBO_TOP_SOURCE_TYPE = PriceType.High;
    private const PriceType BOT_FIBO_TOPTP_SOURCE_TYPE = PriceType.Close;
    private const PriceType BOT_FIBO_BOT_SOURCE_TYPE = PriceType.Low;
    private const PriceType BOT_FIBO_BOTTP_SOURCE_TYPE = PriceType.Open;
    private HistoricalDataCustom fiboSourceHD;

    private HistoricalDataCustom fiboSourceHD2;


    private Indicator rsi;
    private HistoricalDataCustom rsimaSourceHD;

    private bool ISPIVOT_HIGH;
    private bool ISPIVOT_LOW;
    private double LASTFIBOH;
    private double LASTFIBOL;
    private int lastUpdate;

    private int barIndex = 1;
    private List<Box> listboxes = null;


    private double goldenRatio = ((1 + Math.Sqrt(5)) / 2);
    private double goldenFraction = 1 / ((1 + Math.Sqrt(5)) / 2); // 0.618033988749895
    private double goldenFractionComplement = 1 - 1 / ((1 + Math.Sqrt(5)) / 2); // 0.381966011250105
    private double goldenRatioS = 1 - 1 / ((1 + Math.Sqrt(5)) / 2) + 1;


    #endregion Paramaeters

    public DoubleSMAIndicator()
    {
        this.Name = "Inside Bar Channel indicator2";



        this.AddLineSeries("TOPLINE", Color.Red, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE", Color.Green, 1, LineStyle.Solid);
        this.AddLineSeries("TOPLINE2", Color.Yellow, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE2", Color.Yellow, 1, LineStyle.Solid);

        this.AddLineSeries("TOPLINE3", Color.Purple, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE3", Color.Blue, 1, LineStyle.Solid);
        this.AddLineSeries("TOPLINE4", Color.Orange, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE4", Color.Orange, 1, LineStyle.Solid);

        this.AddLineSeries("TOPLINE0", Color.Gray, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE0", Color.Gray, 1, LineStyle.Solid);
        this.AddLineSeries("TOPLINE02", Color.Gray, 1, LineStyle.Solid);
        this.AddLineSeries("BOTLINE02", Color.Gray, 1, LineStyle.Solid);
        this.SeparateWindow = false;


    }

    #region Base overrides

    protected override void OnInit()
    {
        this.insidebarSourceHD = new HistoricalDataCustom(this);

        this.boxtopp2SourceHD = new HistoricalDataCustom(this);
        this.boxtopp2Ema = Core.Instance.Indicators.BuiltIn.EMA(60, BOXTOPP2_EMA_SOURCE_TYPE);
        this.boxtopp2SourceHD.AddIndicator(this.boxtopp2Ema);

        this.boxbotp2SourceHD = new HistoricalDataCustom(this);
        this.boxbotp2Ema = Core.Instance.Indicators.BuiltIn.EMA(60, BOXBOTP2_EMA_SOURCE_TYPE);
        this.boxbotp2SourceHD.AddIndicator(this.boxbotp2Ema);


        this.roc = Core.Instance.Indicators.BuiltIn.ROC(14);
        this.rsi = Core.Instance.Indicators.BuiltIn.RSI(14, PriceType.Close, RSIMode.Simple, MaMode.SMA, 2, IndicatorCalculationType.AllAvailableData);

        this.rocmaSourceHD = new HistoricalDataCustom(this);
        this.rocmaSma = Core.Instance.Indicators.BuiltIn.SMA(9, ROCMA_SMA_SOURCE_TYPE);
        this.rocmaSourceHD.AddIndicator(this.rocmaSma);

        this.rsimaSourceHD = new HistoricalDataCustom(this);
        this.rsimaSourceHD.AddIndicator(this.rsi);

        this.ocSourceHD = new HistoricalDataCustom(this);
        this.fiboSourceHD = new HistoricalDataCustom(this);
        this.fiboSourceHD2 = new HistoricalDataCustom(this);

        //this.H3data = this.Symbol.GetHistory(Period.HOUR3,  this.Symbol.HistoryType, Core.TimeUtils.DateTimeUtcNow.AddDays(-100));


        this.listboxes = new List<Box>();


    }
    protected override void OnUpdate(UpdateArgs args)
    {
        if (300 >= this.Count)
            return;

        if (isInsideBar(1, barIndex + 1))
        {
            barIndex++;
        }
        double ROCMA = this.rocmaSma.GetValue();
        double RSI = this.rsi.GetValue();


        bool IS_SHOCK = (ABS(ROCMA) < 0.2 || (RSI > 48 && RSI < 52));//&& isInsideBar(1, barIndex) && !isInsideBar(0, barIndex)  ;

        bool isSet = false;
        if (isInsideBar(1, barIndex) && !isInsideBar(0, barIndex))
        {

            Double PreviusHigh = Math.Max(Open(barIndex), Close(barIndex));
            Double PreviusLow = Math.Min(Open(barIndex), Close(barIndex));

            //Log("Box Found", LoggingLevel.System);


            if (barIndex > 1)
            {
                Box box = new Box();
                box.PreviusHigh = PreviusHigh;
                box.PreviusLow = PreviusLow;

                listboxes.Add(box);

                barIndex = 1;


                this.insidebarSourceHD[PriceType.High] = PreviusHigh;
                this.insidebarSourceHD[PriceType.Low] = PreviusLow;

                IS_SHOCK = true;
                isSet = true;
            }



        }
        if (!isSet)
        {

            this.insidebarSourceHD[PriceType.High] = this.insidebarSourceHD.GetPrice(PriceType.High, 1);
            this.insidebarSourceHD[PriceType.Low] = this.insidebarSourceHD.GetPrice(PriceType.Low, 1);


        }
        //this.SetValue(this.HHVInsideBar(PriceType.High, 1, 200), 0);
        //this.SetValue(this.LLVInsideBar(PriceType.Low, 1, 200), 1);
        double InsideBarRange = Math.Abs(this.HHVInsideBar(PriceType.High, 1, 90) - this.LLVInsideBar(PriceType.Low, 1, 90));
        //this.SetValue(this.LLVInsideBar(PriceType.Low, 1, 200) + InsideBarRange * goldenRatio, 2);
        //this.SetValue(this.HHVInsideBar(PriceType.High, 1, 200) - InsideBarRange * goldenRatio, 3);

        double h = this.GetPrice(PriceType.High, 0);
        double l = this.GetPrice(PriceType.Low, 0);
        double o = this.GetPrice(PriceType.Open, 0);
        double c = this.GetPrice(PriceType.Close, 0);
        double H = h;
        double L = l;
        double O = o;
        double C = c;

        this.ocSourceHD[MAX_OPEN_CLOSE_SOURCE_TYPE] = this.HHVInsideBar(PriceType.High, 1, 90);
        this.ocSourceHD[MIN_OPEN_CLOSE_SOURCE_TYPE] = this.LLVInsideBar(PriceType.Low, 1, 90);

        double MOC = (SUM(PriceType.Close, 9) / 9 < SUM(PriceType.Close, 300) / 300 ? LLV(MAX_OPEN_CLOSE_SOURCE_TYPE, 0, 9) : HHV(MAX_OPEN_CLOSE_SOURCE_TYPE, 0, 9));
        double MNOC = (SUM(PriceType.Close, 9) / 9 < SUM(PriceType.Close, 300) / 300 ? HHV(MIN_OPEN_CLOSE_SOURCE_TYPE, 0, 9) : LLV(MIN_OPEN_CLOSE_SOURCE_TYPE, 0, 9));

        double BOX_TOPP = MOC;

        double BOX_BOTP = MNOC;
        double BOX_TOPP2 = MAX(BOX_TOPP, BOX_BOTP);

        double BOX_BOTP2 = MIN(BOX_TOPP, BOX_BOTP);



        this.boxtopp2SourceHD[BOXTOPP2_EMA_SOURCE_TYPE] = BOX_TOPP2;
        // 移动平均蜡烛实体
        double BOX_TOP = this.boxtopp2Ema.GetValue();


        this.boxbotp2SourceHD[BOXBOTP2_EMA_SOURCE_TYPE] = BOX_BOTP2;
        double BOX_BOT = this.boxbotp2Ema.GetValue();

        double BOX_SIZE = InsideBarRange;
        double BOT_FIBO_TOP0 = BOX_BOT + BOX_SIZE;
        double BOT_FIBO_BOT0 = BOX_TOP - BOX_SIZE;

        double BOT_FIBO_TOP = BOX_BOT + BOX_SIZE * goldenRatio;
        double BOT_FIBO_BOT = BOX_TOP - BOX_SIZE * goldenRatio;
        double BOT_FIBO_TOPTP = BOX_BOT + BOX_SIZE / goldenFractionComplement;
        double BOT_FIBO_BOTTP = BOX_TOP - BOX_SIZE / goldenFractionComplement;

        double price = this.Close();

        // Get close price by offset.
        double priceN = this.Close(12);

        double roc = 100 * (price - priceN) / priceN;

        double ROC = roc;

        this.rocmaSourceHD[ROCMA_SMA_SOURCE_TYPE] = ROC;



        this.ISPIVOT_HIGH = IsPivotHigh(60);
        this.ISPIVOT_LOW = IsPivotLow(60);

        if (this.ISPIVOT_HIGH)
        {
            this.LASTFIBOH = Math.Max(O, C);
            lastUpdate = 0;
        }

        if (this.ISPIVOT_LOW)
        {
            this.LASTFIBOL = Math.Min(O, C);
            lastUpdate = 0;
        }

        lastUpdate++;

        this.fiboSourceHD[BOT_FIBO_TOP_SOURCE_TYPE] = BOT_FIBO_TOP;
        this.fiboSourceHD[BOT_FIBO_BOT_SOURCE_TYPE] = BOT_FIBO_BOT;
        this.fiboSourceHD[BOT_FIBO_TOPTP_SOURCE_TYPE] = BOT_FIBO_TOPTP;
        this.fiboSourceHD[BOT_FIBO_BOTTP_SOURCE_TYPE] = BOT_FIBO_BOTTP;

        this.fiboSourceHD2[BOT_FIBO_TOP_SOURCE_TYPE] = BOT_FIBO_TOP0;
        this.fiboSourceHD2[BOT_FIBO_BOT_SOURCE_TYPE] = BOT_FIBO_BOT0;


        if (IS_SHOCK)
        {
            this.BOT_FIBO_TOP_BY_SHOCK = this.fiboSourceHD.GetPrice(BOT_FIBO_TOP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOT_BY_SHOCK = this.fiboSourceHD.GetPrice(BOT_FIBO_BOT_SOURCE_TYPE, 4);
            this.BOT_FIBO_TOPTP_BY_SHOCK = this.fiboSourceHD.GetPrice(BOT_FIBO_TOPTP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOTTP_BY_SHOCK = this.fiboSourceHD.GetPrice(BOT_FIBO_BOTTP_SOURCE_TYPE, 4);

            this.BOT_FIBO_TOP_BY_SHOCK0 = this.fiboSourceHD2.GetPrice(BOT_FIBO_TOP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOT_BY_SHOCK0 = this.fiboSourceHD2.GetPrice(BOT_FIBO_BOT_SOURCE_TYPE, 4);

        }


        this.SetValue(this.BOT_FIBO_TOP_BY_SHOCK, 0);
        this.SetValue(this.BOT_FIBO_BOT_BY_SHOCK, 1);
        this.SetValue(this.BOT_FIBO_TOPTP_BY_SHOCK, 2);
        this.SetValue(this.BOT_FIBO_BOTTP_BY_SHOCK, 3);
        this.SetValue(this.BOT_FIBO_TOP_BY_SHOCK0, 8);
        this.SetValue(this.BOT_FIBO_BOT_BY_SHOCK0, 9);

        if (IS_SHOCK && (lastUpdate < 60 || lastUpdate >= 60 && (MAX(O, C) >= this.LASTFIBOL && MAX(O, C) <= this.LASTFIBOH) && (MIN(O, C) >= this.LASTFIBOL && MIN(O, C) <= this.LASTFIBOH)))
        {
            this.BOT_FIBO_TOP_BY_SHOCK2 = this.fiboSourceHD.GetPrice(BOT_FIBO_TOP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOT_BY_SHOCK2 = this.fiboSourceHD.GetPrice(BOT_FIBO_BOT_SOURCE_TYPE, 4);
            this.BOT_FIBO_TOPTP_BY_SHOCK2 = this.fiboSourceHD.GetPrice(BOT_FIBO_TOPTP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOTTP_BY_SHOCK2 = this.fiboSourceHD.GetPrice(BOT_FIBO_BOTTP_SOURCE_TYPE, 4);

            this.BOT_FIBO_TOP_BY_SHOCK02 = this.fiboSourceHD2.GetPrice(BOT_FIBO_TOP_SOURCE_TYPE, 4);
            this.BOT_FIBO_BOT_BY_SHOCK02 = this.fiboSourceHD2.GetPrice(BOT_FIBO_BOT_SOURCE_TYPE, 4);

        }

        this.SetValue(this.BOT_FIBO_TOP_BY_SHOCK2, 4);
        this.SetValue(this.BOT_FIBO_BOT_BY_SHOCK2, 5);
        this.SetValue(this.BOT_FIBO_TOPTP_BY_SHOCK2, 6);
        this.SetValue(this.BOT_FIBO_BOTTP_BY_SHOCK2, 7);
        this.SetValue(this.BOT_FIBO_TOP_BY_SHOCK02, 10);
        this.SetValue(this.BOT_FIBO_BOT_BY_SHOCK02, 11);




    }
    private double HHVInsideBar(PriceType priceType, int startOffset, int count)
    {
        int maxValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.insidebarSourceHD.GetPrice(priceType, maxValueOffset) < this.insidebarSourceHD.GetPrice(priceType, startOffset + i))
                maxValueOffset = startOffset + i;
        }
        return this.insidebarSourceHD.GetPrice(priceType, maxValueOffset);
    }

    private double LLVInsideBar(PriceType priceType, int startOffset, int count)
    {
        int minValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.insidebarSourceHD.GetPrice(priceType, minValueOffset) > this.insidebarSourceHD.GetPrice(priceType, startOffset + i))
                minValueOffset = startOffset + i;
        }
        return this.insidebarSourceHD.GetPrice(priceType, minValueOffset);
    }
    private bool IsPivotHigh(int index)
    {
        if (index < 60)
            return false;

        double currentHigh = Math.Max(this.GetPrice(PriceType.Close, index), this.GetPrice(PriceType.Open, index));
        for (int i = 1; i <= 30; i++)
        {
            if (currentHigh <= Math.Max(this.GetPrice(PriceType.Close, index - i), this.GetPrice(PriceType.Open, index - i)) ||
                currentHigh <= Math.Max(this.GetPrice(PriceType.Close, index + i), this.GetPrice(PriceType.Open, index + i)))
                return false;
        }
        return true;
    }

    private bool IsPivotLow(int index)
    {
        if (index < 60)
            return false;

        double currentLow = Math.Min(this.GetPrice(PriceType.Close, index), this.GetPrice(PriceType.Open, index));
        for (int i = 1; i <= 30; i++)
        {
            if (currentLow >= Math.Min(this.GetPrice(PriceType.Close, index - i), this.GetPrice(PriceType.Open, index - i)) ||
                currentLow >= Math.Min(this.GetPrice(PriceType.Close, index + i), this.GetPrice(PriceType.Open, index + i)))
                return false;
        }
        return true;
    }
    private double ABS(double v1)
    {
        return Math.Abs(v1);
    }

    private double SUM(PriceType priceType, int count)
    {
        double maxValue = 0;
        for (int i = 0; i < count; i++)
        {
            maxValue += this.GetPrice(priceType, i);
        }
        return maxValue;
    }

    private double MAX(double v1, double v2)
    {

        return Math.Max(v1, v2);
    }
    private double MIN(double v1, double v2)
    {

        return Math.Min(v1, v2);
    }

    private double HHV(PriceType priceType, int startOffset, int count)
    {
        int maxValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.ocSourceHD.GetPrice(priceType, maxValueOffset) < this.ocSourceHD.GetPrice(priceType, startOffset + i))
                maxValueOffset = startOffset + i;
        }
        return this.ocSourceHD.GetPrice(priceType, maxValueOffset);
    }

    private double LLV(PriceType priceType, int startOffset, int count)
    {
        int minValueOffset = startOffset;
        for (int i = 0; i < count; i++)
        {
            if (this.ocSourceHD.GetPrice(priceType, minValueOffset) > this.ocSourceHD.GetPrice(priceType, startOffset + i))
                minValueOffset = startOffset + i;
        }
        return this.ocSourceHD.GetPrice(priceType, minValueOffset);
    }
    protected override void OnClear()
    {

        this.boxtopp2Ema?.Dispose();
        this.boxtopp2SourceHD?.Dispose();
        this.boxbotp2Ema?.Dispose();
        this.boxbotp2SourceHD?.Dispose();
    }



    public class Box
    {

        public Double PreviusHigh { get; set; }
        public Double PreviusLow { get; set; }


    }

    private Boolean isInsideBar(int currentbarp, int previusbarp)
    {
        int currentbar = currentbarp;
        int previusbar = previusbarp;
        double hp = Math.Max(Open(previusbar), Close(previusbar));
        double lp = Math.Min(Open(previusbar), Close(previusbar));
        double boxrange = hp - lp;
        double fibo2_hp = lp + boxrange * goldenRatio;
        double fibo2_lp = hp - boxrange * goldenRatio;
        Boolean isIB = (Close(currentbar) <= fibo2_hp && Close(currentbar) >= fibo2_lp) && (Open(currentbar) <= fibo2_hp && Open(currentbar) >= fibo2_lp);
        return isIB;
    }

    #endregion Base overrides
}