// Copyright QUANTOWER LLC. © 2017-2023. All rights reserved.

using System;
using System.Collections.Generic;
using System.Drawing;
using TradingPlatform.BusinessLayer;

namespace TrendIndicators;

public class MarkBuySellLine : Indicator
{
    #region Constants
    // 價格線相關常量
    private const int PD_PERIOD = 180;
    private const int GOOD_PERIOD = 120;
    private const double FIBO1 = 1.382;
    private const double FIBO2 = 1.618;
    private const int TIME_CHECK1_START = 1200;
    private const int TIME_CHECK1_END = 1300;
    private const int TIME_CHECK2_START = 1500;
    private const int TIME_CHECK2_END = 1600;
    private const int TIME_CHECK3_START = 2000;
    private const int TIME_CHECK3_END = 2300;
    #endregion

    #region Historical Data Lists
    // 價格線相關數據
    private readonly List<double> pdHighs = new();
    private readonly List<double> pdLows = new();
    private readonly List<int> goodLowCounts = new();
    private readonly List<int> goodHighCounts = new();
    private readonly List<bool> isBuySafe = new();
    private readonly List<bool> isSellSafe = new();
    private readonly List<double> price1382Values = new();
    private readonly List<double> price1618Values = new();
    private readonly List<double> price13822Values = new();
    private readonly List<double> price16182Values = new();
    private readonly List<double> buyLine1Values = new();
    private readonly List<double> buyLine2Values = new();
    private readonly List<double> sellLine1Values = new();
    private readonly List<double> sellLine2Values = new();
    private readonly List<bool> isCheckTime = new();

    #endregion

    #region Parameters
    [InputParameter("Buy Line 1 Color", 10)]
    public Color BuyLine1Color { get; set; } = Color.Green;

    [InputParameter("Buy Line 2 Color", 20)]
    public Color BuyLine2Color { get; set; } = Color.Green;

    [InputParameter("Sell Line 1 Color", 30)]
    public Color SellLine1Color { get; set; } = Color.Red;

    [InputParameter("Sell Line 2 Color", 40)]
    public Color SellLine2Color { get; set; } = Color.Red;
    #endregion

    public MarkBuySellLine()
    {
        this.Name = "MarkBuySellLine";
        this.BuyLine1Color = Color.Green;
        this.BuyLine2Color = Color.Green;
        this.SellLine1Color = Color.Red;
        this.SellLine2Color = Color.Red;

        // 價格線
        this.AddLineSeries("BuyLine1", this.BuyLine1Color, 1, LineStyle.Solid);
        this.AddLineSeries("BuyLine2", this.BuyLine2Color, 1, LineStyle.Solid);
        this.AddLineSeries("SellLine1", this.SellLine1Color, 1, LineStyle.Solid);
        this.AddLineSeries("SellLine2", this.SellLine2Color, 1, LineStyle.Solid);
    }

    #region Helper Methods
    /// <summary>
    /// BARSLAST函数：返回条件最近一次为true的K线距离
    /// </summary>
    private int BarsLast(Func<int, bool> condition, int startIndex, int maxLookback)
    {
        for (int i = 0; i < maxLookback && startIndex - i >= 0; i++)
            if (condition(startIndex - i))
                return i;
        return maxLookback; // 如果没找到，返回最大回溯期
    }

    /// <summary>
    /// FINDHIGH函数：寻找周期内的最高值
    /// </summary>
    private double FindHigh(Func<int, double> valueSelector, int index, int period)
    {
        double highest = double.MinValue;
        for (int i = 0; i < period && index - i >= 0; i++)
            highest = Math.Max(highest, valueSelector(index - i));
        return highest;
    }

    /// <summary>
    /// FINDLOW函数：寻找周期内的最低值
    /// </summary>
    private double FindLow(Func<int, double> valueSelector, int index, int period)
    {
        double lowest = double.MaxValue;
        for (int i = 0; i < period && index - i >= 0; i++)
            lowest = Math.Min(lowest, valueSelector(index - i));
        return lowest;
    }
    #endregion

    protected override void OnUpdate(UpdateArgs args)
    {
        int currentIndex = this.Count - 1;
        if (currentIndex < PD_PERIOD) return;

        // 確保列表容量
        EnsureListCapacity(currentIndex);

        // 計算價格線
        CalculatePriceLines(currentIndex);

        // 處理價格線
        ProcessPriceLines(currentIndex);
    }

    private void EnsureListCapacity(int index)
    {
        while (pdHighs.Count <= index)
        {
            // 價格線相關數據
            pdHighs.Add(0);
            pdLows.Add(0);
            goodLowCounts.Add(0);
            goodHighCounts.Add(0);
            isBuySafe.Add(false);
            isSellSafe.Add(false);
            price1382Values.Add(0);
            price1618Values.Add(0);
            price13822Values.Add(0);
            price16182Values.Add(0);
            buyLine1Values.Add(0);
            buyLine2Values.Add(0);
            sellLine1Values.Add(0);
            sellLine2Values.Add(0);
            isCheckTime.Add(false);
        }
    }





    private void CalculatePriceLines(int index)
    {
        if (index < PD_PERIOD) return;
        
        // 限制掃描的K線數量，最多只查看300根K線
        const int MAX_SCAN_BARS = 300;

        // 按照偽代碼嚴格順序實現
        // PDH:= HHV(MAX(O,C),180){180日最高價}
        // PDL:= LLV(MIN(O,C),180){180日最低價}
        double PDH = FindHigh(i => Math.Max(Open(index - i), Close(index - i)), index, PD_PERIOD);
        double PDL = FindLow(i => Math.Min(Open(index - i), Close(index - i)), index, PD_PERIOD);
        
        pdHighs[index] = PDH;
        pdLows[index] = PDL;

        // 檢查時間是否在指定範圍內
        DateTime cTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(Time(0), Core.Instance.TimeUtils.SelectedTimeZone);
        int currentTime = cTime.Hour * 100 + cTime.Minute;
        bool ISCHECKTIME1 = currentTime >= TIME_CHECK1_START && currentTime <= TIME_CHECK1_END;
        bool ISCHECKTIME2 = currentTime >= TIME_CHECK2_START && currentTime <= TIME_CHECK2_END;
        bool ISCHECKTIME3 = currentTime >= TIME_CHECK3_START && currentTime <= TIME_CHECK3_END;
        bool ISCHECKTIME = ISCHECKTIME1 || ISCHECKTIME2 || ISCHECKTIME3;
        isCheckTime[index] = ISCHECKTIME;

        // GOODLOW:= BARSLASTCOUNT(PDL=REF(PDL,1)) {連續保持PDL=REF(PDL,1)的次數}
        // GOODHIGH := BARSLASTCOUNT(PDH=REF(PDH,1))  {連續保持PDH=REF(PDH,1)的次數}
        int GOODLOW = 0;
        int GOODHIGH = 0;

        if (index > 0)
        {
            // 計算PDL連續等於前一根K線PDL的次數 (PDL=REF(PDL,1))
            // 從當前K線開始向前計算，直到條件不滿足為止
            for (int i = 0; i <= index && index - i >= 1; i++)
            {
                if (pdLows[index - i] == pdLows[index - i - 1])
                    GOODLOW++;
                else
                    break;
            }

            // 計算PDH連續等於前一根K線PDH的次數 (PDH=REF(PDH,1))
            // 從當前K線開始向前計算，直到條件不滿足為止
            for (int i = 0; i <= index && index - i >= 1; i++)
            {
                if (pdHighs[index - i] == pdHighs[index - i - 1])
                    GOODHIGH++;
                else
                    break;
            }
        }
        goodLowCounts[index] = GOODLOW;
        goodHighCounts[index] = GOODHIGH;

        // ISGOODLOW := GOODLOW >= 120 
        // ISGOODHIGH := GOODHIGH >= 120  
        bool ISGOODLOW = GOODLOW >= GOOD_PERIOD;
        bool ISGOODHIGH = GOODHIGH >= GOOD_PERIOD;

        // HOWCLOSELOW:=BARSLAST(ISGOODLOW) {L被包圍多於120個時距今週期}
        // HOWCLOSEHIGH:=BARSLAST(ISGOODHIGH) {H被包圍多於120個時距今週期}
        int HOWCLOSELOW = BarsLast(i => goodLowCounts[i] >= GOOD_PERIOD, index, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
        int HOWCLOSEHIGH = BarsLast(i => goodHighCounts[i] >= GOOD_PERIOD, index, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

        // LASTGOODLOW := REF(PDL,HOWCLOSELOW)
        // LASTGOODHIGH := REF(PDH,HOWCLOSEHIGH)
        // LASTGOODLOW2 := REF(PDL,HOWCLOSEHIGH+1)
        // LASTGOODHIGH2 := REF(PDH,HOWCLOSELOW+1)
        double LASTGOODLOW = HOWCLOSELOW < PD_PERIOD ? pdLows[index - HOWCLOSELOW] : 0;
        double LASTGOODHIGH = HOWCLOSEHIGH < PD_PERIOD ? pdHighs[index - HOWCLOSEHIGH] : 0;
        double LASTGOODLOW2 = HOWCLOSEHIGH < PD_PERIOD && index - HOWCLOSEHIGH - 1 >= 0 ? pdLows[index - HOWCLOSEHIGH - 1] : 0;
        double LASTGOODHIGH2 = HOWCLOSELOW < PD_PERIOD && index - HOWCLOSELOW - 1 >= 0 ? pdHighs[index - HOWCLOSELOW - 1] : 0;

        // SELLLIQRANGE:= ABS(LASTGOODHIGH2 - LASTGOODLOW)
        // BUYLIQRANGE:= ABS(LASTGOODHIGH - LASTGOODLOW2)
        double SELLLIQRANGE = Math.Abs(LASTGOODHIGH2 - LASTGOODLOW);
        double BUYLIQRANGE = Math.Abs(LASTGOODHIGH - LASTGOODLOW2);

        // ISBUYSAFE := LASTGOODHIGH > LASTGOODLOW
        // ISSELLSAFE := LASTGOODHIGH > LASTGOODLOW
        bool ISBUYSAFE = LASTGOODHIGH > LASTGOODLOW;
        bool ISSELLSAFE = LASTGOODHIGH > LASTGOODLOW;

        // 存儲當前K線的ISBUYSAFE和ISSELLSAFE狀態
        isBuySafe[index] = ISBUYSAFE;
        isSellSafe[index] = ISSELLSAFE;

        // 計算並存儲當前K線的PRICE值
        // PRICE1382:=LASTGOODHIGH - BUYLIQRANGE * FIBO1
        // PRICE1618:=LASTGOODHIGH - BUYLIQRANGE * FIBO2
        // PRICE13822:=LASTGOODLOW + SELLLIQRANGE * FIBO1
        // PRICE16182:=LASTGOODLOW + SELLLIQRANGE * FIBO2
        double PRICE1382 = LASTGOODHIGH - BUYLIQRANGE * FIBO1;
        double PRICE1618 = LASTGOODHIGH - BUYLIQRANGE * FIBO2;
        double PRICE13822 = LASTGOODLOW + SELLLIQRANGE * FIBO1;
        double PRICE16182 = LASTGOODLOW + SELLLIQRANGE * FIBO2;

        // 存儲當前K線的PRICE值
        price1382Values[index] = PRICE1382;
        price1618Values[index] = PRICE1618;
        price13822Values[index] = PRICE13822;
        price16182Values[index] = PRICE16182;
        
        // 優化BARSLAST(ISBUYSAFE)的計算 - 按照txt文件的邏輯
        // 直接從當前向前查找最近一個滿足條件的K線
        int BARSLASTISBUYSAFE = BarsLast(i => {
            if (i < PD_PERIOD) return false;
            int tempHOWCLOSEHIGH = BarsLast(j => goodHighCounts[j] >= GOOD_PERIOD, i, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
            int tempHOWCLOSELOW = BarsLast(j => goodLowCounts[j] >= GOOD_PERIOD, i, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

            if (tempHOWCLOSEHIGH >= PD_PERIOD || tempHOWCLOSELOW >= PD_PERIOD) return false;

            double tempLASTGOODHIGH = pdHighs[i - tempHOWCLOSEHIGH];
            double tempLASTGOODLOW = pdLows[i - tempHOWCLOSELOW];

            return tempLASTGOODHIGH > tempLASTGOODLOW;
        }, index, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

        int BARSLASTISSELLSAFE = BarsLast(i => {
            if (i < PD_PERIOD) return false;
            int tempHOWCLOSEHIGH = BarsLast(j => goodHighCounts[j] >= GOOD_PERIOD, i, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
            int tempHOWCLOSELOW = BarsLast(j => goodLowCounts[j] >= GOOD_PERIOD, i, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

            if (tempHOWCLOSEHIGH >= PD_PERIOD || tempHOWCLOSELOW >= PD_PERIOD) return false;

            double tempLASTGOODHIGH = pdHighs[i - tempHOWCLOSEHIGH];
            double tempLASTGOODLOW = pdLows[i - tempHOWCLOSELOW];

            return tempLASTGOODHIGH > tempLASTGOODLOW;
        }, index, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
        
        // 如果在指定時間內計算價格線 - 按照txt文件的邏輯
        if (ISCHECKTIME)
        {
            if (BARSLASTISBUYSAFE < PD_PERIOD && index - BARSLASTISBUYSAFE >= 0)
            {
                int buySafeIndex = index - BARSLASTISBUYSAFE;

                // 針對那根K線重新計算所有值
                int safeHOWCLOSELOW = BarsLast(i => goodLowCounts[i] >= GOOD_PERIOD, buySafeIndex, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
                int safeHOWCLOSEHIGH = BarsLast(i => goodHighCounts[i] >= GOOD_PERIOD, buySafeIndex, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

                double safeLASTGOODLOW = safeHOWCLOSELOW < PD_PERIOD ? pdLows[buySafeIndex - safeHOWCLOSELOW] : 0;
                double safeLASTGOODHIGH = safeHOWCLOSEHIGH < PD_PERIOD ? pdHighs[buySafeIndex - safeHOWCLOSEHIGH] : 0;
                double safeLASTGOODLOW2 = safeHOWCLOSEHIGH < PD_PERIOD && buySafeIndex - safeHOWCLOSEHIGH - 1 >= 0
                    ? pdLows[buySafeIndex - safeHOWCLOSEHIGH - 1] : 0;

                double safeBUYLIQRANGE = Math.Abs(safeLASTGOODHIGH - safeLASTGOODLOW2);

                // BUYLINE1:= IF(ISCHECKTIME, REF(LASTGOODHIGH - BUYLIQRANGE * FIBO1, BARSLAST(ISBUYSAFE)), DRAWNULL)
                buyLine1Values[index] = safeLASTGOODHIGH - safeBUYLIQRANGE * FIBO1;
                // BUYLINE1B:= IF(ISCHECKTIME, REF(LASTGOODHIGH - BUYLIQRANGE * FIBO2, BARSLAST(ISBUYSAFE)), DRAWNULL)
                buyLine2Values[index] = safeLASTGOODHIGH - safeBUYLIQRANGE * FIBO2;
            }
            else
            {
                buyLine1Values[index] = 0;
                buyLine2Values[index] = 0;
            }

            if (BARSLASTISSELLSAFE < PD_PERIOD && index - BARSLASTISSELLSAFE >= 0)
            {
                int sellSafeIndex = index - BARSLASTISSELLSAFE;

                // 針對那根K線重新計算所有值
                int safeHOWCLOSELOW = BarsLast(i => goodLowCounts[i] >= GOOD_PERIOD, sellSafeIndex, Math.Min(PD_PERIOD, MAX_SCAN_BARS));
                int safeHOWCLOSEHIGH = BarsLast(i => goodHighCounts[i] >= GOOD_PERIOD, sellSafeIndex, Math.Min(PD_PERIOD, MAX_SCAN_BARS));

                double safeLASTGOODLOW = safeHOWCLOSELOW < PD_PERIOD ? pdLows[sellSafeIndex - safeHOWCLOSELOW] : 0;
                double safeLASTGOODHIGH2 = safeHOWCLOSELOW < PD_PERIOD && sellSafeIndex - safeHOWCLOSELOW - 1 >= 0
                    ? pdHighs[sellSafeIndex - safeHOWCLOSELOW - 1] : 0;

                double safeSELLLIQRANGE = Math.Abs(safeLASTGOODHIGH2 - safeLASTGOODLOW);

                // SELLLINE1:= IF(ISCHECKTIME, REF(LASTGOODLOW + SELLLIQRANGE * FIBO1, BARSLAST(ISSELLSAFE)), DRAWNULL)
                sellLine1Values[index] = safeLASTGOODLOW + safeSELLLIQRANGE * FIBO1;
                // SELLLINE1B:= IF(ISCHECKTIME, REF(LASTGOODLOW + SELLLIQRANGE * FIBO2, BARSLAST(ISSELLSAFE)), DRAWNULL)
                sellLine2Values[index] = safeLASTGOODLOW + safeSELLLIQRANGE * FIBO2;
            }
            else
            {
                sellLine1Values[index] = 0;
                sellLine2Values[index] = 0;
            }
        }
        else
        {
            // 不在檢查時間內，設為0
            buyLine1Values[index] = 0;
            buyLine2Values[index] = 0;
            sellLine1Values[index] = 0;
            sellLine2Values[index] = 0;
        }
    }

    private void ProcessPriceLines(int index)
    {
        // 只有在有足夠的歷史數據時才處理
        if (index <= 0) return;

        // 處理買入線1 (B線) - 使用線系列索引 0
        if (buyLine1Values[index] != 0 && buyLine1Values[index] == buyLine1Values[index - 1])
            SetValue(buyLine1Values[index], 0);
        else
            SetValue(0, 0);

        // 處理買入線2 (P線) - 使用線系列索引 1
        if (buyLine2Values[index] != 0 && buyLine2Values[index] == buyLine2Values[index - 1])
            SetValue(buyLine2Values[index], 1);
        else
            SetValue(0, 1);

        // 處理賣出線1 (S線) - 使用線系列索引 2
        if (sellLine1Values[index] != 0 && sellLine1Values[index] == sellLine1Values[index - 1])
            SetValue(sellLine1Values[index], 2);
        else
            SetValue(0, 2);

        // 處理賣出線2 (R線) - 使用線系列索引 3
        if (sellLine2Values[index] != 0 && sellLine2Values[index] == sellLine2Values[index - 1])
            SetValue(sellLine2Values[index], 3);
        else
            SetValue(0, 3);
    }
}