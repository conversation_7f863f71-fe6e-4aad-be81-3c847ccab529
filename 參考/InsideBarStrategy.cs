// Copyright QUANTOWER LLC. © 2017-2022. All rights reserved.

using RiskManagement;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Security.Principal;
using System.Threading;
using System.Threading.Tasks;
using TradingPlatform.BusinessLayer;

namespace Strategy1k
{
    public sealed class Strategy1k : Strategy, ICurrentAccount, ICurrentSymbol
    {
        [InputParameter("Symbol", 0)]
        public Symbol CurrentSymbol { get; set; }

        /// <summary>
        /// Account to place orders
        /// </summary>
        [InputParameter("Account", 1)]
        public Account CurrentAccount { get; set; }



        /// <summary>
        /// Quantity to open order
        /// </summary>
        [InputParameter("Quantity", 2, 0.1, ********, 0.1, 2)]
        public double Quantity { get; set; }

        /// <summary>
        /// Period to load history
        /// </summary>
        [InputParameter("Period", 3)]
        public Period Period { get; set; }

        /// <summary>
        /// Start point to load history
        /// </summary>
        [InputParameter("Start point", 4)]
        public DateTime StartPoint { get; set; }

        [InputParameter("Using Trading Time", 5)]
        public bool UsingTradingTime = true;

        [InputParameter("Start Time", 6)]
        public string StartTime = "21:30";

        [InputParameter("End Time", 7)]
        public string EndTime = "03:43";

        [InputParameter("End Time(Flatern)", 7)]
        public string EndTimeFlatern = "03:43";

        [InputParameter("Personally Daily Loss", 8)]
        public bool UsingPDLL = false;

        [InputParameter("Personally Daily Profit", 8)]
        public bool UsingPDPT = false;


        [InputParameter("Max Loss", 8)]
        public double MaxLoss = 300;

        [InputParameter("Max Profit", 9)]
        public double MaxProfit = 900;

        [InputParameter("ATR", 11)]
        public double ATRK = 3;

        [InputParameter("RR", 12)]
        public double RRR = 2;

        [InputParameter("Max Trades per day", 13)]
        public int MaxTrades = 3;

        [InputParameter("Additional symbol")]
        public Symbol AdditionalSymbol { get; set; }

        [InputParameter("Additional Period", 3)]
        public Period AdditionalPeriod { get; set; }

        private HistoricalData additionalData;

        public override string[] MonitoringConnectionsIds => new string[] { this.CurrentSymbol?.ConnectionId, this.CurrentAccount?.ConnectionId };


        private Indicator podfInd;
        private Indicator podfIndex;
        private Indicator indicatorATRMA;
        private Indicator indicatorRSIMA;

        private HistoricalData hdm;
        private HistoricalData hdd;

        private int longPositionsCount;
        private int shortPositionsCount;
        private string orderTypeId;
        private string orderTypeIdLimit;
        private string orderTypeIdStop;

        private bool waitOpenPosition;
        private bool waitClosePositions;

        private string buyOrderId;
        private string sellOrderId;
        private string tslId;

        private double buyOffset;
        private double sellOffset;
        private double buyTriggerTrigger = -99999;
        private double sellTriggerTrigger = -99999;

        private double totalNetPl;
        private double totalGrossPl;
        private double totalFee;

        private bool ReadyToLao;
        private bool ReadyToBreak;

        private bool jilei = false;
        private bool zhongjie = false;
        private bool caozong = false;

        private const int PP_LINE_INDEX = 0;

        private const int R1_LINE_INDEX = 1;
        private const int R2_LINE_INDEX = 2;
        private const int R3_LINE_INDEX = 3;
        private const int R4_LINE_INDEX = 4;
        private const int R5_LINE_INDEX = 5;
        private const int R6_LINE_INDEX = 6;

        private const int S1_LINE_INDEX = 7;
        private const int S2_LINE_INDEX = 8;
        private const int S3_LINE_INDEX = 9;
        private const int S4_LINE_INDEX = 10;
        private const int S5_LINE_INDEX = 11;
        private const int S6_LINE_INDEX = 12;

        private double entryPrice;
        private string orderHistoryId;
        private double bePrice;

        private bool longWinToday = false;
        private bool shortWinToday = false;
        private bool InTradeDay = false;

        private DateTime LastFlatternTime;
        private bool LastFlatternTimeSet;


        private RiskManager riskManager;
        public class BarData
        {
            public double Open { get; set; }
            public double High { get; set; }
            public double Low { get; set; }
            public double Close { get; set; }
            public double Volume { get; set; }
            public double Ticks { get; set; }
        }

        private BarData[] barBuyDataArray = new BarData[30];
        private BarData[] barSellDataArray = new BarData[30];
        private int currentBuyIndex = 0;
        private int currentSellIndex = 0;

        private int buyCount = 0;
        private int sellCount = 0;

        private int barIndex = 1;
        private List<Box> listboxes = null;


        public Strategy1k()
            : base()
        {
            this.Name = "IBStrategy";
            this.Description = "Raw strategy without any additional functional";


            this.Period = Period.MIN5;
            this.AdditionalPeriod = Period.MIN5;
            this.StartPoint = Core.TimeUtils.DateTimeUtcNow.AddDays(-100);
        }

        protected override void OnRun()
        {
            if (!this.UsingTradingTime)
            {
                this.UsingPDLL = false;
                this.UsingPDPT = false;
            }
            this.riskManager = new RiskManager(
                this.CurrentAccount,
                this.CurrentSymbol,
                this.UsingPDLL, this.MaxLoss, // 使用每日亏损限制
                this.UsingPDPT, this.MaxProfit, // 使用每日盈利目标
                this.UsingTradingTime, this.StartTime, this.EndTime, this.EndTimeFlatern, this.MaxTrades, // 使用交易时间
                this.Log
            );
            this.totalNetPl = 0D;

            // Restore symbol object from active connection
            if (this.CurrentSymbol != null && this.CurrentSymbol.State == BusinessObjectState.Fake)
                this.CurrentSymbol = Core.Instance.GetSymbol(this.CurrentSymbol.CreateInfo());

            if (this.CurrentSymbol == null)
            {
                this.Log("Incorrect input parameters... Symbol have not specified.", StrategyLoggingLevel.Error);
                return;
            }
            if (this.AdditionalSymbol != null && this.AdditionalSymbol.State == BusinessObjectState.Fake)
                this.AdditionalSymbol = Core.Instance.GetSymbol(this.AdditionalSymbol.CreateInfo());

            if (this.AdditionalSymbol == null)
            {
                this.Log("Incorrect input parameters... AdditionalSymbol have not specified.", StrategyLoggingLevel.Error);
                return;
            }


            // Restore account object from active connection
            if (this.CurrentAccount != null && this.CurrentAccount.State == BusinessObjectState.Fake)
                this.CurrentAccount = Core.Instance.GetAccount(this.CurrentAccount.CreateInfo());

            if (this.CurrentAccount == null)
            {
                this.Log("Incorrect input parameters... Account have not specified.", StrategyLoggingLevel.Error);
                return;
            }

            if (this.CurrentSymbol.ConnectionId != this.CurrentAccount.ConnectionId)
            {
                this.Log("Incorrect input parameters... Symbol and Account from different connections.", StrategyLoggingLevel.Error);
                return;
            }


            this.orderTypeId = Core.OrderTypes.FirstOrDefault(x => x.ConnectionId == this.CurrentSymbol.ConnectionId && x.Behavior == OrderTypeBehavior.Market).Id;
            this.orderTypeIdLimit = Core.OrderTypes.FirstOrDefault(x => x.ConnectionId == this.CurrentSymbol.ConnectionId && x.Behavior == OrderTypeBehavior.Limit).Id;
            this.orderTypeIdStop = Core.OrderTypes.FirstOrDefault(x => x.ConnectionId == this.CurrentSymbol.ConnectionId && x.Behavior == OrderTypeBehavior.Stop).Id;

            if (string.IsNullOrEmpty(this.orderTypeId))
            {
                this.Log("Connection of selected symbol has not support market orders", StrategyLoggingLevel.Error);
                return;
            }
            if (string.IsNullOrEmpty(this.orderTypeIdLimit))
            {
                this.Log("Connection of selected symbol has not support market orders", StrategyLoggingLevel.Error);
                return;
            }
            if (string.IsNullOrEmpty(this.orderTypeIdStop))
            {
                this.Log("Connection of selected symbol has not support market orders", StrategyLoggingLevel.Error);
                return;
            }



            this.hdm = this.CurrentSymbol.GetHistory(this.Period, this.CurrentSymbol.HistoryType, this.StartPoint);
            this.hdd = this.CurrentSymbol.GetHistory(Period.DAY1, this.CurrentSymbol.HistoryType, this.StartPoint);



            this.additionalData = this.AdditionalSymbol.GetHistory(this.AdditionalPeriod, this.AdditionalSymbol.HistoryType, this.StartPoint);

            Core.PositionAdded += this.Core_PositionAdded;
            Core.PositionRemoved += this.Core_PositionRemoved;

            Core.OrdersHistoryAdded += this.Core_OrdersHistoryAdded;

            Core.TradeAdded += this.Core_TradeAdded;


            this.hdm.NewHistoryItem += this.Hdm_HistoryItemUpdated;
            this.hdd.NewHistoryItem += this.Hdd_HistoryItemUpdated;



            IndicatorInfo podfIndicatorInfo = Core.Instance.Indicators.All.FirstOrDefault(info => info.Name == "Inside Bar Channel indicator");
            this.podfInd = Core.Instance.Indicators.CreateIndicator(podfIndicatorInfo);




            //DrawValueAreaForEachBarIndicator
            this.hdm.AddIndicator(this.podfInd);



            IndicatorInfo podfIndicatorInfoex = Core.Instance.Indicators.All.FirstOrDefault(info => info.Name == "Inside Bar Channel indicator");
            this.podfIndex = Core.Instance.Indicators.CreateIndicator(podfIndicatorInfoex);




            //DrawValueAreaForEachBarIndicator
            this.additionalData.AddIndicator(this.podfIndex);


            this.indicatorATRMA = Core.Instance.Indicators.BuiltIn.ATR(20, MaMode.SMMA, IndicatorCalculationType.AllAvailableData);
            this.hdm.AddIndicator(this.indicatorATRMA);

            this.indicatorRSIMA = Core.Instance.Indicators.BuiltIn.RSI(20, PriceType.Close, RSIMode.Simple, MaMode.EMA, 2, IndicatorCalculationType.AllAvailableData);
            this.hdm.AddIndicator(this.indicatorRSIMA);

            this.longWinToday = false;
            this.shortWinToday = false;
            this.InTradeDay = false;

            this.listboxes = new List<Box>();

        }

        protected override void OnStop()
        {
            Core.PositionAdded -= this.Core_PositionAdded;
            Core.PositionRemoved -= this.Core_PositionRemoved;

            Core.OrdersHistoryAdded -= this.Core_OrdersHistoryAdded;

            Core.TradeAdded -= this.Core_TradeAdded;


            if (this.hdm != null)
            {
                this.hdm.HistoryItemUpdated -= this.Hdm_HistoryItemUpdated;
                this.hdm.RemoveIndicator(podfInd);

                this.hdm.RemoveIndicator(this.indicatorATRMA);
                this.hdm.RemoveIndicator(this.indicatorRSIMA);
                this.hdm.Dispose();
            }
            if (this.hdd != null)
            {

                this.hdd.HistoryItemUpdated -= this.Hdd_HistoryItemUpdated;

                this.hdd.Dispose();
            }
            if (this.additionalData != null)
            {
                this.additionalData.RemoveIndicator(this.podfIndex);
                this.additionalData?.Dispose();
            }
            if (this.riskManager != null)
            {
                this.riskManager = null;
            }
            if (this.listboxes != null)
            {
                this.listboxes = null;
            }
            this.totalFee = 0;
            this.totalGrossPl = 0;
            this.totalNetPl = 0;

            // Restore symbol object from active connection
            if (this.CurrentSymbol != null && this.CurrentSymbol.State == BusinessObjectState.Fake)
                this.CurrentSymbol = Core.Instance.GetSymbol(this.CurrentSymbol.CreateInfo());

            if (this.CurrentSymbol == null)
            {
                this.Log("Incorrect input parameters... Symbol have not specified.", StrategyLoggingLevel.Error);
                return;
            }
            if (this.AdditionalSymbol != null && this.AdditionalSymbol.State == BusinessObjectState.Fake)
                this.AdditionalSymbol = Core.Instance.GetSymbol(this.AdditionalSymbol.CreateInfo());

            if (this.AdditionalSymbol == null)
            {
                this.Log("Incorrect input parameters... AdditionalSymbol have not specified.", StrategyLoggingLevel.Error);
                return;
            }


            // Restore account object from active connection
            if (this.CurrentAccount != null && this.CurrentAccount.State == BusinessObjectState.Fake)
                this.CurrentAccount = Core.Instance.GetAccount(this.CurrentAccount.CreateInfo());

            if (this.CurrentAccount == null)
            {
                this.Log("Incorrect input parameters... Account have not specified.", StrategyLoggingLevel.Error);
                return;
            }

            if (this.CurrentSymbol.ConnectionId != this.CurrentAccount.ConnectionId)
            {
                this.Log("Incorrect input parameters... Symbol and Account from different connections.", StrategyLoggingLevel.Error);
                return;
            }
            base.OnStop();
        }



        protected override void OnInitializeMetrics(Meter meter)
        {
            base.OnInitializeMetrics(meter);

            meter.CreateObservableCounter("total-long-positions", () => this.longPositionsCount, description: "Total long positions");
            meter.CreateObservableCounter("total-short-positions", () => this.shortPositionsCount, description: "Total short positions");

            meter.CreateObservableCounter("total-pl-net", () => this.totalNetPl, description: "Total Net profit/loss");
            meter.CreateObservableCounter("total-pl-gross", () => this.totalGrossPl, description: "Total Gross profit/loss");
            meter.CreateObservableCounter("total-fee", () => this.totalFee, description: "Total fee");
        }



        private void Core_PositionAdded(Position obj)
        {
            var positions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            this.longPositionsCount = positions.Count(x => x.Side == Side.Buy);
            this.shortPositionsCount = positions.Count(x => x.Side == Side.Sell);

            double currentPositionsQty = positions.Sum(x => x.Side == Side.Buy ? x.Quantity : -x.Quantity);

            if (Math.Abs(currentPositionsQty) == this.Quantity)
                this.waitOpenPosition = false;







        }

        private void Core_PositionRemoved(Position obj)
        {
            var positions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            this.longPositionsCount = positions.Count(x => x.Side == Side.Buy);
            this.shortPositionsCount = positions.Count(x => x.Side == Side.Sell);

            if (!positions.Any())
            {
                this.waitClosePositions = false;
                this.ReadyToBreak = false;
                this.ReadyToLao = false;
            }
            else
            {
                this.riskManager.FlattenPositions();
                /*this.buyOrderId = null;
                this.sellOrderId = null;
                if (this.waitOpenPosition) this.waitOpenPosition = false;
                if (this.waitClosePositions) this.waitClosePositions = false;
                if (this.InTradeDay) this.InTradeDay = false;
                if (this.longWinToday) this.longWinToday = false;
                if (this.shortWinToday) this.shortWinToday = false;*/

            }
        }


        private void CheckAndModifyStopLoss()
        {

            var orders = Core.Instance.Orders.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount && (x.Status == OrderStatus.Opened));
            if (!(orders.Any()))
                return;


            foreach (var order in orders)
            {

                var nowbar = (HistoryItemBar)(this.hdm[0]);
                var thisbar = (HistoryItemBar)(this.hdm[1]);
                var lastbar = (HistoryItemBar)(this.hdm[2]);
                double currentPrice = thisbar.Close;
                bool riskBuy = currentPrice > this.FormatSymbolPrice(this.podfInd.GetValue(1, 1));
                bool riskSell = currentPrice < this.FormatSymbolPrice(this.podfInd.GetValue(1, 0));

                if (true)
                {
                    // 修改止損為保本點
                    this.entryPrice = order.Price;

                    if (entryPrice > 0 && (order.StopLoss != null || order.TakeProfit != null))
                    {
                        if (order.FilledQuantity > 0 && order.Side == Side.Buy && currentPrice > entryPrice && nowbar.Close > this.CurrentSymbol.CalculatePrice(entryPrice, 10))
                        {
                            double tickSize = this.CurrentSymbol.TickSize;
                            double atrPoints = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0);
                            double atrPrice = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0) * this.ATRK;
                            double stopLoss = atrPrice;
                            double stopLossPoint = this.indicatorATRMA.GetValue(1) * this.ATRK;
                            //Открытие новых позиций
                            //this.Log($"{this.podfInd.GetValue(0, 2)}");
                            double c = thisbar.Close;
                            double o = thisbar.Open;
                            double threshold = 10 * atrPoints;
                            double topline1 = this.podfInd.GetValue(1, 2);
                            double topline2 = this.podfInd.GetValue(1, 6);
                            double topline = Math.Abs(topline1 - topline2) > threshold ? topline1 : Math.Max(topline1, topline2);
                            double tpPrice = topline1;
                            var nearSellBar = FindNearestSellBar(tpPrice);
                            bool isCFD = !(thisbar.Volume > 0);
                            double safeTpPrice = !isCFD && nearSellBar != null ? Math.Max(nearSellBar.Open, nearSellBar.Close) : tpPrice;
                            Core.Instance.ModifyOrder(new ModifyOrderRequestParameters(order)
                            {
                                StopLoss = SlTpHolder.CreateSL(-2, PriceMeasurement.Offset),
                                //TakeProfit = SlTpHolder.CreateSL(Math.Abs(this.CurrentSymbol.CalculateTicks(this.entryPrice, topline)), PriceMeasurement.Offset)
                            });

                            this.Log($"{order.PositionId} 哈 {Math.Abs(this.CurrentSymbol.CalculateTicks(this.entryPrice, topline))}");
                        }

                        if (order.FilledQuantity > 0 && order.Side == Side.Sell && currentPrice < entryPrice && nowbar.Close < this.CurrentSymbol.CalculatePrice(entryPrice, 10))
                        {
                            double tickSize = this.CurrentSymbol.TickSize;
                            double atrPoints = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0);
                            double atrPrice = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0) * this.ATRK;
                            double stopLoss = atrPrice;
                            double stopLossPoint = this.indicatorATRMA.GetValue(1) * this.ATRK;
                            //Открытие новых позиций
                            //this.Log($"{this.podfInd.GetValue(0, 2)}");
                            double c = thisbar.Close;
                            double o = thisbar.Open;
                            double threshold = 10 * atrPoints;
                            double bottomline1 = this.podfInd.GetValue(1, 3);
                            double bottomline2 = this.podfInd.GetValue(1, 7);
                            double bottomline = Math.Abs(bottomline1 - bottomline2) > threshold ? bottomline1 : Math.Min(bottomline1, bottomline2);
                            double tpPrice = bottomline;
                            var nearBuyBar = FindNearestSellBar(tpPrice);
                            bool isCFD = !(thisbar.Volume > 0);
                            double safeTpPrice = !isCFD && nearBuyBar != null ? Math.Min(nearBuyBar.Open, nearBuyBar.Close) : tpPrice;
                            Core.Instance.ModifyOrder(new ModifyOrderRequestParameters(order)
                            {
                                StopLoss = SlTpHolder.CreateSL(-2, PriceMeasurement.Offset),
                                //TakeProfit = SlTpHolder.CreateSL(Math.Abs(this.CurrentSymbol.CalculateTicks(this.entryPrice, bottomline)), PriceMeasurement.Offset)
                            });
                            this.Log($"{order.PositionId} 哈 {Math.Abs(this.CurrentSymbol.CalculateTicks(this.entryPrice, bottomline))}");

                        }


                    }

                    //this.Log($"修改止損為保本點{this.entryPrice}");
                }



            }
        }

        private void Core_OrdersHistoryAdded(OrderHistory obj)
        {



            if (obj.Symbol == this.CurrentSymbol)
                return;

            if (obj.Account == this.CurrentAccount)
                return;

            if (obj.Status == OrderStatus.Refused)
                this.ProcessTradingRefuse();





        }

        private void Core_TradeAdded(Trade obj)
        {
            this.riskManager.AddTradeCount(1);
            this.ReadyToBreak = false;
            this.ReadyToLao = false;
            if (obj.Account != this.CurrentAccount || obj.Symbol != this.CurrentSymbol) return;
            if (obj.NetPnl != null)
                this.totalNetPl += obj.NetPnl.Value;

            if (obj.GrossPnl != null)
            {
                // 記錄成功的交易
                if (obj.GrossPnl.Value > 0 && obj.Account == this.CurrentAccount && obj.Symbol == this.CurrentSymbol)
                {
                    if (obj.Side == Side.Buy)
                    {
                        this.longWinToday = true;
                        this.shortWinToday = false;
                    }
                    else if (obj.Side == Side.Sell)
                    {
                        this.shortWinToday = true;
                        this.longWinToday = false;
                    }
                }
                else
                {
                    if (obj.Side == Side.Buy)
                    {
                        this.longWinToday = false;

                    }
                    else if (obj.Side == Side.Sell)
                    {
                        this.shortWinToday = false;

                    }
                }
                this.totalGrossPl += obj.GrossPnl.Value;
            }


            if (obj.Fee != null)
                this.totalFee += obj.Fee.Value;

            //Trailing SL
            if (false)//(buyTriggerTrigger != -99999 || sellTriggerTrigger != -99999)
            {
                if (buyTriggerTrigger != -99999) { buyTriggerTrigger = -99999; };
                if (sellTriggerTrigger != -99999) { sellTriggerTrigger = -99999; };
                this.ReadyToBreak = false;
                this.ReadyToLao = false;
                var tslRequest = new PlaceOrderRequestParameters();
                tslRequest.Symbol = this.CurrentSymbol;
                tslRequest.Account = this.CurrentAccount;
                var allowedOrderType = this.CurrentSymbol.GetAlowedOrderTypes(OrderTypeUsage.All);
                // If TSL not available, we try Trail Offset, otherwise we use a normal SL
                tslRequest.OrderTypeId = allowedOrderType.Any(o => o.Id.Equals(OrderType.TrailingStop) || o.Behavior == OrderTypeBehavior.TrailingStop) ? OrderType.TrailingStop : allowedOrderType.Any(o => o.Id.Equals(OrderType.TRAIL_OFFSET)) ? OrderType.TRAIL_OFFSET : OrderType.Stop;
                /*AdditionalParameters = new List<SettingItem>(){new SettingItemBoolean("Reduce-Only", true)},*/
                if (tslRequest.OrderTypeId.Equals(OrderType.Stop))
                {
                    tslRequest.AdditionalParameters = new List<SettingItem> { new SettingItemBoolean("isTrailing", true) };
                }
                tslRequest.Side = obj.Side.Equals(Side.Buy) ? Side.Sell : Side.Buy; //反向
                tslRequest.TriggerPrice = obj.Side.Equals(Side.Buy) ? buyTriggerTrigger : sellTriggerTrigger;
                /*                tslRequest.Price = myPosition.Side.Equals(Side.Buy) ? Math.Min(breakEven, atmTriggerPrice) : Math.Max(breakEven, atmTriggerPrice);*/
                tslRequest.TrailOffset = (obj.Side.Equals(Side.Buy) ? this.buyOffset : this.sellOffset) / this.CurrentSymbol.TickSize;
                tslRequest.Quantity = Convert.ToDouble(this.CurrentSymbol.FormatQuantity(this.Quantity));


                var result2 = Core.Instance.PlaceOrder(tslRequest);

                if (result2.Status == TradingOperationResultStatus.Failure)
                {

                    this.LogInfo(result2.Message + " Error during ATM TSL");

                }

                else
                {
                    this.LogInfo("TSL placed successfully");
                    tslId = result2.OrderId;

                }

            }










        }

        private void Hdm_HistoryItemUpdated(object sender, HistoryEventArgs e) => this.OnUpdate();
        private void Hdd_HistoryItemUpdated(object sender, HistoryEventArgs e) => this.OnDayUpdate();
        private void OnDayUpdate()
        {
            jilei = false;
            zhongjie = false;
            caozong = false;
        }

        private void OnUpdate()
        {



            DateTime cTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(this.hdm[1].TimeLeft, Core.Instance.TimeUtils.SelectedTimeZone);
            this.riskManager.UpdateCurrentTime(cTime);
            this.riskManager.UpdateBalance(this.CurrentAccount.Balance);


            if (this.riskManager.ShouldFlatten(this.CurrentAccount))
            {


                if (!this.LastFlatternTimeSet)
                {
                    this.riskManager.FlattenPositions();
                    this.buyOrderId = null;
                    this.sellOrderId = null;
                    this.LastFlatternTime = cTime;
                    this.LastFlatternTimeSet = true;
                    if (this.waitOpenPosition) this.waitOpenPosition = false;
                    if (this.waitClosePositions) this.waitClosePositions = false;
                    if (this.InTradeDay) this.InTradeDay = false;
                    if (this.longWinToday) this.longWinToday = false;
                    if (this.shortWinToday) this.shortWinToday = false;
                }
                else
                {
                    if (cTime - this.LastFlatternTime > TimeSpan.FromMinutes(30))
                    {
                        this.LastFlatternTime = cTime;
                        this.LastFlatternTimeSet = true;
                        this.riskManager.FlattenPositions();
                        this.buyOrderId = null;
                        this.sellOrderId = null;
                        if (this.waitOpenPosition) this.waitOpenPosition = false;
                        if (this.waitClosePositions) this.waitClosePositions = false;
                        if (this.InTradeDay) this.InTradeDay = false;
                        if (this.longWinToday) this.longWinToday = false;
                        if (this.shortWinToday) this.shortWinToday = false;
                    }

                }


                return;
            }
            if (!this.UsingTradingTime)
            {

                if (this.InTradeDay) this.InTradeDay = false;
                if (this.longWinToday) this.longWinToday = false;
                if (this.shortWinToday) this.shortWinToday = false;
            }
            this.InTradeDay = true;
            //CheckAndModifyStopLoss();


            if (this.waitOpenPosition)
                return;

            if (this.waitClosePositions)
                return;


            var thisbar = (HistoryItemBar)(this.hdm[1]);
            var lastbar = (HistoryItemBar)(this.hdm[2]);
            var lastlastbar = (HistoryItemBar)(this.hdm[3]);
            bool isCFD = !(thisbar.Volume > 0);
            if (!isCFD && (thisbar.Volume > 0 && thisbar.Volume > lastbar.Volume * 2.5 && lastbar.Volume < lastlastbar.Volume * 1.2))
            {
                if (thisbar.Close > thisbar.Open)
                {
                    barBuyDataArray[currentBuyIndex] = new BarData
                    {
                        Open = thisbar[PriceType.Open],
                        High = thisbar[PriceType.High],
                        Low = thisbar[PriceType.Low],
                        Close = thisbar[PriceType.Close],
                        Volume = thisbar.Volume,
                        Ticks = thisbar.Ticks
                    };
                    currentBuyIndex = (currentBuyIndex + 1) % 30; // 循環使用數組
                    buyCount = 9;
                }

                if (thisbar.Close < thisbar.Open)
                {
                    barSellDataArray[currentSellIndex] = new BarData
                    {
                        Open = thisbar[PriceType.Open],
                        High = thisbar[PriceType.High],
                        Low = thisbar[PriceType.Low],
                        Close = thisbar[PriceType.Close],
                        Volume = thisbar.Volume,
                        Ticks = thisbar.Ticks
                    };
                    currentSellIndex = (currentSellIndex + 1) % 30; // 循環使用數組
                    sellCount = 9;
                }



            }
            if (buyCount > 0)
            {
                buyCount--;

            }
            if (sellCount > 0)
            {
                sellCount--;

            }
            if (isInsideBar(1, barIndex + 1))
            {
                barIndex++;
            }
            if (this.indicatorRSIMA.GetValue(2) >= 48 && this.indicatorRSIMA.GetValue(1) < 47)
            {
                this.ReadyToLao = true;

            }
            if (this.indicatorRSIMA.GetValue(2) <= 52 && this.indicatorRSIMA.GetValue(1) > 53)
            {
                this.ReadyToBreak = true;

            }
            else if (isInsideBar(1, barIndex) && !isInsideBar(0, barIndex))
            {


                Double PreviusHigh = Math.Max(this.hdm.Open(barIndex + 1), this.hdm.Close(barIndex + 1));
                Double PreviusLow = Math.Min(this.hdm.Open(barIndex + 1), this.hdm.Close(barIndex + 1));

                //Log("Box Found", LoggingLevel.System);


                if (barIndex > 1)
                {
                    Box box = new Box();

                    box.PreviusHigh = PreviusHigh;
                    box.PreviusLow = PreviusLow;

                    listboxes.Add(box);

                    barIndex = 1;
                    if (this.indicatorRSIMA.GetValue(1) < this.indicatorRSIMA.GetValue(2))
                    {
                        this.ReadyToLao = true;

                    }
                    else if (this.indicatorRSIMA.GetValue(1) > this.indicatorRSIMA.GetValue(2))
                    {
                        this.ReadyToBreak = true;

                    }
                }
                else if (this.indicatorRSIMA.GetValue(1) >= 48 && this.indicatorRSIMA.GetValue(1) <= 52)
                {
                    this.ReadyToBreak = false;
                    this.ReadyToLao = false;
                }



            }
            if (this.indicatorRSIMA.GetValue(1) >= 48 && this.indicatorRSIMA.GetValue(1) <= 52)
            {
                this.ReadyToBreak = false;
                this.ReadyToLao = false;
            }

            var thisDayBar = (HistoryItemBar)this.hdd[0];
            var lastDayBar = (HistoryItemBar)this.hdd[0];

            if (this.ReadyToLao)
            {
                if (thisbar.Close <= thisDayBar.Open)
                {
                    jilei = true;

                }
                else
                {
                    caozong = true;

                }

            }

            if (this.ReadyToBreak)
            {
                if (thisbar.Close >= thisDayBar.Open)
                {
                    jilei = true;

                }
                else
                {
                    caozong = true;

                }

            }
            if (Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray().Any() || this.riskManager.CanTrade() || (!this.ReadyToLao && !this.ReadyToBreak))
            {
                //Закрытие позиций
                //var thisbar = (HistoryItemBar)(this.hdm[1]);
                //var lastbar = (HistoryItemBar)(this.hdm[2]);
                /*
                if (thisbar.Close < this.podfInd.GetValue(1,0) || thisbar.Close > this.podfInd.GetValue(1, 1))
                {
                    this.waitClosePositions = true;
                    this.Log($"Start close positions ({positions.Length})");

                    foreach (var item in positions)
                    {
                        var result = item.Close();

                        if (result.Status == TradingOperationResultStatus.Failure)
                        {
                            this.Log($"Close positions refuse: {(string.IsNullOrEmpty(result.Message) ? result.Status : result.Message)}", StrategyLoggingLevel.Trading);
                            this.ProcessTradingRefuse();
                        }
                        else
                            this.Log($"Position was close: {result.Status}", StrategyLoggingLevel.Trading);
                    }
                }*/
            }
            else
            {




                double tickSize = this.CurrentSymbol.TickSize;
                double atrPoints = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0);
                double atrPrice = Math.Round(this.indicatorATRMA.GetValue(1) / tickSize, 0) * this.ATRK;
                double stopLoss = atrPrice;
                double stopLossPoint = this.indicatorATRMA.GetValue(1) * this.ATRK;
                //Открытие новых позиций
                //this.Log($"{this.podfInd.GetValue(0, 2)}");
                double c = thisbar.Close;
                double o = thisbar.Open;
                double threshold = 10 * atrPoints;
                double bottomline1 = this.podfInd.GetValue(1, 3);
                double bottomline2 = this.podfInd.GetValue(1, 7);
                double bottomline = Math.Abs(bottomline1 - bottomline2) > threshold ? bottomline1 : Math.Min(bottomline1, bottomline2);
                double topline1 = this.podfInd.GetValue(1, 2);
                double topline2 = this.podfInd.GetValue(1, 6);
                double topline = Math.Abs(topline1 - topline2) > threshold ? topline1 : Math.Max(topline1, topline2);

                if (!zhongjie && lastbar.Close < this.podfInd.GetValue(1, 9) && (this.ReadyToLao ))
                {

                    /*if (this.shortWinToday) 
                    {
                        this.Log("Skipping long position as we had a successful short trade today", StrategyLoggingLevel.Trading);
                        return;
                    }*/
                    if (!caozong && this.sellCount > 0)
                    {
                        caozong = true; this.ReadyToLao = false;
                        return;

                    }
                    if (!jilei)
                    {
                        if (thisbar.Close > thisDayBar.Close)
                        {
                            jilei = true;
                        }
                        else
                        {
                            return;
                        }

                    }
                    if (thisbar.Close >= lastDayBar.High && lastDayBar.High > thisDayBar.Open)
                    {
                        zhongjie = true;
                    }

                    if (!(this.buyCount == 0 )) return;



                    // 檢測tpline
                    double tpline1 = this.podfInd.GetValue(1, 1);
                    double tpline2 = this.podfInd.GetValue(1, 5);
                    double tpline = Math.Abs(tpline1 - tpline2) > threshold ? tpline1 : Math.Min(tpline1, tpline2);

                    // 檢測normalline
                    double normalline1 = this.podfInd.GetValue(1, 9);
                    double normalline2 = this.podfInd.GetValue(1, 11);
                    double normalline = Math.Abs(normalline1 - normalline2) > threshold ? normalline1 : Math.Min(normalline1, normalline2);
                    double limitedPrice = this.FormatSymbolPrice(bottomline);
                    double slPrice = this.FormatSymbolPrice(bottomline - stopLossPoint);

                    double tpPrice = this.RRR < 2 ? this.FormatSymbolPrice(normalline) : this.RRR > 2 ? this.FormatSymbolPrice(topline) : this.FormatSymbolPrice(normalline);
                    var nearSellBar = FindNearestSellBar(tpPrice);

                    double safeTpPrice = !isCFD && nearSellBar != null ? Math.Max(nearSellBar.Open, nearSellBar.Close) : tpPrice;
                    this.buyTriggerTrigger = slPrice;
                    this.buyOffset = (int)Math.Abs(limitedPrice - slPrice) / 2;
                    bool risk = Math.Abs(limitedPrice - slPrice) > Math.Abs(limitedPrice - tpPrice);
                    if (limitedPrice > LLV(PriceType.Low, 0, 90)) return;
                    this.waitOpenPosition = true;
                    this.Log("Start open buy  limit order");
                    var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
                    {
                        Account = this.CurrentAccount,
                        Symbol = this.CurrentSymbol,

                        OrderTypeId = this.orderTypeIdLimit,
                        Price = limitedPrice,
                        TimeInForce = TimeInForce.Default,
                        Quantity = this.Quantity,
                        Side = Side.Buy,

                        StopLoss = SlTpHolder.CreateSL(Math.Abs(this.CurrentSymbol.CalculateTicks(limitedPrice, slPrice)), PriceMeasurement.Offset),
                        TakeProfit = SlTpHolder.CreateTP(Math.Abs(this.CurrentSymbol.CalculateTicks(limitedPrice, safeTpPrice)), PriceMeasurement.Offset)
                    });

                    if (result.Status == TradingOperationResultStatus.Failure)
                    {
                        this.Log($"Place buy order refuse: {(string.IsNullOrEmpty(result.Message) ? result.Status : result.Message)}", StrategyLoggingLevel.Trading);
                        this.ProcessTradingRefuse();
                    }
                    else
                    {
                        this.Log($"order open: {result.Status}", StrategyLoggingLevel.Trading);
                        this.buyOrderId = result.OrderId;
                        //this.LastOrderTime = thisbar.TimeLeft;
                        //this.LastOrderTimeSet = true;
                        //this.Log($"buyid {this.buyOrderId}"); 


                    }




                }
                else if (!zhongjie && lastbar.Close > this.podfInd.GetValue(1, 8) && (this.ReadyToBreak ))
                {
                    if (!caozong && this.buyCount > 0)
                    {
                        caozong = true; this.ReadyToBreak = false;
                        return;
                    }
                    if (!jilei)
                    {
                        if (thisbar.Close < thisDayBar.Close)
                        {
                            jilei = true;
                        }
                        else
                        {
                            return;
                        }
                    }
                    if (thisbar.Close <= lastDayBar.High && lastDayBar.High < thisDayBar.Open)
                    {
                        zhongjie = true;
                    }

                    if (!(this.sellCount == 0 )) return;

                    //if (this.longWinToday) // 只有當天沒有成功的多頭交易時才開啟空頭倉位
                    //{
                    //this.Log("Skipping short position as we had a successful long trade today", StrategyLoggingLevel.Trading);
                    //return;
                    //}


                    // 檢測tpline
                    double tpline1 = this.podfInd.GetValue(1, 0);
                    double tpline2 = this.podfInd.GetValue(1, 4);
                    double tpline = Math.Abs(tpline1 - tpline2) > threshold ? tpline1 : Math.Max(tpline1, tpline2);

                    // 檢測normalline
                    double normalline1 = this.podfInd.GetValue(1, 8);
                    double normalline2 = this.podfInd.GetValue(1, 10);
                    double normalline = Math.Abs(normalline1 - normalline2) > threshold ? normalline1 : Math.Max(normalline1, normalline2);
                    double limitedPrice = this.FormatSymbolPrice(topline);
                    double slPrice = this.FormatSymbolPrice(topline + stopLossPoint);
                    double tpPrice = this.RRR < 2 ? this.FormatSymbolPrice(normalline) : this.RRR > 2 ? this.FormatSymbolPrice(bottomline) : this.FormatSymbolPrice(normalline);

                    var nearBuyBar = FindNearestBuyBar(tpPrice);

                    double safeTpPrice = !isCFD && nearBuyBar != null ? Math.Min(nearBuyBar.Open, nearBuyBar.Close) : tpPrice;
                    bool risk = Math.Abs(limitedPrice - slPrice) > Math.Abs(limitedPrice - tpPrice);
                    if (limitedPrice < HHV(PriceType.High, 0, 90)) return;
                    this.sellTriggerTrigger = slPrice;
                    this.sellOffset = (int)Math.Abs(limitedPrice - slPrice) / 2;
                    this.waitOpenPosition = true;
                    this.Log("Start open Sell limit order");
                    var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
                    {
                        Account = this.CurrentAccount,
                        Symbol = this.CurrentSymbol,

                        OrderTypeId = this.orderTypeIdLimit,
                        Price = limitedPrice,
                        TimeInForce = TimeInForce.Default,
                        Quantity = this.Quantity,
                        Side = Side.Sell,
                        StopLoss = SlTpHolder.CreateSL(Math.Abs(this.CurrentSymbol.CalculateTicks(limitedPrice, slPrice)), PriceMeasurement.Offset),
                        TakeProfit = SlTpHolder.CreateTP(Math.Abs(this.CurrentSymbol.CalculateTicks(limitedPrice, safeTpPrice)), PriceMeasurement.Offset)
                    });

                    if (result.Status == TradingOperationResultStatus.Failure)
                    {
                        this.Log($"Place sell order refuse: {(string.IsNullOrEmpty(result.Message) ? result.Status : result.Message)}", StrategyLoggingLevel.Trading);
                        this.ProcessTradingRefuse();
                    }
                    else
                    {
                        this.Log($"order open: {result.Status}", StrategyLoggingLevel.Trading);
                        this.sellOrderId = result.OrderId;
                        //this.LastOrderTime = thisbar.TimeLeft;
                        //this.LastOrderTimeSet = true;

                        //this.Log($"sellid {this.sellOrderId}");

                    }




                }
                //else if (lastbar.Close > this.podfInd.GetValue(1, 8) /*&& thisbar.Close < this.podfInd.GetValue(1, 8)*/ && this.ReadyToBreak)
                /*{
                    this.waitOpenPosition = true;
                    
                    
                    //buyTriggerTrigger = this.podfInd.GetValue(1, 2) + this.buyOffset;
                    //this.buyOffset = (int)Math.Abs(this.podfInd.GetValue(1, 8) - buyTriggerTrigger);
                    this.Log("Start open break buy position");
                    var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
                    {
                        Account = this.CurrentAccount,
                        Symbol = this.CurrentSymbol,

                        OrderTypeId = this.orderTypeIdStop,
                        TriggerPrice = this.podfInd.GetValue(1, 8),
                        TimeInForce = TimeInForce.Default,
                        Quantity = this.Quantity,
                        Side = Side.Buy,
                        StopLoss = SlTpHolder.CreateSL(this.podfInd.GetValue(1, 3) - stopLoss, PriceMeasurement.Absolute),
                        TakeProfit = SlTpHolder.CreateTP(this.podfInd.GetValue(1, 1) + (this.podfInd.GetValue(1, 2)- this.podfInd.GetValue(1, 1))*2, PriceMeasurement.Absolute),
                    });

                    if (result.Status == TradingOperationResultStatus.Failure)
                    {
                        this.Log($"Place break buy order refuse: {(string.IsNullOrEmpty(result.Message) ? result.Status : result.Message)}", StrategyLoggingLevel.Trading);
                        this.ProcessTradingRefuse();
                    }
                    else
                    {
                        this.Log($"Position open: {result.Status}", StrategyLoggingLevel.Trading);
                        this.sellOrderId = result.OrderId;
                    }
                        
                }*/
            }
        }

        private BarData FindNearestSellBar(double targetPrice)
        {
            BarData nearestBar = null;
            double minDifference = double.MaxValue;

            for (int i = 0; i < 30; i++)
            {
                if (barSellDataArray[i] == null) continue;

                if (targetPrice >= barSellDataArray[i].Low && targetPrice <= barSellDataArray[i].High)
                {
                    return barSellDataArray[i]; // 直接返回包含目標價格的K線
                }

                double midPrice = (barSellDataArray[i].High + barSellDataArray[i].Low) / 2;
                double difference = Math.Abs(targetPrice - midPrice);

                if (difference < minDifference)
                {
                    minDifference = difference;
                    nearestBar = barSellDataArray[i];
                }
            }

            return nearestBar;
        }

        private BarData FindNearestBuyBar(double targetPrice)
        {
            BarData nearestBar = null;
            double minDifference = double.MaxValue;

            for (int i = 0; i < 30; i++)
            {
                if (barBuyDataArray[i] == null) continue;

                if (targetPrice >= barBuyDataArray[i].Low && targetPrice <= barBuyDataArray[i].High)
                {
                    return barBuyDataArray[i]; // 直接返回包含目標價格的K線
                }

                double midPrice = (barBuyDataArray[i].High + barBuyDataArray[i].Low) / 2;
                double difference = Math.Abs(targetPrice - midPrice);

                if (difference < minDifference)
                {
                    minDifference = difference;
                    nearestBar = barBuyDataArray[i];
                }
            }

            return nearestBar;
        }

        private void ProcessTradingRefuse()
        {
            this.Log("Strategy have received refuse for trading action. It should be stopped", StrategyLoggingLevel.Error);
            this.Stop();
        }

        public double FormatSymbolPrice(double price)
        {
            Symbol symbol = this.CurrentSymbol;
            if (symbol == null)
                throw new ArgumentNullException(nameof(symbol));

            // 首先，將價格舍入到最接近的 tick size
            double roundedPrice = symbol.RoundPriceToTickSize(price, symbol.TickSize);

            // 然後，使用 Symbol 的 FormatPrice 方法來格式化價格
            double p;
            if (double.TryParse(symbol.FormatPrice(roundedPrice), out p))
            {
                p = symbol.RoundPriceToTickSize(price, symbol.TickSize);

            }
            return p;
        }

        public class Box
        {

            public Double PreviusHigh { get; set; }
            public Double PreviusLow { get; set; }


        }
        private Boolean isInsideBar(int currentbarp, int previusbarp)
        {
            int currentbar = currentbarp + 1;
            int previusbar = previusbarp + 1;
            double hp = Math.Max(this.hdm.Open(previusbar), this.hdm.Close(previusbar));
            double lp = Math.Min(this.hdm.Open(previusbar), this.hdm.Close(previusbar));
            double boxrange = hp - lp;
            double fibo2_hp = lp + boxrange * 1.618;
            double fibo2_lp = hp - boxrange * 1.618;
            Boolean isIB = (this.hdm.Median(currentbar) <= fibo2_hp && this.hdm.Median(currentbar) >= fibo2_lp) ;
            return isIB;
        }

        private double HHV(PriceType priceType, int startOffset, int count)
        {
            int maxValueOffset = startOffset;
            for (int i = 0; i < count; i++)
            {
                if (this.hdm.GetPrice(priceType, maxValueOffset) < this.hdm.GetPrice(priceType, startOffset + i))
                    maxValueOffset = startOffset + i;
            }
            return this.hdm.GetPrice(priceType, maxValueOffset);
        }

        private double LLV(PriceType priceType, int startOffset, int count)
        {
            int minValueOffset = startOffset;
            for (int i = 0; i < count; i++)
            {
                if (this.hdm.GetPrice(priceType, minValueOffset) > this.hdm.GetPrice(priceType, startOffset + i))
                    minValueOffset = startOffset + i;
            }
            return this.hdm.GetPrice(priceType, minValueOffset);
        }

    }
}
