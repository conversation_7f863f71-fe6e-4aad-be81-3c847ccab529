using System.Collections.Generic;
using System.Drawing;
using System.Xml.Linq;
using System;
using TradingPlatform.BusinessLayer;
using System.Linq;
using System.Drawing.Drawing2D;

public class TrendLineIndicator : Indicator
{
    private Point startPoint = Point.Empty;
    private Point endPoint = Point.Empty;
    private Point currentMousePosition = Point.Empty;  // 新增：記錄當前鼠標位置
    private TrendLine nearestLine = null;  // 新增：記錄最近的趨勢線
    private TrendLine nearestStraightLine = null;  // 新增：記錄最近的直線
    private TrendLine nearestCurveLine = null;  // 新增：記錄最近的弧線
    private List<TrendLine> nearestLines = new List<TrendLine>();  // 新增：記錄重疊區域的所有趨勢線
    private TrendLine selectedLongLine = null;  // 修改：記錄等待下穿的趨勢線（綠色）
    private TrendLine selectedShortLine = null;  // 新增：記錄等待上穿的趨勢線（紅色）
    private List<TrendLine> selectedLongLines = new List<TrendLine>();  // 新增：記錄等待下穿的多條趨勢線
    private List<TrendLine> selectedShortLines = new List<TrendLine>();  // 新增：記錄等待上穿的多條趨勢線
    private bool isSelectedLongLineActive = false;  // 修改：記錄綠色線是否活躍
    private bool isSelectedShortLineActive = false;  // 新增：記錄紅色線是否活躍
    private bool isDrawing = false;
    private List<TrendLine> trendLines = new List<TrendLine>();
    private Indicator atr;

    // 新增參數：趨勢線模式控制
    [InputParameter("TrendLine Mode", 1)]
    public int TrendLineMode = 1;  // 1 = 曲線模式, 2 = 直線模式, 3 = 混合模式（同時顯示曲線和直線）
    
    private enum PriceType
    {
        High,
        Low,
        BodyHighRight,    // 蠟燭實體右側高點
        BodyHighLeft,     // 蠟燭實體左側高點
        BodyLowRight,     // 蠟燭實體右側低點
        BodyLowLeft       // 蠟燭實體左側低點
    }

    private Rectangle buttonRect;
    private bool isButtonHovered = false;
    private bool isLinesLocked = false;  // 趨勢線鎖定
    private Color buttonColor = Color.FromArgb(128, 100, 162);  // 淺紫色
    private Color buttonHoverColor = Color.FromArgb(160, 130, 200);  // 較亮的淺紫色

    private Rectangle clearButtonRect;
    private bool isClearButtonHovered = false;

    private Rectangle clearAllButtonRect;
    private bool isClearAllButtonHovered = false;

    // 新增：顯示距離按鈕
    private Rectangle showDistanceButtonRect;
    private bool isShowDistanceButtonHovered = false;
    private bool showDistance = false;  // 改為默認false，表示默認不能選中趨勢線

    // 新增：開倉控制按鈕
    private Rectangle orderPlaceButtonRect;
    private bool isOrderPlaceButtonHovered = false;
    private bool IsOrderPlaceAllowed = false;  // 默認關閉開倉功能

    // 批次計數器，用於一鍵移除最後一批畫線
    private int currentBatchId = 0;

    // 鼠標事件相關變量
    private DateTime mouseDownTime = DateTime.MinValue;
    private Point lastMousePosition = Point.Empty;
    private const int MOUSE_IDLE_TIMEOUT_MS = 500; // 鼠標靜止500毫秒後自動完成選擇
    private const int MIN_MOUSE_MOVE_DISTANCE = 5; // 最小鼠標移動距離（像素）

    // 新增：鼠標角度相關變數
    private double mouseSelectionAngle = 0;     // 鼠標選擇範圍的角度
    private bool hasMouseAngle = false;         // 是否有有效的鼠標角度
    private const int MIN_MOUSE_ANGLE_LINES = 5; // 最少生成的鼠標角度加權趨勢線數量
    private const double ANGLE_SIMILARITY_THRESHOLD = 15.0; // 角度相似度閾值（度）
    private const double ANGLE_SIMILARITY_THRESHOLD_RELAXED = 25.0; // 放寬的角度相似度閾值（度）
    private const double MOUSE_ANGLE_BONUS = 50.0; // 鼠標角度匹配的額外分數
    private const double MOUSE_ANGLE_BONUS_RELAXED = 30.0; // 放寬條件下的鼠標角度匹配分數

    // 新增：鼠標提起點作為延長線權重加分相關變數
    private Point mouseReleasePoint = Point.Empty;  // 鼠標提起位置（僅用於調試）
    private int mouseReleaseIndex = -1;              // 鼠標提起位置對應的K線索引
    private double mouseReleasePrice = 0;            // 鼠標提起位置對應的價格
    private bool hasMouseReleaseTarget = false;      // 是否有有效的鼠標提起目標點
    private DateTime mouseReleaseTime = DateTime.MinValue; // 鼠標提起的時間
    private DateTime targetTextShowTime = DateTime.MinValue; // 目標點文字顯示的時間
    private bool showTargetText = false;             // 是否顯示目標點文字
    private const double MOUSE_RELEASE_EXTENSION_TOLERANCE = 15.0; // 延長線經過鼠標提起點的容忍度（像素）
    private const double MOUSE_RELEASE_BONUS_SCORE = 100.0; // 延長線經過鼠標提起點的加分
    private const int TARGET_TEXT_DISPLAY_SECONDS = 3; // 目標點文字顯示秒數

    // 斐波那契曲線相關常數
    private const double GOLDEN_RATIO = 1.618033988749895; // 黃金比例
    private const double FIBONACCI_RATIO = 0.618033988749895; // 斐波那契比例 (1/φ)
    private const double FIBONACCI_RATIO_2 = 0.381966011250105; // 斐波那契比例 (1-1/φ)
    private const int CURVE_SEGMENTS = 50; // 曲線分段數量，用於繪製平滑曲線

    // 優化後的趨勢線參數設定
    private const int PIVOT_LOOKBACK = 3;       // 提高到3根K線，確保更可靠的pivot點
    private const int MIN_TOUCH_POINTS = 3;     // 降低到3個觸點，但提高品質要求
    private const int MIN_TOUCH_POINTS_RANGE = 4; // 範圍線所需的最低觸點數
    private const int MIN_BODY_CROSS_TOLERANCE = 1; // 降低允許的蠟燭實體穿越次數
    private const double MAX_ABS_ANGLE = 61.8;  // 降低最大角度，過濾過陡的線
    private const double MIN_ABS_ANGLE = 21.0;   // 新增最小角度，過濾過平的線
 
    private const int MAX_RANGE_LINES = 200;    // 減少最大範圍線數量
    private const int REGION_SIZE = 15;         // 減小區域大小，提高精確度
    private const double MIN_DISTANCE = 8;      // 增加最小距離，避免重複線條

    private const int MIN_TIME_SPAN = 5;        // 最小時間跨度（K線數量）
    private const int MAX_TIME_SPAN = 60;       // 最大時間跨度（K線數量）

    // 調試：基於BarsWidth的動態觸點閾值調整
    private double GetDynamicTouchThreshold(double price)
    {
        var mainWindow = this.CurrentChart.MainWindow;

        // 獲取BarsWidth和價格比例信息
        int barsWidth = this.CurrentChart.BarsWidth;
        double visibleHighPrice = mainWindow.CoordinatesConverter.GetPrice(mainWindow.ClientRectangle.Top);
        double visibleLowPrice = mainWindow.CoordinatesConverter.GetPrice(mainWindow.ClientRectangle.Bottom);
        double visiblePriceRange = Math.Abs(visibleHighPrice - visibleLowPrice);
        int chartHeight = mainWindow.ClientRectangle.Height;
        double pricePerPixel = visiblePriceRange / chartHeight;

        // 觸點閾值
        double pixelTouchMargin = Math.Max(2.0, barsWidth * 0.2); // 至少2像素，最多BarsWidth的20%
        double dynamicThreshold = pixelTouchMargin * pricePerPixel / price; // 轉換為相對閾值

        // 設置合理的上下限
        double minThreshold = 0.0001; // 最小0.01%
        double maxThreshold = 0.01;   // 最大1%

        dynamicThreshold = Math.Max(minThreshold, Math.Min(maxThreshold, dynamicThreshold));

        Core.Instance.Loggers.Log($"[DEBUG] GetDynamicTouchThreshold (BarsWidth): price={price:F6}, barsWidth={barsWidth}, pixelMargin={pixelTouchMargin:F2}, threshold={dynamicThreshold:F6} ({dynamicThreshold*100:F3}%)");
        return dynamicThreshold;
    }

    

    // 今日趨勢線按鈕
    private Rectangle todayButtonRect;
    private bool isTodayButtonHovered = false;
    private bool showTodayTrendLines = false;
    private List<TrendLine> todayTrendLines = new List<TrendLine>();


     private Indicator LDInd;

    // 區域類別
    private class Region
    {
        public int StartIndex { get; set; }
        public int EndIndex { get; set; }
        public bool HasTrendLine { get; set; }
        public List<TrendLineCandidate> Candidates { get; set; } = new List<TrendLineCandidate>();
    }

    public TrendLineIndicator() : base()
    {
        Name = "Selected Range CurveTrendLineSelection MIX";
        Description = "Draws trend lines in selected range CurveTrendLineSelection_MIX";
        SeparateWindow = false;
    }

    protected override void OnInit()
    {
        // 初始化基本變量
        startPoint = Point.Empty;
        endPoint = Point.Empty;
        currentMousePosition = Point.Empty;
        isDrawing = false;

        // 初始化趨勢線相關變量
        nearestLine = null;
        nearestStraightLine = null;
        nearestCurveLine = null;
        nearestLines.Clear();
        selectedLongLine = null;
        selectedShortLine = null;
        selectedLongLines.Clear();
        selectedShortLines.Clear();
        isSelectedLongLineActive = false;
        isSelectedShortLineActive = false;

        // 初始化按鈕狀態
        isLinesLocked = false;
        showDistance = false;
        showTodayTrendLines = false;
        IsOrderPlaceAllowed = false;

        // 初始化鼠標提起相關變量
        hasMouseReleaseTarget = false;
        mouseReleasePoint = Point.Empty;
        showTargetText = false;
        targetTextShowTime = DateTime.MinValue;

        // 初始化鼠標事件相關變量
        mouseDownTime = DateTime.MinValue;
        lastMousePosition = Point.Empty;

        // 初始化批次計數器
        currentBatchId = 0;

        // 初始化鼠標角度相關變量
        mouseSelectionAngle = 0;
        hasMouseAngle = false;

        // 初始化集合
        if (trendLines == null) trendLines = new List<TrendLine>();
        if (todayTrendLines == null) todayTrendLines = new List<TrendLine>();

        // 清空現有數據
        trendLines.Clear();
        todayTrendLines.Clear();

        // 註冊事件處理器前先取消註冊，避免重複
        this.CurrentChart.MouseDown -= CurrentChart_MouseDown;
        this.CurrentChart.MouseUp -= CurrentChart_MouseUp;
        this.CurrentChart.MouseMove -= CurrentChart_MouseMove;

        // 重新註冊事件處理器
        this.CurrentChart.MouseDown += CurrentChart_MouseDown;
        this.CurrentChart.MouseUp += CurrentChart_MouseUp;
        this.CurrentChart.MouseMove += CurrentChart_MouseMove;

        // 初始化ATR指標
        atr = Core.Indicators.BuiltIn.ATR(14, MaMode.SMA);

        // 初始化按鈕矩形
        buttonRect = new Rectangle(10, 10, 100, 30);

        // 初始化其他指標
        IndicatorInfo LDIndicatorInfo = Core.Instance.Indicators.All.FirstOrDefault(info => info.Name == "MarkBuySell");
        this.LDInd = Core.Instance.Indicators.CreateIndicator(LDIndicatorInfo);

        this.AddIndicator(this.LDInd);

        Core.Instance.Loggers.Log($"[DEBUG] OnInit completed - TrendLineMode: {TrendLineMode}");
    }

    private void CurrentChart_MouseDown(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
    {
        if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Left)
        {
            if (buttonRect.Contains(e.Location))
            {
                isLinesLocked = !isLinesLocked;
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (clearButtonRect.Contains(e.Location))
            {
                RemoveLastTrendLines();
                isLinesLocked = false;  // 清除後自動解鎖
                selectedLongLine = null;  // 清除選中狀態
                selectedShortLine = null;  // 清除選中狀態
                selectedLongLines.Clear();  // 清除多選狀態
                selectedShortLines.Clear();  // 清除多選狀態
                isSelectedLongLineActive = false;
                isSelectedShortLineActive = false;
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (clearAllButtonRect.Contains(e.Location))
            {
                trendLines.Clear();
                isLinesLocked = false;
                selectedLongLine = null;  // 清除選中狀態
                selectedShortLine = null;  // 清除選中狀態
                selectedLongLines.Clear();  // 清除多選狀態
                selectedShortLines.Clear();  // 清除多選狀態
                isSelectedLongLineActive = false;
                isSelectedShortLineActive = false;
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (showDistanceButtonRect.Contains(e.Location))
            {
                showDistance = !showDistance;  // 切換狀態
                if (!showDistance)
                {
                    // 如果關閉選中功能，同時清除所有選中的趨勢線
                    selectedLongLine = null;
                    selectedShortLine = null;
                    selectedLongLines.Clear();
                    selectedShortLines.Clear();
                    isSelectedLongLineActive = false;
                    isSelectedShortLineActive = false;
                }
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (todayButtonRect.Contains(e.Location))
            {
                showTodayTrendLines = !showTodayTrendLines;
                if (showTodayTrendLines)
                {
                    CalculateTodayTrendLines();  
                }
                else
                {
                    // 如果選中的線是來自範圍線，則清除選中狀態
                    if (todayTrendLines.Contains(selectedLongLine))
                    {
                        selectedLongLine = null;
                        isSelectedLongLineActive = false;
                    }
                    if (todayTrendLines.Contains(selectedShortLine))
                    {
                        selectedShortLine = null;
                        isSelectedShortLineActive = false;
                    }
                    todayTrendLines.Clear();
                }
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (orderPlaceButtonRect.Contains(e.Location))
            {
                IsOrderPlaceAllowed = !IsOrderPlaceAllowed;
                this.CurrentChart.RedrawBuffer();
                return;
            }
            else if (nearestLine != null && showDistance)  // 只有在showDistance為true時才允許選中趨勢線
            {
                // 檢查當前價格是否在線上方
                var currentBar = this.HistoricalData[0] as HistoryItemBar;
                if (currentBar != null)
                {
                    var mainWindow = this.CurrentChart.MainWindow;
                    int currentIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(currentBar.TimeLeft);

                    // 在混合模式下，處理多條重疊的趨勢線
                    if (TrendLineMode == 3 && nearestLines.Count > 1)
                    {
                        // 檢查是否同時有直線和弧線
                        var straightLines = nearestLines.Where(l => !l.IsCurve).ToList();
                        var curveLines = nearestLines.Where(l => l.IsCurve).ToList();

                        if (straightLines.Any() && curveLines.Any())
                        {
                            // 檢查所有線的價格位置，確保它們都在同一側
                            bool allAbove = true;
                            bool allBelow = true;
                            bool anyAlreadyCrossed = false;

                            foreach (var line in nearestLines)
                            {
                                double linePrice = CalculateLinePrice(line, currentIndex);
                                bool currentlyAboveLine = currentBar.Close > linePrice;

                                // 檢查前一根K線的位置
                                var previousBar = this.HistoricalData[1] as HistoryItemBar;
                                if (previousBar != null)
                                {
                                    int previousIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(previousBar.TimeLeft);
                                    double previousLinePrice = CalculateLinePrice(line, previousIndex);
                                    bool previouslyAboveLine = previousBar.Close > previousLinePrice;

                                    // 如果已經發生穿越，標記為不可選擇
                                    if (currentlyAboveLine != previouslyAboveLine)
                                    {
                                        anyAlreadyCrossed = true;
                                        break;
                                    }
                                }

                                if (!currentlyAboveLine) allAbove = false;
                                if (currentlyAboveLine) allBelow = false;
                            }

                            if (anyAlreadyCrossed)
                            {
                                Core.Instance.Loggers.Log($"Cannot select overlapping lines as price has already crossed some of them.");
                                return;
                            }

                            // 如果所有線都在同一側，可以同時選擇
                            if (allAbove)
                            {
                                // 價格在所有線上方，設為等待下穿的綠色線組
                                selectedLongLines.Clear();
                                selectedLongLines.AddRange(nearestLines);
                                selectedLongLine = nearestLines.First(); // 保持兼容性
                                isSelectedLongLineActive = true;
                                Core.Instance.Loggers.Log($"Selected {nearestLines.Count} overlapping trend lines for long position (green). Waiting for price to cross down all lines.");
                            }
                            else if (allBelow)
                            {
                                // 價格在所有線下方，設為等待上穿的紅色線組
                                selectedShortLines.Clear();
                                selectedShortLines.AddRange(nearestLines);
                                selectedShortLine = nearestLines.First(); // 保持兼容性
                                isSelectedShortLineActive = true;
                                Core.Instance.Loggers.Log($"Selected {nearestLines.Count} overlapping trend lines for short position (red). Waiting for price to cross up all lines.");
                            }
                            else
                            {
                                Core.Instance.Loggers.Log($"Cannot select overlapping lines as price is not on the same side of all lines.");
                                return;
                            }
                        }
                        else
                        {
                            // 沒有直線和弧線的組合，使用單線邏輯
                            ProcessSingleLineSelection(nearestLine, currentBar, currentIndex);
                        }
                    }
                    else
                    {
                        // 非混合模式或只有一條線，使用原有邏輯
                        ProcessSingleLineSelection(nearestLine, currentBar, currentIndex);
                    }
                }
                this.CurrentChart.RedrawBuffer();
                return;
            }
        }

        if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Right &&
            this.CurrentChart.MainWindow.ClientRectangle.Contains(e.Location) &&
            !isLinesLocked)
        {
            startPoint = e.Location;
            isDrawing = true;
        }
    }

    /// <summary>
    /// 處理單條趨勢線的選擇邏輯
    /// </summary>
    private void ProcessSingleLineSelection(TrendLine line, HistoryItemBar currentBar, int currentIndex)
    {
        double linePrice = CalculateLinePrice(line, currentIndex);
        bool currentlyAboveLine = currentBar.Close > linePrice;

        // 檢查前一根K線的位置
        var previousBar = this.HistoricalData[1] as HistoryItemBar;
        if (previousBar != null)
        {
            var mainWindow = this.CurrentChart.MainWindow;
            int previousIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(previousBar.TimeLeft);
            double previousLinePrice = CalculateLinePrice(line, previousIndex);
            bool previouslyAboveLine = previousBar.Close > previousLinePrice;

            // 如果已經發生穿越，不選擇該線
            if (currentlyAboveLine != previouslyAboveLine)
            {
                Core.Instance.Loggers.Log($"Cannot select this line as price has already crossed it.");
                return;
            }

            // 如果價格在線上方，設為等待下穿的綠色線
            if (currentlyAboveLine)
            {
                if (selectedLongLine == line)
                {
                    selectedLongLine = null;
                    selectedLongLines.Clear();
                    isSelectedLongLineActive = false;
                }
                else
                {
                    selectedLongLine = line;
                    selectedLongLines.Clear();
                    selectedLongLines.Add(line);
                    isSelectedLongLineActive = true;
                    Core.Instance.Loggers.Log($"Selected trend line for long position (green). Waiting for price to cross down.");
                }
            }
            // 如果價格在線下方，設為等待上穿的紅色線
            else
            {
                if (selectedShortLine == line)
                {
                    selectedShortLine = null;
                    selectedShortLines.Clear();
                    isSelectedShortLineActive = false;
                }
                else
                {
                    selectedShortLine = line;
                    selectedShortLines.Clear();
                    selectedShortLines.Add(line);
                    isSelectedShortLineActive = true;
                    Core.Instance.Loggers.Log($"Selected trend line for short position (red). Waiting for price to cross up.");
                }
            }
        }
    }

    private void CurrentChart_MouseUp(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
    {
        if (isDrawing)
        {
            endPoint = e.Location;
            mouseReleasePoint = e.Location; // 記錄鼠標提起位置
            isDrawing = false;

            int previousCount = trendLines.Count;

            // 計算鼠標選擇範圍的角度
            CalculateMouseSelectionAngle();

            // 創建鼠標提起點對應的目標點（用於延長線加分）
            CreateMouseReleaseTarget();

            // 重新計算趨勢線
            CalculateTrendLines();

            if (trendLines.Count > previousCount)
            {
                isLinesLocked = true;
            }

            this.CurrentChart.RedrawBuffer();
        }
    }

    private void CurrentChart_MouseMove(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
    {
        bool wasHovered = isButtonHovered;
        bool wasClearHovered = isClearButtonHovered;
        bool wasClearAllHovered = isClearAllButtonHovered;
        bool wasTodayHovered = isTodayButtonHovered;
        bool wasShowDistanceHovered = isShowDistanceButtonHovered;
        bool wasOrderPlaceHovered = isOrderPlaceButtonHovered;
        
        isButtonHovered = buttonRect.Contains(e.Location);
        isClearButtonHovered = clearButtonRect.Contains(e.Location);
        isClearAllButtonHovered = clearAllButtonRect.Contains(e.Location);
        isTodayButtonHovered = todayButtonRect.Contains(e.Location);
        isShowDistanceButtonHovered = showDistanceButtonRect.Contains(e.Location);
        isOrderPlaceButtonHovered = orderPlaceButtonRect.Contains(e.Location);
        
        if (wasHovered != isButtonHovered || 
            wasClearHovered != isClearButtonHovered || 
            wasClearAllHovered != isClearAllButtonHovered ||
            wasTodayHovered != isTodayButtonHovered ||
            wasShowDistanceHovered != isShowDistanceButtonHovered ||
            wasOrderPlaceHovered != isOrderPlaceButtonHovered)
        {
            this.CurrentChart.RedrawBuffer();
        }

        currentMousePosition = e.Location;  // 更新鼠標位置
        UpdateNearestLine();  // 更新最近的趨勢線

        if (isDrawing)
        {
            endPoint = e.Location;
            this.CurrentChart.RedrawBuffer();
        }
    }

    private void UpdateNearestLine()
    {
        var mainWindow = CurrentChart.MainWindow;
        double minDistance = double.MaxValue;
        double minStraightDistance = double.MaxValue;
        double minCurveDistance = double.MaxValue;

        TrendLine nearest = null;
        nearestStraightLine = null;
        nearestCurveLine = null;
        nearestLines.Clear();

        // 檢查所有趨勢線
        var allLines = trendLines.Concat(showTodayTrendLines ? todayTrendLines : Enumerable.Empty<TrendLine>()).ToList();

        foreach (var line in allLines)
        {
            double distance = GetDistanceToLine(line, currentMousePosition, mainWindow);

            // 添加调试信息
            if (distance < 50) // 更宽的范围用于调试
            {
                Core.Instance.Loggers.Log($"[DEBUG] Line distance: {distance:F2}px, IsCurve: {line.IsCurve}, Mouse: ({currentMousePosition.X}, {currentMousePosition.Y})");
            }

            if (distance < 20) // 20像素的檢測範圍
            {
                // 更新總體最近線
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearest = line;
                }

                // 分別更新最近的直線和弧線
                if (line.IsCurve)
                {
                    if (distance < minCurveDistance)
                    {
                        minCurveDistance = distance;
                        nearestCurveLine = line;
                    }
                }
                else
                {
                    if (distance < minStraightDistance)
                    {
                        minStraightDistance = distance;
                        nearestStraightLine = line;
                    }
                }
            }
        }

        // 設置結果
        nearestLine = nearest;

        // 在混合模式下，必須同時有直線和弧線才能反應
        if (TrendLineMode == 3)
        {
            if (nearestStraightLine != null && nearestCurveLine != null)
            {
                // 同時找到直線和弧線，添加兩條線
                nearestLines.Add(nearestStraightLine);
                nearestLines.Add(nearestCurveLine);
                nearestLine = nearestStraightLine; // 設置主要最近線為直線
                Core.Instance.Loggers.Log($"[DEBUG] Found both straight and curve lines: straight distance={minStraightDistance:F2}px, curve distance={minCurveDistance:F2}px");
            }
            else
            {
                // 混合模式下，如果沒有同時找到兩種線，則不做任何反應
                nearestLine = null;
                nearestLines.Clear();
                if (nearestStraightLine != null)
                {
                    Core.Instance.Loggers.Log($"[DEBUG] Mixed mode: Found only straight line, no reaction");
                }
                else if (nearestCurveLine != null)
                {
                    Core.Instance.Loggers.Log($"[DEBUG] Mixed mode: Found only curve line, no reaction");
                }
                else
                {
                    Core.Instance.Loggers.Log($"[DEBUG] Mixed mode: No lines found within range");
                }
            }
        }
        else
        {
            // 非混合模式，只添加最近的線
            if (nearest != null)
            {
                nearestLines.Add(nearest);
                Core.Instance.Loggers.Log($"[DEBUG] Found nearest line: distance={minDistance:F2}px, IsCurve={nearest.IsCurve}");
            }
            else
            {
                Core.Instance.Loggers.Log($"[DEBUG] No lines found within range");
            }
        }

        this.CurrentChart.RedrawBuffer();
    }





    private double GetDistanceToLine(TrendLine line, Point mousePoint, TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        if (line.IsCurve)
        {
            // 計算到弧線的距離
            return GetDistanceToArc(line, mousePoint, mainWindow);
        }
        else
        {
            // 計算到直線的距離（原有邏輯）
            return GetDistanceToStraightLine(line, mousePoint, mainWindow);
        }
    }

    private double GetDistanceToArc(TrendLine line, Point mousePoint, TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        try
        {
            var startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(line.StartTime);
            var endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(line.EndTime);

            if (startIndex < 0 || endIndex >= HistoricalData.Count || startIndex >= endIndex)
                return double.MaxValue;

            // 找到最近的弧線點
            double minDistance = double.MaxValue;

            // 1. 檢查主弧線段上的多個點
            for (int i = startIndex; i <= endIndex; i += Math.Max(1, (endIndex - startIndex) / 20))
            {
                double price = CalculateArcPrice(line, i);
                float x = (float)mainWindow.CoordinatesConverter.GetChartX(
                    HistoricalData[i, SeekOriginHistory.Begin].TimeLeft);
                float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);

                double distance = Math.Sqrt(Math.Pow(mousePoint.X - x, 2) + Math.Pow(mousePoint.Y - y, 2));
                minDistance = Math.Min(minDistance, distance);
            }

            // 2. 檢查左延長線
            int leftExtendIndex = Math.Max(0, startIndex - 300);
            if (leftExtendIndex < startIndex)
            {
                for (int i = leftExtendIndex; i < startIndex; i += Math.Max(1, (startIndex - leftExtendIndex) / 20))
                {
                    if (i >= 0 && i < HistoricalData.Count)
                    {
                        double price = CalculateArcPrice(line, i);
                        float x = (float)mainWindow.CoordinatesConverter.GetChartX(
                            HistoricalData[i, SeekOriginHistory.Begin].TimeLeft);
                        float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);

                        double distance = Math.Sqrt(Math.Pow(mousePoint.X - x, 2) + Math.Pow(mousePoint.Y - y, 2));
                        minDistance = Math.Min(minDistance, distance);
                    }
                }
            }

            // 3. 檢查右延長線 - 支持延伸到未來
            float rightX = mainWindow.ClientRectangle.Right;
            DateTime rightTime = mainWindow.CoordinatesConverter.GetTime(rightX);
            int rightExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(rightTime);

            if (rightExtendIndex > endIndex)
            {
                int maxExtendIndex = rightExtendIndex;
                for (int i = endIndex + 1; i <= maxExtendIndex; i += Math.Max(1, (maxExtendIndex - endIndex) / 20))
                {
                    // 計算弧線價格（即使超出歷史數據範圍）
                    double price = CalculateArcPrice(line, i);

                    // 計算時間點（可能是未來時間）
                    DateTime timePoint;
                    if (i < HistoricalData.Count)
                    {
                        // 在歷史數據範圍內
                        timePoint = HistoricalData[i, SeekOriginHistory.Begin].TimeLeft;
                    }
                    else
                    {
                        // 超出歷史數據範圍，推算未來時間
                        var lastBar = HistoricalData[HistoricalData.Count - 1, SeekOriginHistory.Begin];
                        var secondLastBar = HistoricalData[HistoricalData.Count - 2, SeekOriginHistory.Begin];
                        TimeSpan barInterval = lastBar.TimeLeft - secondLastBar.TimeLeft;
                        int futureSteps = i - (HistoricalData.Count - 1);
                        timePoint = lastBar.TimeLeft.Add(TimeSpan.FromTicks(barInterval.Ticks * futureSteps));
                    }

                    float x = (float)mainWindow.CoordinatesConverter.GetChartX(timePoint);
                    float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);

                    double distance = Math.Sqrt(Math.Pow(mousePoint.X - x, 2) + Math.Pow(mousePoint.Y - y, 2));
                    minDistance = Math.Min(minDistance, distance);
                }
            }

            return minDistance;
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] GetDistanceToArc: {ex.Message}");
            return GetDistanceToStraightLine(line, mousePoint, mainWindow);
        }
    }

    private double GetDistanceToStraightLine(TrendLine line, Point mousePoint, TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        // 獲取線的起點和終點座標
        float x1 = (float)mainWindow.CoordinatesConverter.GetChartX(line.StartTime);
        float x2 = (float)mainWindow.CoordinatesConverter.GetChartX(line.EndTime);
        float y1 = (float)mainWindow.CoordinatesConverter.GetChartY(
            line.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(line.StartTime) + line.Intercept);
        float y2 = (float)mainWindow.CoordinatesConverter.GetChartY(
            line.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(line.EndTime) + line.Intercept);

        // 計算點到線段的距離
        double distance = PointToLineDistance(mousePoint.X, mousePoint.Y, x1, y1, x2, y2);

        // 檢查延伸線 - 修改為可以延伸到未來
        int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(line.StartTime);
        int leftExtendIndex = Math.Max(0, startIndex - 300);
        float leftX = (float)mainWindow.CoordinatesConverter.GetChartX(
            HistoricalData[leftExtendIndex, SeekOriginHistory.Begin].TimeLeft);
        float leftY = (float)mainWindow.CoordinatesConverter.GetChartY(
            line.Slope * leftExtendIndex + line.Intercept);

        int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(line.EndTime);

        // 修改：右延長線可以延伸到圖表右邊界，不受歷史數據限制
        float rightX = mainWindow.ClientRectangle.Right; // 直接延伸到圖表右邊界
        DateTime rightTime = mainWindow.CoordinatesConverter.GetTime(rightX);
        int rightExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(rightTime);
        float rightY = (float)mainWindow.CoordinatesConverter.GetChartY(
            line.Slope * rightExtendIndex + line.Intercept);

        // 檢查左延伸線距離
        double leftDistance = PointToLineDistance(mousePoint.X, mousePoint.Y, leftX, leftY, x1, y1);
        // 檢查右延伸線距離
        double rightDistance = PointToLineDistance(mousePoint.X, mousePoint.Y, x2, y2, rightX, rightY);

        // 返回最小距離
        return Math.Min(Math.Min(distance, leftDistance), rightDistance);
    }

    private double PointToLineDistance(double px, double py, double x1, double y1, double x2, double y2)
    {
        double A = px - x1;
        double B = py - y1;
        double C = x2 - x1;
        double D = y2 - y1;

        double dot = A * C + B * D;
        double len_sq = C * C + D * D;

        double param = -1;
        if (len_sq != 0)
            param = dot / len_sq;

        double xx, yy;

        if (param < 0)
        {
            xx = x1;
            yy = y1;
        }
        else if (param > 1)
        {
            xx = x2;
            yy = y2;
        }
        else
        {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        double dx = px - xx;
        double dy = py - yy;
        return Math.Sqrt(dx * dx + dy * dy);
    }
private bool IsSameSymbol(Symbol a, Symbol b)
        {
            if (a == null || b == null)
            {
                return false;
            }

            // 獲取基礎商品代碼
            string aId = GetTradableSymbolId(a);
            string bId = GetTradableSymbolId(b);
            
            // 嘗試多種匹配方式
            bool isMatch = (a.Id == b.Id) ||  // 通過完整ID匹配
                          (a.Name == b.Name && a.ConnectionId == b.ConnectionId) ||  // 通過完整名稱和連接ID匹配
                          (a.CreateInfo().Equals(b.CreateInfo())) ||  // 通過CreateInfo匹配
                          (aId == bId && a.ConnectionId == b.ConnectionId);  // 通過基礎商品代碼匹配

            return isMatch;
        }

        // 重載 GetTradableSymbolId 方法，接受 Symbol 參數
        private string GetTradableSymbolId(Symbol symbol)
        {
            var symbolId = symbol.AdditionalInfo != null && 
                          symbol.AdditionalInfo.TryGetItem(Symbol.TRADING_SYMBOL_ID, out var item) ? 
                          item.Value.ToString() : 
                          symbol.Id;
            int atIndex = symbolId.IndexOf('@');
            string tradableSymbolId = atIndex > 0 ? symbolId.Substring(0, atIndex) : symbolId;
            return tradableSymbolId;
        }

        private bool IsSameAccount(Account a, Account b)
        {
            if (a == null || b == null)
            {
                //this.Log($"[{this.CurrentSymbol?.Name}] Account comparison failed: null check", StrategyLoggingLevel.Trading);
                return false;
            }

            // 記錄詳細的比較信息
            //this.Log($"[{this.CurrentSymbol.Name}] Comparing accounts - A: {a.Name}({a.Id}/{a.ConnectionId}), B: {b.Name}({b.Id}/{b.ConnectionId})", StrategyLoggingLevel.Trading);
            
            // 嘗試多種匹配方式
            bool isMatch = (a.Id == b.Id) ||  // 通過ID匹配
                          (a.Name == b.Name && a.ConnectionId == b.ConnectionId) ||  // 通過名稱和連接ID匹配
                          (a.CreateInfo().Equals(b.CreateInfo()));  // 通過CreateInfo匹配

            /*if (isMatch)
                this.Log($"[{this.CurrentSymbol.Name}] Account matched", StrategyLoggingLevel.Trading);
            else
                this.Log($"[{this.CurrentSymbol.Name}] Account not matched", StrategyLoggingLevel.Trading);
            */
            
            return isMatch;
        }

    private Position[] GetPositions()
        {
            // 先獲取所有倉位
            var allPositions = Core.Instance.Positions.ToArray();
            //this.Log($"[{this.CurrentSymbol.Name}] Total positions in Core: {allPositions.Length}", StrategyLoggingLevel.Trading);

            // 遍歷並記錄每個倉位的詳細信息
            foreach (var pos in allPositions)
            {
                //this.Log($"[{this.CurrentSymbol.Name}] Found position - Symbol: {pos.Symbol.Name}({pos.Symbol.Id}/{pos.Symbol.ConnectionId}), Account: {pos.Account.Name}({pos.Account.Id}/{pos.Account.ConnectionId})", StrategyLoggingLevel.Trading);
            }

            // 過濾倉位
            var filteredPositions = allPositions
                .Where(x => IsSameSymbol(x.Symbol, this.Symbol) && 
                           IsSameAccount(x.Account, this.CurrentChart.Account))
                .ToArray();

            //this.Log($"[{this.CurrentSymbol.Name}] Filtered positions count: {filteredPositions.Length}", StrategyLoggingLevel.Trading);
            return filteredPositions;
        }

    protected override void OnUpdate(UpdateArgs args)
    {
        if (!isSelectedLongLineActive && !isSelectedShortLineActive) return;

        try
        {
            bool IsBottom = this.LDInd.GetValue(0,2) > 0;
            bool IsTop = this.LDInd.GetValue(0,3) > 0;
            bool hasPosition = GetPositions().Any();

            


            // 獲取當前K線數據
            var currentBar = this.HistoricalData[0] as HistoryItemBar;
            if (currentBar == null) return;

            var mainWindow = this.CurrentChart.MainWindow;
            int currentIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(currentBar.TimeLeft);

            // 檢查綠色線（等待下穿）
            if (isSelectedLongLineActive && selectedLongLines.Any())
            {
                if (TrendLineMode == 3 && selectedLongLines.Count > 1)
                {
                    // 混合模式：檢查是否所有線都被穿越
                    bool allLinesCrossed = true;
                    bool anyLineCrossed = false;

                    foreach (var line in selectedLongLines)
                    {
                        double linePrice = CalculateLinePrice(line, currentIndex);
                        bool isCurrentlyAboveLine = currentBar.Close > linePrice;

                        if (isCurrentlyAboveLine)
                        {
                            allLinesCrossed = false; // 還有線沒被穿越
                        }
                        else
                        {
                            anyLineCrossed = true; // 至少有一條線被穿越
                        }
                    }

                    if (hasPosition)
                    {
                        if (anyLineCrossed)  // 任何一條線被穿越就平倉
                        {
                            Core.Instance.Loggers.Log($"Price crossed DOWN at least one of {selectedLongLines.Count} selected long trend lines at {currentBar.TimeLeft}" +
                                $"\nCross Price: {currentBar.Close}");
                            CloseAllPositions();
                            isSelectedLongLineActive = false;
                            selectedLongLine = null;
                            selectedLongLines.Clear();
                            this.CurrentChart.RedrawBuffer();
                        }
                    }
                    else if (IsOrderPlaceAllowed) // 只有在允許開倉時才執行
                    {
                        if (allLinesCrossed) // 所有線都被穿越才開倉
                        {
                            // 使用第一條線的價格作為參考
                            double referencePrice = CalculateLinePrice(selectedLongLines.First(), currentIndex);
                            var todayATR = GetPercentATR();
                            var SL = this.Symbol.CalculateTicks(referencePrice - todayATR, referencePrice);
                            SendOrderStrategyMarket("Buy", SL);
                            isSelectedLongLineActive = false;
                            selectedLongLine = null;
                            selectedLongLines.Clear();
                            this.CurrentChart.RedrawBuffer();
                            Core.Instance.Loggers.Log($"Opened BUY position after crossing ALL {selectedLongLines.Count} selected long trend lines");
                        }
                    }
                }
                else
                {
                    // 單線模式或非混合模式
                    var line = selectedLongLine ?? selectedLongLines.FirstOrDefault();
                    if (line != null)
                    {
                        double longLinePrice = CalculateLinePrice(line, currentIndex);
                        bool isCurrentlyAboveLongLine = currentBar.Close > longLinePrice;

                        if (hasPosition)
                        {
                            if (!isCurrentlyAboveLongLine)  // 發生下穿
                            {
                                Core.Instance.Loggers.Log($"Price crossed DOWN selected long trend line at {currentBar.TimeLeft}" +
                                    $"\nCross Price: {currentBar.Close}");
                                CloseAllPositions();
                                isSelectedLongLineActive = false;
                                selectedLongLine = null;
                                selectedLongLines.Clear();
                                this.CurrentChart.RedrawBuffer();
                            }
                        }
                        else if (IsOrderPlaceAllowed) // 只有在允許開倉時才執行
                        {
                            if (currentBar.High >= longLinePrice && currentBar.Low <= longLinePrice)
                            {
                                var todayATR = GetPercentATR();
                                var SL = this.Symbol.CalculateTicks(longLinePrice - todayATR, longLinePrice);
                                SendOrderStrategyMarket("Buy", SL);
                                isSelectedLongLineActive = false;
                                selectedLongLine = null;
                                selectedLongLines.Clear();
                                this.CurrentChart.RedrawBuffer();
                            }
                        }
                    }
                }
            }

            // 檢查紅色線（等待上穿）
            if (isSelectedShortLineActive && selectedShortLines.Any())
            {
                if (TrendLineMode == 3 && selectedShortLines.Count > 1)
                {
                    // 混合模式：檢查是否所有線都被穿越
                    bool allLinesCrossed = true;
                    bool anyLineCrossed = false;

                    foreach (var line in selectedShortLines)
                    {
                        double linePrice = CalculateLinePrice(line, currentIndex);
                        bool isCurrentlyAboveLine = currentBar.Close > linePrice;

                        if (!isCurrentlyAboveLine)
                        {
                            allLinesCrossed = false; // 還有線沒被穿越
                        }
                        else
                        {
                            anyLineCrossed = true; // 至少有一條線被穿越
                        }
                    }

                    if (hasPosition)
                    {
                        if (anyLineCrossed)  // 任何一條線被穿越就平倉
                        {
                            Core.Instance.Loggers.Log($"Price crossed UP at least one of {selectedShortLines.Count} selected short trend lines at {currentBar.TimeLeft}" +
                                $"\nCross Price: {currentBar.Close}");
                            CloseAllPositions();
                            isSelectedShortLineActive = false;
                            selectedShortLine = null;
                            selectedShortLines.Clear();
                            this.CurrentChart.RedrawBuffer();
                        }
                    }
                    else if (IsOrderPlaceAllowed) // 只有在允許開倉時才執行
                    {
                        if (allLinesCrossed) // 所有線都被穿越才開倉
                        {
                            // 使用第一條線的價格作為參考
                            double referencePrice = CalculateLinePrice(selectedShortLines.First(), currentIndex);
                            var todayATR = GetPercentATR();
                            var SL = this.Symbol.CalculateTicks(referencePrice, referencePrice + todayATR);
                            SendOrderStrategyMarket("Sell", SL);
                            isSelectedShortLineActive = false;
                            selectedShortLine = null;
                            selectedShortLines.Clear();
                            this.CurrentChart.RedrawBuffer();
                            Core.Instance.Loggers.Log($"Opened SELL position after crossing ALL {selectedShortLines.Count} selected short trend lines");
                        }
                    }
                }
                else
                {
                    // 單線模式或非混合模式
                    var line = selectedShortLine ?? selectedShortLines.FirstOrDefault();
                    if (line != null)
                    {
                        double shortLinePrice = CalculateLinePrice(line, currentIndex);
                        bool isCurrentlyAboveShortLine = currentBar.Close > shortLinePrice;

                        if (hasPosition)
                        {
                            if (isCurrentlyAboveShortLine)  // 發生上穿
                            {
                                Core.Instance.Loggers.Log($"Price crossed UP selected short trend line at {currentBar.TimeLeft}" +
                                    $"\nCross Price: {currentBar.Close}");
                                CloseAllPositions();
                                isSelectedShortLineActive = false;
                                selectedShortLine = null;
                                selectedShortLines.Clear();
                                this.CurrentChart.RedrawBuffer();
                            }
                        }
                        else if (IsOrderPlaceAllowed) // 只有在允許開倉時才執行
                        {
                            if (currentBar.High >= shortLinePrice && currentBar.Low <= shortLinePrice)
                            {
                                var todayATR = GetPercentATR();
                                var SL = this.Symbol.CalculateTicks(shortLinePrice, shortLinePrice + todayATR);
                                SendOrderStrategyMarket("Sell", SL);
                                isSelectedShortLineActive = false;
                                selectedShortLine = null;
                                selectedShortLines.Clear();
                                this.CurrentChart.RedrawBuffer();
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"Error in OnUpdate: {ex.Message}");
        }
    }

    public override void Dispose()
    {
        if (this.CurrentChart != null)
        {
            this.CurrentChart.MouseDown -= CurrentChart_MouseDown;
            this.CurrentChart.MouseUp -= CurrentChart_MouseUp;
            this.CurrentChart.MouseMove -= CurrentChart_MouseMove;
        }
        this.LDInd.Dispose();
        this.RemoveIndicator(this.LDInd);
        base.Dispose();
    }

    #region PivotPoint / TrendLine 類別

    private class PivotPoint
    {
        public int Index { get; set; }
        public PriceType PriceType { get; set; }
        public double Price { get; set; }
    }

    // 波段類別
    private class WaveSegment
    {
        public PivotPoint StartPivot { get; set; }
        public PivotPoint EndPivot { get; set; }
        public bool IsUpWave { get; set; }  // true 表示上漲波段，false 表示下跌波段
        public List<TrendLine> TrendLines { get; set; } = new List<TrendLine>();
    }

    // 趨勢線類別 - 支持斐波那契弧線和直線
    private class TrendLine
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public PriceType StartPriceType { get; set; }
        public PriceType EndPriceType { get; set; }
        public double Slope { get; set; }
        public double Intercept { get; set; }
        public int BatchId { get; set; }

        // 斐波那契弧線相關屬性（僅在曲線模式下使用）
        public bool IsCurve { get; set; } = true;  // 根據TrendLineMode設置
        public double CurvatureCoefficient { get; set; } = 0.0; // 弧線係數 (二次項係數)
        public double LinearCoefficient { get; set; } = 0.0;    // 線性係數 (一次項係數)
        public double ConstantTerm { get; set; } = 0.0;         // 常數項
        public double FibonacciCurvature { get; set; } = 0.618; // 斐波那契曲率強度
        public double HorizontalExtensionPrice { get; set; } = 0.0; // 水平延伸的目標價格
        public Color CustomColor { get; set; } = Color.Empty; // 自定義顏色，用於區分不同的斐波那契強度
    }

    #endregion

    #region 趨勢線計算主流程

    private void CalculateTrendLines()
    {
        // 調試：輸出商品詳細信息
        LogSymbolDebugInfo();

        var mainWindow = this.CurrentChart.MainWindow;
        DateTime startTime = mainWindow.CoordinatesConverter.GetTime(startPoint.X);
        DateTime endTime = mainWindow.CoordinatesConverter.GetTime(endPoint.X);
        double targetPrice = mainWindow.CoordinatesConverter.GetPrice(endPoint.Y);

        // 調試：記錄商品信息和價格範圍
        var currentBar = this.HistoricalData[0] as HistoryItemBar;
        string symbolName = this.Symbol?.Name ?? "Unknown";
        double currentPrice = currentBar?.Close ?? 0;
        Core.Instance.Loggers.Log($"[DEBUG] CalculateTrendLines - Symbol: {symbolName}, Current Price: {currentPrice:F6}");
        Core.Instance.Loggers.Log($"[DEBUG] Target Price: {targetPrice:F6}, Price Range: {Math.Abs(targetPrice - currentPrice):F6}");

        // 計算鼠標選擇範圍的角度
        CalculateMouseSelectionAngle();

        currentBatchId++;

        // 1) 找出 [startIndex, endIndex] 區間內的 pivot points
        int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(startTime);
        int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(endTime);

        Core.Instance.Loggers.Log($"[DEBUG] Search range: startIndex={startIndex}, endIndex={endIndex}, span={endIndex - startIndex}");

        List<PivotPoint> pivotPoints = FindAllPivotPoints(startIndex, endIndex);
        Core.Instance.Loggers.Log($"[DEBUG] Found {pivotPoints.Count} pivot points");

        // 調試：記錄前幾個pivot點的詳細信息
        for (int i = 0; i < Math.Min(5, pivotPoints.Count); i++)
        {
            var pivot = pivotPoints[i];
            Core.Instance.Loggers.Log($"[DEBUG] Pivot {i}: Index={pivot.Index}, Type={pivot.PriceType}, Price={pivot.Price:F6}");
        }

        // 2) 將 pivotPoints 兩兩配對，嘗試連線
        List<TrendLineCandidate> candidates = new List<TrendLineCandidate>();
        int totalPairs = 0;
        int validCandidates = 0;

        for (int i = 0; i < pivotPoints.Count - 1; i++)
        {
            for (int j = i + 1; j < pivotPoints.Count; j++)
            {
                totalPairs++;
                // 嘗試生成一條趨勢線
                var cand = CreateCandidateLine(pivotPoints[i], pivotPoints[j]);
                if (cand == null)
                    continue;

                validCandidates++;
                // 打分
                double score = ScoreCandidate(cand);
                cand.Score = score;

                // 如果分數大於 0，視為候選
                if (score > 0)
                    candidates.Add(cand);

                // 調試：記錄前幾個候選線的詳細信息
                if (validCandidates <= 5)
                {
                    Core.Instance.Loggers.Log($"[DEBUG] Candidate {validCandidates}: score={score:F2}, touches={cand.TouchIndices.Count}, crosses={cand.CrossedBodies}");
                }
            }
        }

        Core.Instance.Loggers.Log($"[DEBUG] Processed {totalPairs} pivot pairs, {validCandidates} valid candidates, {candidates.Count} scored candidates");

        // 3) 優化的趨勢線選擇邏輯 - 優先選擇鼠標角度匹配的趨勢線
        var bestLines = SelectTrendLinesWithMouseAnglePriority(candidates);
        Core.Instance.Loggers.Log($"[DEBUG] Selected {bestLines.Count} best lines after priority selection");

        // 4) 進一步過濾重複或相似的線條
        bestLines = FilterSimilarLines(bestLines);
        Core.Instance.Loggers.Log($"[DEBUG] Final {bestLines.Count} lines after filtering similar lines");

        // 5) 加入到 trendLines
        foreach (var c in bestLines)
        {
            AddTrendLine(c.StartPoint, c.EndPoint);
        }

        Core.Instance.Loggers.Log($"[DEBUG] CalculateTrendLines completed. Total trend lines: {trendLines.Count}");
    }

    /// <summary>
    /// 過濾相似的趨勢線
    /// </summary>
    private List<TrendLineCandidate> FilterSimilarLines(List<TrendLineCandidate> candidates)
    {
        var filtered = new List<TrendLineCandidate>();

        foreach (var candidate in candidates)
        {
            bool isSimilar = false;

            foreach (var existing in filtered)
            {
                // 檢查起點和終點是否太接近
                if (Math.Abs(candidate.StartPoint.Index - existing.StartPoint.Index) <= MIN_DISTANCE &&
                    Math.Abs(candidate.EndPoint.Index - existing.EndPoint.Index) <= MIN_DISTANCE)
                {
                    // 檢查斜率是否相似
                    double slope1 = (candidate.EndPoint.Price - candidate.StartPoint.Price) /
                                   (candidate.EndPoint.Index - candidate.StartPoint.Index);
                    double slope2 = (existing.EndPoint.Price - existing.StartPoint.Price) /
                                   (existing.EndPoint.Index - existing.StartPoint.Index);

                    if (Math.Abs(slope1 - slope2) / Math.Max(Math.Abs(slope1), Math.Abs(slope2)) < 0.1)
                    {
                        isSimilar = true;
                        break;
                    }
                }
            }

            if (!isSimilar)
            {
                filtered.Add(candidate);
            }
        }

        return filtered;
    }

    #endregion

    #region 鼠標角度計算和趨勢線優先選擇

    /// <summary>
    /// 計算鼠標選擇範圍的角度（使用像素坐標，與CalculateDragAngle保持一致）
    /// </summary>
    private void CalculateMouseSelectionAngle()
    {
        try
        {
            var mainWindow = this.CurrentChart.MainWindow;

            // 使用與CalculateDragAngle相同的計算方式
            double deltaX = endPoint.X - startPoint.X;
            double deltaY = endPoint.Y - startPoint.Y;

            if (deltaX == 0)
            {
                // 修正Y軸方向：屏幕向下為正，但價格向上為正
                mouseSelectionAngle = deltaY < 0 ? 90 : -90;  // 注意這裡反轉了符號
                hasMouseAngle = true;
                return;
            }

            double pixelSlope = Math.Abs(deltaY) / Math.Abs(deltaX);
            double angle = Math.Atan(pixelSlope) * 180.0 / Math.PI;

            // 修正角度符號以匹配趨勢線角度
            if (deltaY > 0) angle = -angle;  // 向下拖拽 = 下降趨勢 = 負角度

            mouseSelectionAngle = angle;
            hasMouseAngle = true;

            Core.Instance.Loggers.Log($"[DEBUG] Mouse selection angle calculated: {mouseSelectionAngle:F2}° (deltaX={deltaX:F1}, deltaY={deltaY:F1}, pixelSlope={pixelSlope:F4})");
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CalculateMouseSelectionAngle: {ex.Message}");
            hasMouseAngle = false;
        }
    }

    /// <summary>
    /// 計算趨勢線角度與鼠標角度的相似度
    /// </summary>
    private double CalculateAngleSimilarity(double trendLineAngle, bool useRelaxedThreshold = false)
    {
        if (!hasMouseAngle) return 0;

        double angleDifference = Math.Abs(trendLineAngle - mouseSelectionAngle);

        // 處理角度環繞（例如 -179度 和 179度 實際上很接近）
        if (angleDifference > 180)
            angleDifference = 360 - angleDifference;

        // 選擇使用的閾值和獎勵分數
        double threshold = useRelaxedThreshold ? ANGLE_SIMILARITY_THRESHOLD_RELAXED : ANGLE_SIMILARITY_THRESHOLD;
        double bonus = useRelaxedThreshold ? MOUSE_ANGLE_BONUS_RELAXED : MOUSE_ANGLE_BONUS;

        // 如果角度差異在閾值內，返回相似度分數
        if (angleDifference <= threshold)
        {
            // 角度越接近，分數越高
            double similarity = (threshold - angleDifference) / threshold;
            return similarity * bonus;
        }

        return 0;
    }

    /// <summary>
    /// 優先選擇與鼠標角度匹配的趨勢線
    /// </summary>
    private List<TrendLineCandidate> SelectTrendLinesWithMouseAnglePriority(List<TrendLineCandidate> candidates)
    {
        if (!hasMouseAngle || candidates.Count == 0)
        {
            // 如果沒有鼠標角度，使用原來的邏輯
            return candidates
                .OrderByDescending(c => c.Score)
                .Take(30)
                .Where(c => c.Score > 50)
                .ToList();
        }

        Core.Instance.Loggers.Log($"Mouse angle: {mouseSelectionAngle:F2}°, Processing {candidates.Count} candidates");

        // 為所有候選線計算角度匹配分數（使用嚴格閾值）
        foreach (var candidate in candidates)
        {
            // 使用正確的像素坐標角度計算（參考GetCandidateAngle）
            double angle = GetCandidateAngle(candidate);

            double angleSimilarityScore = CalculateAngleSimilarity(angle, false); // 使用嚴格閾值
            candidate.Score += angleSimilarityScore; // 為匹配的角度加分
        }

        // 第一輪：使用嚴格閾值找角度匹配的線
        var strictAngleMatchedLines = candidates
            .Where(c => CalculateAngleSimilarity(GetCandidateAngle(c), false) > 0)
            .OrderByDescending(c => c.Score)
            .ToList();

        var selectedLines = new List<TrendLineCandidate>();
        int targetAngleLines = MIN_MOUSE_ANGLE_LINES;

        // 如果嚴格匹配的線不足5條，使用放寬的閾值
        if (strictAngleMatchedLines.Count < targetAngleLines)
        {
            Core.Instance.Loggers.Log($"Strict angle matching found only {strictAngleMatchedLines.Count} lines, using relaxed threshold");

            // 重新計算所有候選線的角度匹配分數（使用放寬閾值）
            foreach (var candidate in candidates)
            {
                // 先移除之前的角度分數
                double previousAngleScore = CalculateAngleSimilarity(GetCandidateAngle(candidate), false);
                candidate.Score -= previousAngleScore;

                // 添加放寬條件的角度分數
                double relaxedAngleScore = CalculateAngleSimilarity(GetCandidateAngle(candidate), true);
                candidate.Score += relaxedAngleScore;
            }

            var relaxedAngleMatchedLines = candidates
                .Where(c => CalculateAngleSimilarity(GetCandidateAngle(c), true) > 0)
                .OrderByDescending(c => c.Score)
                .ToList();

            // 優先選擇嚴格匹配的線，然後補充放寬匹配的線
            selectedLines.AddRange(strictAngleMatchedLines);

            int needMore = targetAngleLines - strictAngleMatchedLines.Count;
            var additionalLines = relaxedAngleMatchedLines
                .Where(c => !strictAngleMatchedLines.Contains(c))
                .Take(needMore)
                .ToList();

            selectedLines.AddRange(additionalLines);

            Core.Instance.Loggers.Log($"Added {strictAngleMatchedLines.Count} strict + {additionalLines.Count} relaxed angle-matched lines");
        }
        else
        {
            // 嚴格匹配的線已經足夠，選擇至少5條（或全部如果超過5條）
            int angleLinesToTake = Math.Max(targetAngleLines, strictAngleMatchedLines.Count);
            selectedLines.AddRange(strictAngleMatchedLines.Take(angleLinesToTake));
            Core.Instance.Loggers.Log($"Selected {selectedLines.Count} strict angle-matched lines");
        }

        // 添加其他高分線條，總數不超過30條
        var otherLines = candidates
            .Where(c => !selectedLines.Contains(c))
            .Where(c => CalculateAngleSimilarity(GetCandidateAngle(c), true) == 0) // 完全不匹配角度的線
            .OrderByDescending(c => c.Score)
            .Where(c => c.Score > 50)
            .ToList();

        int remainingSlots = 30 - selectedLines.Count;
        if (remainingSlots > 0)
        {
            selectedLines.AddRange(otherLines.Take(remainingSlots));
        }

        int finalAngleMatchedCount = selectedLines.Count(c => CalculateAngleSimilarity(GetCandidateAngle(c), true) > 0);
        int finalOtherCount = selectedLines.Count - finalAngleMatchedCount;

        Core.Instance.Loggers.Log($"Final selection: {finalAngleMatchedCount} angle-matched + {finalOtherCount} other lines = {selectedLines.Count} total");

        return selectedLines;
    }

    /// <summary>
    /// 獲取候選趨勢線的角度
    /// </summary>
    private double GetCandidateAngle(TrendLineCandidate candidate)
    {
        try
        {
            var mainWindow = this.CurrentChart.MainWindow;
            if (mainWindow == null) return 0;

            // 將索引轉換為像素座標
            int barsWidth = this.CurrentChart.BarsWidth;
            double pixelTimeSpan = (candidate.EndPoint.Index - candidate.StartPoint.Index) * barsWidth;

            // 將價格轉換為像素座標
            double startY = mainWindow.CoordinatesConverter.GetChartY(candidate.StartPoint.Price);
            double endY = mainWindow.CoordinatesConverter.GetChartY(candidate.EndPoint.Price);
            double pixelPriceChange = Math.Abs(endY - startY);

            // 計算實際的像素斜率
            if (pixelTimeSpan == 0) return 0;
            double pixelSlope = pixelPriceChange / pixelTimeSpan;

            // 計算角度（以度為單位）
            double angleRad = Math.Atan(pixelSlope);
            double angleDeg = angleRad * 180.0 / Math.PI;

            // 調試：記錄角度計算詳情
            Core.Instance.Loggers.Log($"[DEBUG] Angle calculation: priceChange={candidate.EndPoint.Price - candidate.StartPoint.Price:F6}, " +
                                    $"timeSpan={candidate.EndPoint.Index - candidate.StartPoint.Index}, " +
                                    $"pixelPriceChange={pixelPriceChange:F2}, pixelTimeSpan={pixelTimeSpan:F2}, " +
                                    $"pixelSlope={pixelSlope:F6}, angle={angleDeg:F2}°");

            return angleDeg;
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] GetCandidateAngle: {ex.Message}");
            return 0;
        }
    }

    #endregion

    #region 生成候選趨勢線＋檢查

    private class TrendLineCandidate
    {
        public PivotPoint StartPoint { get; set; }
        public PivotPoint EndPoint { get; set; }
        public List<int> TouchIndices { get; set; } = new List<int>();
        public double Score { get; set; }
        public int CrossedBodies { get; set; } // 多少根蠟燭實體被穿越
        public double EndpointTypeWeight { get; set; } // 端點類型權重
        public double TouchQualityScore { get; set; } // 觸點品質分數
        public double TimeSpanScore { get; set; } // 時間跨度分數
        public double AngleScore { get; set; } // 角度分數
    }

    /// <summary>
    /// 建立一個趨勢線候選者（優化版）
    /// </summary>
    private TrendLineCandidate CreateCandidateLine(PivotPoint p1, PivotPoint p2)
    {
        int timeSpan = p2.Index - p1.Index;

        // 檢查時間跨度
        if (timeSpan < MIN_TIME_SPAN || timeSpan > MAX_TIME_SPAN)
        {
            // 調試：記錄時間跨度過濾（僅記錄前幾個失敗案例）
            if (timeSpan < MIN_TIME_SPAN)
                Core.Instance.Loggers.Log($"[DEBUG] CreateCandidateLine: timeSpan too small ({timeSpan} < {MIN_TIME_SPAN})");
            else
                Core.Instance.Loggers.Log($"[DEBUG] CreateCandidateLine: timeSpan too large ({timeSpan} > {MAX_TIME_SPAN})");
            return null;
        }

        // 計算端點類型權重，平衡6端點的多樣性
        double endpointWeight = GetEndpointTypeWeight(p1.PriceType, p2.PriceType);
        if (endpointWeight < 0.6)  // 適中的權重門檻，保持6端點的有效性
        {
            Core.Instance.Loggers.Log($"[DEBUG] CreateCandidateLine: endpointWeight too low ({endpointWeight:F3} < 0.6) for {p1.PriceType}-{p2.PriceType}");
            return null;
        }

        // 計算斜率（仍需要用於線性方程）
        double slope = (p2.Price - p1.Price) / timeSpan;

        // 創建臨時候選線來計算正確的像素角度
        var tempCandidate = new TrendLineCandidate
        {
            StartPoint = p1,
            EndPoint = p2
        };

        // 使用正確的像素坐標角度計算
        double angle = GetCandidateAngle(tempCandidate);

        // 使用動態角度過濾 - 根據價格水平調整角度限制
        double avgPrice = (p1.Price + p2.Price) / 2;
        double minAngle, maxAngle;

        minAngle = MIN_ABS_ANGLE;
        maxAngle = MAX_ABS_ANGLE;

        if (Math.Abs(angle) > maxAngle || Math.Abs(angle) < minAngle)
        {
            Core.Instance.Loggers.Log($"[DEBUG] CreateCandidateLine: angle out of range ({angle:F2}° not in [{minAngle:F2}, {maxAngle:F2}]) for avgPrice={avgPrice:F2}");
            return null;
        }

        // 檢查是否為有意義的趨勢線（避免幾乎水平的線）
        double priceRange = Math.Abs(p2.Price - p1.Price);
        //double avgPrice = (p1.Price + p2.Price) / 2;
        double priceChangeRatio = priceRange / avgPrice;
        if (priceChangeRatio < 0.001)  // 價格變化太小
        {
            //這個有機會讓超大價格例如比特幣那樣出現 300/100000的情況，會少於0.001
            //Core.Instance.Loggers.Log($"[DEBUG] CreateCandidateLine: price change too small (ratio={priceChangeRatio:F6} < 0.001, range={priceRange:F6}, avgPrice={avgPrice:F6})");
            //return null;
        }

        // 計算截距
        double intercept = p1.Price - slope * p1.Index;

        // 收集「貼線」的 pivot index（或 K 線）
        List<int> touchedIndices = new List<int>();
        int bodyCrossCount = 0;

        // 優化的觸點檢測邏輯
        int leftExtendIndex = Math.Max(0, p1.Index - 200); // 減少延伸範圍
        List<int> qualityTouches = new List<int>(); // 高品質觸點

        // 計算動態閾值
        double avgPrice2 = (p1.Price + p2.Price) / 2;
        double dynamicThresholdBase = GetDynamicTouchThreshold(avgPrice2);
        double dynamicThresholdSameType = dynamicThresholdBase * 1.5;

        // 移除了鼠標提起點的特殊容忍度處理，因為現在它不作為連接端點

        Core.Instance.Loggers.Log($"[DEBUG] Touch detection: avgPrice={avgPrice2:F6}, dynamicThresholdBase={dynamicThresholdBase:F6}, dynamicThresholdSameType={dynamicThresholdSameType:F6}");

        for (int i = leftExtendIndex; i <= p2.Index; i++)
        {
            double linePrice = slope * i + intercept;
            var bar = HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
            if (bar == null) continue;

            double barHigh = bar.High;
            double barLow = bar.Low;
            double bodyHigh = Math.Max(bar.Open, bar.Close);
            double bodyLow = Math.Min(bar.Open, bar.Close);

            // 優先檢查主要價格類型（High/Low）
            bool isHighQualityTouch = false;
            bool isTouch = false;

            // 檢查High/Low觸點（高品質）- 使用動態閾值
            if (CheckPriceTouch(barHigh, linePrice, dynamicThresholdBase) ||
                CheckPriceTouch(barLow, linePrice, dynamicThresholdBase))
            {
                isHighQualityTouch = true;
                isTouch = true;
                Core.Instance.Loggers.Log($"[DEBUG] Touch detected at bar {i} (High/Low touch)");
            }

            // 檢查實體端點（中等品質）- 使用動態閾值
            if (!isTouch)
            {
                if (CheckPriceTouch(bodyHigh, linePrice, dynamicThresholdSameType) ||
                    CheckPriceTouch(bodyLow, linePrice, dynamicThresholdSameType))
                {
                    isTouch = true;
                    Core.Instance.Loggers.Log($"[DEBUG] Touch detected at bar {i} (body touch)");
                }
            }

            if (isTouch)
            {
                touchedIndices.Add(i);
                if (isHighQualityTouch)
                {
                    qualityTouches.Add(i);
                }
            }

            // 檢查是否穿過蠟燭實體（更嚴格）
            if (linePrice > bodyLow + (bodyHigh - bodyLow) * 0.1 &&
                linePrice < bodyHigh - (bodyHigh - bodyLow) * 0.1)
            {
                bodyCrossCount++;
            }
        }

        // 計算觸點品質分數
        double touchQualityScore = qualityTouches.Count * 2.0 + (touchedIndices.Count - qualityTouches.Count);

        TrendLineCandidate cand = new TrendLineCandidate
        {
            StartPoint = p1,
            EndPoint = p2,
            TouchIndices = touchedIndices,
            CrossedBodies = bodyCrossCount,
            EndpointTypeWeight = endpointWeight,
            TouchQualityScore = touchQualityScore  // 新增觸點品質分數
        };
        return cand;
    }

    /// <summary>
    /// 檢查價格是否接觸趨勢線
    /// </summary>
    private bool CheckPriceTouch(double price, double linePrice, double threshold)
    {
        if (price == 0 || linePrice == 0)
        {
            Core.Instance.Loggers.Log($"[DEBUG] CheckPriceTouch: Invalid price (price={price:F6}, linePrice={linePrice:F6})");
            return false;
        }

        double diffPct = Math.Abs(price - linePrice) / Math.Max(price, linePrice);
        bool isTouch = diffPct <= threshold;

        // 調試：記錄觸點檢測詳情（僅記錄前幾個案例）
        int touchCheckCount = 0;
        touchCheckCount++;
        if (touchCheckCount <= 10)
        {
            Core.Instance.Loggers.Log($"[DEBUG] CheckPriceTouch #{touchCheckCount}: price={price:F6}, linePrice={linePrice:F6}, diffPct={diffPct:F6}, threshold={threshold:F6}, isTouch={isTouch}");
        }

        return isTouch;
    }

    /// <summary>
    /// 優化的趨勢線候選者評分系統
    /// </summary>
    private double ScoreCandidate(TrendLineCandidate cand, bool isRangeLine = false)
    {
        int touchCount = cand.TouchIndices.Count;
        int requiredTouchPoints = isRangeLine ? MIN_TOUCH_POINTS_RANGE : MIN_TOUCH_POINTS;

        if (touchCount < requiredTouchPoints)
        {
            Core.Instance.Loggers.Log($"[DEBUG] ScoreCandidate: insufficient touch points ({touchCount} < {requiredTouchPoints})");
            return 0;
        }

        // 更嚴格的實體穿越檢查
        if (cand.CrossedBodies > MIN_BODY_CROSS_TOLERANCE)
        {
            Core.Instance.Loggers.Log($"[DEBUG] ScoreCandidate: too many crossed bodies ({cand.CrossedBodies} > {MIN_BODY_CROSS_TOLERANCE})");
            return 0;
        }

        // 計算各項分數
        int timeSpan = cand.EndPoint.Index - cand.StartPoint.Index;

        // 1. 觸點品質分數（高品質觸點權重更高）
        double touchScore = cand.TouchQualityScore * 10.0;

        // 2. 時間跨度分數（適中的時間跨度得分最高）
        double timeSpanScore = CalculateTimeSpanScore(timeSpan);
        cand.TimeSpanScore = timeSpanScore;

        // 3. 角度分數（避免過陡或過平）- 使用正確的像素坐標角度
        double angle = GetCandidateAngle(cand);
        double angleScore = CalculateAngleScore(Math.Abs(angle));
        cand.AngleScore = angleScore;

        // 4. 觸點分佈分數（觸點分佈越均勻越好）
        double distributionScore = CalculateTouchDistributionScore(cand.TouchIndices, cand.StartPoint.Index, cand.EndPoint.Index);

        // 5. 向左延伸分數（延伸觸點的額外獎勵）
        int leftExtendTouches = cand.TouchIndices.Count(i => i < cand.StartPoint.Index);
        double leftExtendScore = leftExtendTouches * 2.0;

        // 6. 鼠標提起點延長線加分（新增）
        double mouseReleaseExtensionScore = CalculateMouseReleaseExtensionScore(cand);

        // 綜合分數計算
        double baseScore = touchScore + timeSpanScore + angleScore + distributionScore + leftExtendScore + mouseReleaseExtensionScore;
        double finalScore = baseScore * cand.EndpointTypeWeight;

        // 調試：記錄評分詳情（僅記錄前幾個案例）
        int scoreCount = 0;
        scoreCount++;
        if (scoreCount <= 5)
        {
            Core.Instance.Loggers.Log($"[DEBUG] ScoreCandidate #{scoreCount}: touchCount={touchCount}, touchQuality={cand.TouchQualityScore:F2}, crossedBodies={cand.CrossedBodies}");
            Core.Instance.Loggers.Log($"[DEBUG] ScoreCandidate #{scoreCount}: touchScore={touchScore:F2}, timeSpanScore={timeSpanScore:F2}, angleScore={angleScore:F2}, distributionScore={distributionScore:F2}, leftExtendScore={leftExtendScore:F2}, mouseReleaseScore={mouseReleaseExtensionScore:F2}");
            Core.Instance.Loggers.Log($"[DEBUG] ScoreCandidate #{scoreCount}: baseScore={baseScore:F2}, endpointWeight={cand.EndpointTypeWeight:F3}, finalScore={finalScore:F2}");
        }

        return finalScore;
    }

    /// <summary>
    /// 計算時間跨度分數
    /// </summary>
    private double CalculateTimeSpanScore(int timeSpan)
    {
        // 理想時間跨度為15-30根K線
        if (timeSpan >= 15 && timeSpan <= 30)
            return 20.0;
        else if (timeSpan >= 10 && timeSpan <= 45)
            return 15.0;
        else if (timeSpan >= 5 && timeSpan <= 60)
            return 10.0;
        else
            return 5.0;
    }

    /// <summary>
    /// 計算角度分數
    /// </summary>
    private double CalculateAngleScore(double absAngle)
    {
        // 理想角度為15-45度
        if (absAngle >= 15 && absAngle <= 45)
            return 15.0;
        else if (absAngle >= 10 && absAngle <= 55)
            return 10.0;
        else if (absAngle >= 5 && absAngle <= 60)
            return 5.0;
        else
            return 1.0;
    }

    /// <summary>
    /// 計算觸點分佈分數
    /// </summary>
    private double CalculateTouchDistributionScore(List<int> touchIndices, int startIndex, int endIndex)
    {
        if (touchIndices.Count < 2) return 0;

        // 計算觸點在時間軸上的分佈均勻度
        var sortedTouches = touchIndices.OrderBy(x => x).ToList();
        double totalSpan = endIndex - startIndex;
        double avgGap = totalSpan / (sortedTouches.Count - 1);

        double variance = 0;
        for (int i = 1; i < sortedTouches.Count; i++)
        {
            double gap = sortedTouches[i] - sortedTouches[i - 1];
            variance += Math.Pow(gap - avgGap, 2);
        }
        variance /= (sortedTouches.Count - 1);

        // 方差越小，分佈越均勻，分數越高
        double distributionScore = Math.Max(0, 10.0 - variance / avgGap);
        return distributionScore;
    }

    /// <summary>
    /// 計算鼠標提起點延長線加分（支持弧線水平延伸）
    /// </summary>
    private double CalculateMouseReleaseExtensionScore(TrendLineCandidate cand)
    {
        if (!hasMouseReleaseTarget) return 0;

        try
        {
            var mainWindow = this.CurrentChart.MainWindow;
            if (mainWindow == null) return 0;

            // 計算趨勢線的斜率和截距
            double deltaX = cand.EndPoint.Index - cand.StartPoint.Index;
            double deltaY = cand.EndPoint.Price - cand.StartPoint.Price;

            if (Math.Abs(deltaX) < 0.001) return 0; // 避免除零

            double slope = deltaY / deltaX;
            double intercept = cand.StartPoint.Price - slope * cand.StartPoint.Index;

            double linePrice;

            // 關鍵改進：如果鼠標提起點在趨勢線終點之後，使用水平延長線
            if (mouseReleaseIndex > cand.EndPoint.Index)
            {
                // 延長線段：使用終點價格的水平線
                linePrice = cand.EndPoint.Price;
                Core.Instance.Loggers.Log($"[DEBUG] Using horizontal extension: mouseIndex={mouseReleaseIndex} > endIndex={cand.EndPoint.Index}, using horizontal price={linePrice:F6}");
            }
            else
            {
                // 弧線段：使用直線方程近似（候選階段）
                linePrice = slope * mouseReleaseIndex + intercept;
                Core.Instance.Loggers.Log($"[DEBUG] Using arc segment approximation: mouseIndex={mouseReleaseIndex} <= endIndex={cand.EndPoint.Index}, linePrice={linePrice:F6}");
            }

            // 將價格轉換為像素坐標進行距離計算
            float linePriceY = (float)mainWindow.CoordinatesConverter.GetChartY(linePrice);
            float targetPriceY = (float)mainWindow.CoordinatesConverter.GetChartY(mouseReleasePrice);

            // 計算像素距離
            double pixelDistance = Math.Abs(linePriceY - targetPriceY);

            // 如果在容忍度範圍內，給予高分
            if (pixelDistance <= MOUSE_RELEASE_EXTENSION_TOLERANCE)
            {
                // 距離越近分數越高
                double proximityFactor = 1.0 - (pixelDistance / MOUSE_RELEASE_EXTENSION_TOLERANCE);
                double score = MOUSE_RELEASE_BONUS_SCORE * proximityFactor;

                // 水平延長線的加分（這是我們想要的效果）
                if (mouseReleaseIndex > cand.EndPoint.Index)
                {
                    score *= 1.5; // 水平延長線段額外50%加分
                    Core.Instance.Loggers.Log($"[DEBUG] Horizontal extension line bonus applied: score increased to {score:F2}");
                }

                Core.Instance.Loggers.Log($"[DEBUG] Extension bonus: line({cand.StartPoint.Index},{cand.StartPoint.Price:F6})-({cand.EndPoint.Index},{cand.EndPoint.Price:F6}), target({mouseReleaseIndex},{mouseReleasePrice:F6}), linePrice={linePrice:F6}, distance={pixelDistance:F2}px, score={score:F2}");
                return score;
            }

            return 0;
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CalculateMouseReleaseExtensionScore: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 優化的6端點權重計算（保持完整性但提高品質）
    /// </summary>
    private double GetEndpointTypeWeight(PriceType type1, PriceType type2)
    {
        // 完全相同類型 - 最高權重
        if (type1 == type2)
            return 1.0;

        // 定義價格類型的優先級和族群
        bool isType1Primary = (type1 == PriceType.High || type1 == PriceType.Low);
        bool isType2Primary = (type2 == PriceType.High || type2 == PriceType.Low);

        bool isType1High = (type1 == PriceType.High || type1 == PriceType.BodyHighLeft || type1 == PriceType.BodyHighRight);
        bool isType2High = (type2 == PriceType.High || type2 == PriceType.BodyHighLeft || type2 == PriceType.BodyHighRight);
        bool isType1Low = (type1 == PriceType.Low || type1 == PriceType.BodyLowLeft || type1 == PriceType.BodyLowRight);
        bool isType2Low = (type2 == PriceType.Low || type2 == PriceType.BodyLowLeft || type2 == PriceType.BodyLowRight);

        // 兩個都是主要類型（High/Low）- 最高權重
        if (isType1Primary && isType2Primary)
        {
            if ((type1 == PriceType.High && type2 == PriceType.High) ||
                (type1 == PriceType.Low && type2 == PriceType.Low))
                return 1.0;
            else
                return 0.9; // High-Low組合仍然很重要
        }

        // 主要類型與同族Body端點組合 - 高權重
        if ((isType1Primary && isType2High && isType1High) ||
            (isType1Primary && isType2Low && isType1Low) ||
            (isType2Primary && isType1High && isType2High) ||
            (isType2Primary && isType1Low && isType2Low))
        {
            return 0.85;
        }

        // 主要類型與異族Body端點組合 - 中高權重
        if (isType1Primary || isType2Primary)
        {
            return 0.75;
        }

        // 同族Body端點組合 - 中等權重
        if ((isType1High && isType2High) || (isType1Low && isType2Low))
        {
            // 檢查是否為同一根K線的左右端點組合
            bool isSameCandleLeftRight =
                (type1 == PriceType.BodyHighLeft && type2 == PriceType.BodyHighRight) ||
                (type1 == PriceType.BodyHighRight && type2 == PriceType.BodyHighLeft) ||
                (type1 == PriceType.BodyLowLeft && type2 == PriceType.BodyLowRight) ||
                (type1 == PriceType.BodyLowRight && type2 == PriceType.BodyLowLeft);

            if (isSameCandleLeftRight)
                return 0.6; // 同一根K線的左右端點權重較低
            else
                return 0.8; // 不同K線的同族端點權重較高
        }

        // 異族Body端點組合 - 較低權重
        if ((isType1High && isType2Low) || (isType1Low && isType2High))
        {
            return 0.65;
        }

        // 其他組合 - 最低權重
        return 0.5;
    }

    #endregion

    #region Pivot 檢測 (採用 n-Bar fractal 方式)

    /// <summary>
    /// 查找區間內所有高低點 pivot（n-Bar fractal）
    /// </summary>
    private List<PivotPoint> FindAllPivotPoints(int startIndex, int endIndex)
    {
        // 確保區間合理
        if (startIndex < 0) startIndex = 0;
        if (endIndex >= HistoricalData.Count) endIndex = HistoricalData.Count - 1;
        if (startIndex >= endIndex)
        {
            Core.Instance.Loggers.Log($"[DEBUG] FindAllPivotPoints: Invalid range startIndex={startIndex}, endIndex={endIndex}");
            return new List<PivotPoint>();
        }

        Core.Instance.Loggers.Log($"[DEBUG] FindAllPivotPoints: Processing range [{startIndex + PIVOT_LOOKBACK}, {endIndex - PIVOT_LOOKBACK}]");

        List<PivotPoint> pivots = new List<PivotPoint>();
        HashSet<int> processedIndices = new HashSet<int>(); // 避免重複處理
        int totalBarsChecked = 0;
        int validBarsFound = 0;

        // 從 startIndex + PIVOT_LOOKBACK 到 endIndex - PIVOT_LOOKBACK
        for (int i = startIndex + PIVOT_LOOKBACK; i <= endIndex - PIVOT_LOOKBACK; i++)
        {
            totalBarsChecked++;
            if (processedIndices.Contains(i)) continue;

            var bar = HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
            if (bar == null) continue;

            validBarsFound++;
            double currentHigh = bar.High;
            double currentLow = bar.Low;
            double currentOpen = bar.Open;
            double currentClose = bar.Close;
            double currentBodyHigh = Math.Max(currentOpen, currentClose);
            double currentBodyLow = Math.Min(currentOpen, currentClose);

            

            // 調試：記錄價格信息（僅前幾根K線）
            if (totalBarsChecked <= 3)
            {
                Core.Instance.Loggers.Log($"[DEBUG] Bar {i}: H={currentHigh:F6}, L={currentLow:F6}, O={currentOpen:F6}, C={currentClose:F6}");
                Core.Instance.Loggers.Log($"[DEBUG] Bar {i}: BodyH={currentBodyHigh:F6}, BodyL={currentBodyLow:F6}");
            }
            
            // 恢復完整的6個端點檢測（保留優化的嚴格性）
            bool isHighPivot = true;
            bool isLowPivot = true;
            bool isBodyHighRightPivot = true;
            bool isBodyHighLeftPivot = true;
            bool isBodyLowRightPivot = true;
            bool isBodyLowLeftPivot = true;

            int comparedBars = 0;
            // 檢查前後 PIVOT_LOOKBACK 根K線
            for (int k = i - PIVOT_LOOKBACK; k <= i + PIVOT_LOOKBACK; k++)
            {
                if (k == i) continue;
                var compBar = HistoricalData[k, SeekOriginHistory.Begin] as HistoryItemBar;
                if (compBar == null) continue;

                comparedBars++;

                // 高點判斷
                if (compBar.High >= currentHigh )
                {
                    isHighPivot = false;
                }

                // 低點判斷
                if (compBar.Low <= currentLow )
                {
                    isLowPivot = false;
                }

                // 蠟燭實體高點判斷
                double compBodyHigh = Math.Max(compBar.Open, compBar.Close);
                if (compBodyHigh >= currentBodyHigh )
                {
                    isBodyHighRightPivot = false;
                    isBodyHighLeftPivot = false;
                }

                // 蠟燭實體低點判斷
                double compBodyLow = Math.Min(compBar.Open, compBar.Close);
                if (compBodyLow <= currentBodyLow )
                {
                    isBodyLowRightPivot = false;
                    isBodyLowLeftPivot = false;
                }
            }

            // 調試：記錄pivot檢測結果（僅前幾根K線）
            if (totalBarsChecked <= 3)
            {
                Core.Instance.Loggers.Log($"[DEBUG] Bar {i} pivot check: compared {comparedBars} bars");
                Core.Instance.Loggers.Log($"[DEBUG] Bar {i} pivots: High={isHighPivot}, Low={isLowPivot}, BodyHR={isBodyHighRightPivot}, BodyHL={isBodyHighLeftPivot}, BodyLR={isBodyLowRightPivot}, BodyLL={isBodyLowLeftPivot}");
            }

            // 添加找到的完整6個端點（保留優化的品質控制）
            int pivotsAddedForThisBar = 0;
            if (isHighPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i,
                    PriceType = PriceType.High,
                    Price = currentHigh
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            if (isLowPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i,
                    PriceType = PriceType.Low,
                    Price = currentLow
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            if (isBodyHighRightPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i + 1, // BodyHighRight 是右上角的價格
                    PriceType = PriceType.BodyHighRight,
                    Price = currentBodyHigh
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            if (isBodyHighLeftPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i, // BodyHighLeft 是左上角的價格
                    PriceType = PriceType.BodyHighLeft,
                    Price = currentBodyHigh
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            if (isBodyLowRightPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i + 1, // BodyLowRight 是右下角的價格
                    PriceType = PriceType.BodyLowRight,
                    Price = currentBodyLow
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            if (isBodyLowLeftPivot)
            {
                pivots.Add(new PivotPoint
                {
                    Index = i, // BodyLowLeft 是左下角的價格
                    PriceType = PriceType.BodyLowLeft,
                    Price = currentBodyLow
                });
                processedIndices.Add(i);
                pivotsAddedForThisBar++;
            }

            // 調試：記錄添加的pivot點（僅前幾根K線）
            if (totalBarsChecked <= 3 && pivotsAddedForThisBar > 0)
            {
                Core.Instance.Loggers.Log($"[DEBUG] Bar {i}: Added {pivotsAddedForThisBar} pivot points");
            }
        }

        Core.Instance.Loggers.Log($"[DEBUG] FindAllPivotPoints completed: checked {totalBarsChecked} bars, found {validBarsFound} valid bars, created {pivots.Count} pivot points");

        // 依 Index 排序
        return pivots.OrderBy(p => p.Index).ToList();
    }

    #endregion

    #region 斐波那契弧線計算方法

    /// <summary>
    /// 計算斐波那契弧線的二次方程係數
    /// 弧線方程: price = a * (index - startIndex)^2 + b * (index - startIndex) + c
    /// </summary>
    private void CalculateFibonacciArcCoefficients(PivotPoint start, PivotPoint end, TrendLine trendLine, double fibonacciRatio = 0.618033988749895)
    {
        try
        {
            // 計算基本參數
            double deltaIndex = end.Index - start.Index;
            double deltaPrice = end.Price - start.Price;

            if (deltaIndex == 0)
            {
                // 退化為直線
                trendLine.IsCurve = false;
                return;
            }

            // 使用傳入的斐波那契比例計算弧線的曲率 - 增加弧度強度
            // 在中點處，弧線偏離直線的距離為總價格變化的斐波那契比例
            double midIndex = deltaIndex * fibonacciRatio; // 斐波那契分割點
            double straightLinePrice = start.Price + (deltaPrice * fibonacciRatio); // 直線在該點的價格

            // 計算弧線在黃金分割點的偏移量（適度的美感弧度）
            // 根據價格範圍調整，保持自然的弧線效果
            double priceRange = Math.Abs(deltaPrice);
            double timeSpan = deltaIndex;

            // 大幅減小弧度：基於價格變化的2-8%，讓弧線更溫和
            double curvatureRatio = Math.Max(0.02, Math.Min(0.08, timeSpan / 500.0)); // 2%-8%的弧度
            double curvatureOffset = priceRange * curvatureRatio;

            // 關鍵改進：讓弧線延長線往右趨向水平
            bool isUpTrend = deltaPrice > 0;

            // 調整弧線中點，使用更小的弧度讓終點更容易趨向水平
            // 重點：弧度要足夠小，讓終點斜率能夠接近0
            double arcMidPrice;
            if (isUpTrend)
            {
                // 上升趨勢：弧線輕微向上彎曲，減少弧度強度
                arcMidPrice = straightLinePrice + curvatureOffset * 0.3; // 從0.8降到0.3
            }
            else
            {
                // 下降趨勢：弧線輕微向下彎曲，減少弧度強度
                arcMidPrice = straightLinePrice - curvatureOffset * 0.3; // 從0.8降到0.3
            }

            // 重新設計：使用更簡單直觀的方法
            // 策略：先用傳統三點法建立基礎弧線，然後調整係數讓終點趨向水平

            double y1 = start.Price;
            double x2 = midIndex;
            double y2 = arcMidPrice;
            double x3 = deltaIndex;
            double y3 = end.Price;

            // 使用傳統三點法構建二次方程 y = ax² + bx + c

            // 建立線性方程組求解係數
            // y1 = a*x1² + b*x1 + c  => y1 = c
            // y2 = a*x2² + b*x2 + c
            // y3 = a*x3² + b*x3 + c

            double c = y1;

            // 從第二和第三個方程求解a和b
            // y2 - c = a*x2² + b*x2
            // y3 - c = a*x3² + b*x3

            double det = x2 * x3 * (x3 - x2);
            double a, b;

            if (Math.Abs(det) > 1e-10)
            {
                // 使用克拉默法則求解
                a = (x2 * (y3 - c) - x3 * (y2 - c)) / det;
                b = ((y2 - c) * x3 - (y3 - c) * x2) / det;

                // 關鍵改進：調整係數讓終點斜率趨向水平
                // 計算當前終點斜率
                double currentEndSlope = 2 * a * x3 + b;
                double originalSlope = deltaPrice / deltaIndex;

                // 如果終點斜率太陡，進行調整
                double maxAllowedSlope = Math.Abs(originalSlope) * 0.1; // 允許最大10%的原斜率

                if (Math.Abs(currentEndSlope) > maxAllowedSlope)
                {
                    // 調整係數：保持起點和終點不變，但讓終點斜率更平緩
                    double targetEndSlope = Math.Sign(currentEndSlope) * maxAllowedSlope;

                    // 重新計算係數：保持c和終點約束，調整斜率約束
                    // y3 = a*x3² + b*x3 + c
                    // targetEndSlope = 2*a*x3 + b
                    // 解得：b = targetEndSlope - 2*a*x3
                    // 代入：y3 = a*x3² + (targetEndSlope - 2*a*x3)*x3 + c
                    // 簡化：y3 = a*x3² + targetEndSlope*x3 - 2*a*x3² + c
                    // 即：y3 = -a*x3² + targetEndSlope*x3 + c
                    // 所以：a = (targetEndSlope*x3 + c - y3) / (-x3²)

                    a = (targetEndSlope * x3 + c - y3) / (-x3 * x3);
                    b = targetEndSlope - 2 * a * x3;
                }
            }
            else
            {
                // 退化為直線
                trendLine.IsCurve = false;
                return;
            }

            trendLine.CurvatureCoefficient = a;
            trendLine.LinearCoefficient = b;
            trendLine.ConstantTerm = c;

            // 儲存終點價格用於參考
            trendLine.HorizontalExtensionPrice = y3;

            // 記錄實際的終點斜率
            double actualEndSlope = 2 * a * x3 + b;
            Core.Instance.Loggers.Log($"[DEBUG] Continuous arc: actual end slope={actualEndSlope:F6}, end price={y3:F6}");

            // 設置斐波那契曲率強度
            trendLine.FibonacciCurvature = fibonacciRatio;

            Core.Instance.Loggers.Log($"[DEBUG] Arc coefficients: a={trendLine.CurvatureCoefficient:F6}, b={trendLine.LinearCoefficient:F6}, c={trendLine.ConstantTerm:F6}");
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CalculateFibonacciArcCoefficients: {ex.Message}");
            trendLine.IsCurve = false; // 降級為直線
        }
    }

    /// <summary>
    /// 根據弧線方程計算指定索引處的價格（連續弧線，延長線部分斜率趨向水平）
    /// </summary>
    private double CalculateArcPrice(TrendLine trendLine, int index)
    {
        if (!trendLine.IsCurve)
        {
            // 使用直線方程
            return trendLine.Slope * index + trendLine.Intercept;
        }

        var startIndex = (int)CurrentChart.MainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime);
        var endIndex = (int)CurrentChart.MainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime);
        double x = index - startIndex;

        // 關鍵改進：使用連續的弧線公式，不分段
        // 弧線在終點處的斜率已經接近水平，延長線自然延續這個趨勢

        if (index <= endIndex)
        {
            // 弧線段：使用二次方程
            return trendLine.CurvatureCoefficient * x * x +
                   trendLine.LinearCoefficient * x +
                   trendLine.ConstantTerm;
        }
        else
        {
            // 延長線段：繼續使用弧線公式，但添加漸進水平修正
            double basePrice = trendLine.CurvatureCoefficient * x * x +
                              trendLine.LinearCoefficient * x +
                              trendLine.ConstantTerm;

            // 計算延長距離
            double extensionDistance = x - (endIndex - startIndex);

            // 漸進水平修正：隨著延長距離增加，價格變化率逐漸減小
            // 使用更快的指數衰減讓斜率快速趨向0
            double decayFactor = Math.Exp(-extensionDistance / 20.0); // 20個bar的衰減常數，更快衰減

            // 計算終點處的理論斜率
            double endX = endIndex - startIndex;
            double theoreticalSlope = 2 * trendLine.CurvatureCoefficient * endX + trendLine.LinearCoefficient;

            // 延長線的價格修正：讓斜率逐漸衰減到接近0
            double slopeCorrection = theoreticalSlope * extensionDistance * decayFactor;

            return basePrice + slopeCorrection;
        }
    }

    /// <summary>
    /// 繪製斐波那契弧線
    /// </summary>
    private void DrawFibonacciArc(Graphics g, Pen pen, TrendLine trendLine,
        TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        try
        {
            var startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime);
            var endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime);

            if (startIndex < 0 || endIndex >= HistoricalData.Count || startIndex >= endIndex)
                return;

            // 繪製弧線段
            var points = new List<PointF>();

            for (int i = startIndex; i <= endIndex; i++)
            {
                double price = CalculateArcPrice(trendLine, i);
                float x = (float)mainWindow.CoordinatesConverter.GetChartX(
                    HistoricalData[i, SeekOriginHistory.Begin].TimeLeft);
                float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);
                points.Add(new PointF(x, y));
            }

            // 繪製弧線
            if (points.Count >= 2)
            {
                for (int i = 0; i < points.Count - 1; i++)
                {
                    g.DrawLine(pen, points[i], points[i + 1]);
                }
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] DrawFibonacciArc: {ex.Message}");
            // 降級為直線繪製
            float x1 = (float)mainWindow.CoordinatesConverter.GetChartX(trendLine.StartTime);
            float x2 = (float)mainWindow.CoordinatesConverter.GetChartX(trendLine.EndTime);
            float y1 = (float)mainWindow.CoordinatesConverter.GetChartY(
                trendLine.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime) + trendLine.Intercept);
            float y2 = (float)mainWindow.CoordinatesConverter.GetChartY(
                trendLine.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime) + trendLine.Intercept);
            g.DrawLine(pen, x1, y1, x2, y2);
        }
    }

    /// <summary>
    /// 繪製弧線延伸
    /// </summary>
    private void DrawArcExtensions(Graphics g, Pen pen, TrendLine trendLine,
        TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        try
        {
            var startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime);
            var endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime);

            if (startIndex < 0 || endIndex >= HistoricalData.Count || startIndex >= endIndex)
                return;

            // 確定延伸範圍 - 修改為支持延伸到未來
            int leftExtendIndex, rightExtendIndex;

            // 如果是選中的線，延伸到圖表邊緣
            if ((selectedLongLine == trendLine && isSelectedLongLineActive) ||
                (selectedShortLine == trendLine && isSelectedShortLineActive))
            {
                // 延伸到圖表邊緣
                float leftX = mainWindow.ClientRectangle.Left;
                float rightX = mainWindow.ClientRectangle.Right;
                leftExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(
                    mainWindow.CoordinatesConverter.GetTime(leftX));
                rightExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(
                    mainWindow.CoordinatesConverter.GetTime(rightX));
            }
            else
            {
                // 修改：正常延伸也可以延伸到圖表右邊界，不受歷史數據限制
                leftExtendIndex = Math.Max(0, startIndex - 300);
                float rightX = mainWindow.ClientRectangle.Right;
                DateTime rightTime = mainWindow.CoordinatesConverter.GetTime(rightX);
                rightExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(rightTime);
            }

            // 繪製左延伸弧線
            if (leftExtendIndex < startIndex)
            {
                var leftPoints = new List<PointF>();
                for (int i = leftExtendIndex; i < startIndex; i++)
                {
                    if (i >= 0 && i < HistoricalData.Count)
                    {
                        double price = CalculateArcPrice(trendLine, i);
                        float x = (float)mainWindow.CoordinatesConverter.GetChartX(
                            HistoricalData[i, SeekOriginHistory.Begin].TimeLeft);
                        float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);
                        leftPoints.Add(new PointF(x, y));
                    }
                }

                // 繪製左延伸弧線
                for (int i = 0; i < leftPoints.Count - 1; i++)
                {
                    g.DrawLine(pen, leftPoints[i], leftPoints[i + 1]);
                }
            }

            // 繪製右延伸弧線 - 修改為支持延伸到未來
            if (rightExtendIndex > endIndex)
            {
                var rightPoints = new List<PointF>();

                // 計算延伸範圍，不受歷史數據限制
                int maxExtendIndex = rightExtendIndex;

                for (int i = endIndex + 1; i <= maxExtendIndex; i++)
                {
                    // 計算弧線價格（即使超出歷史數據範圍）
                    double price = CalculateArcPrice(trendLine, i);

                    // 計算時間點（可能是未來時間）
                    DateTime timePoint;
                    if (i < HistoricalData.Count)
                    {
                        // 在歷史數據範圍內
                        timePoint = HistoricalData[i, SeekOriginHistory.Begin].TimeLeft;
                    }
                    else
                    {
                        // 超出歷史數據範圍，推算未來時間
                        var lastBar = HistoricalData[HistoricalData.Count - 1, SeekOriginHistory.Begin];
                        var secondLastBar = HistoricalData[HistoricalData.Count - 2, SeekOriginHistory.Begin];
                        TimeSpan barInterval = lastBar.TimeLeft - secondLastBar.TimeLeft;
                        int futureSteps = i - (HistoricalData.Count - 1);
                        timePoint = lastBar.TimeLeft.Add(TimeSpan.FromTicks(barInterval.Ticks * futureSteps));
                    }

                    float x = (float)mainWindow.CoordinatesConverter.GetChartX(timePoint);
                    float y = (float)mainWindow.CoordinatesConverter.GetChartY(price);
                    rightPoints.Add(new PointF(x, y));
                }

                // 繪製右延伸弧線
                for (int i = 0; i < rightPoints.Count - 1; i++)
                {
                    g.DrawLine(pen, rightPoints[i], rightPoints[i + 1]);
                }
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] DrawArcExtensions: {ex.Message}");
        }
    }

    /// <summary>
    /// 繪製直線延伸（原有邏輯）
    /// </summary>
    private void DrawStraightExtensions(Graphics g, Pen pen, TrendLine trendLine,
        TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow, float x1, float y1, float x2, float y2)
    {
        try
        {
            float leftX, leftY, rightX, rightY;

            // 如果是選中的線，延伸到圖表邊緣
            if ((selectedLongLine == trendLine && isSelectedLongLineActive) ||
                (selectedShortLine == trendLine && isSelectedShortLineActive))
            {
                // 延伸到圖表左邊緣
                leftX = mainWindow.ClientRectangle.Left;
                float leftIndex = (float)mainWindow.CoordinatesConverter.GetBarIndex(
                    mainWindow.CoordinatesConverter.GetTime(leftX));
                leftY = (float)mainWindow.CoordinatesConverter.GetChartY(
                    trendLine.Slope * leftIndex + trendLine.Intercept);

                // 延伸到圖表右邊緣
                rightX = mainWindow.ClientRectangle.Right;
                float rightIndex = (float)mainWindow.CoordinatesConverter.GetBarIndex(
                    mainWindow.CoordinatesConverter.GetTime(rightX));
                rightY = (float)mainWindow.CoordinatesConverter.GetChartY(
                    trendLine.Slope * rightIndex + trendLine.Intercept);
            }
            else
            {
                // 修改：非選中線也可以延伸到圖表右邊界，不受歷史數據限制
                int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime);
                int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime);
                int leftExtendIndex = Math.Max(0, startIndex - 300);

                // 左延長線仍使用歷史數據
                leftX = (float)mainWindow.CoordinatesConverter.GetChartX(
                    HistoricalData[leftExtendIndex, SeekOriginHistory.Begin].TimeLeft);
                leftY = (float)mainWindow.CoordinatesConverter.GetChartY(
                    trendLine.Slope * leftExtendIndex + trendLine.Intercept);

                // 右延長線延伸到圖表右邊界
                rightX = mainWindow.ClientRectangle.Right;
                DateTime rightTime = mainWindow.CoordinatesConverter.GetTime(rightX);
                int rightExtendIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(rightTime);
                rightY = (float)mainWindow.CoordinatesConverter.GetChartY(
                    trendLine.Slope * rightExtendIndex + trendLine.Intercept);
            }

            // 向左延伸
            g.DrawLine(pen, x1, y1, leftX, leftY);
            // 向右延伸
            g.DrawLine(pen, x2, y2, rightX, rightY);
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] DrawStraightExtensions: {ex.Message}");
        }
    }

    #endregion

    #region 其他功能（新增趨勢線、取得價格等）

    /// <summary>
    /// 計算趨勢線在指定索引處的價格（支持直線和曲線）
    /// </summary>
    private double CalculateLinePrice(TrendLine line, int index)
    {
        if (line.IsCurve)
        {
            // 使用弧線計算
            return CalculateArcPrice(line, index);
        }
        else
        {
            // 使用直線計算
            return line.Slope * index + line.Intercept;
        }
    }

    private void AddTrendLine(PivotPoint start, PivotPoint end)
    {
        double slope = (end.Price - start.Price) / (end.Index - start.Index);
        double intercept = start.Price - slope * start.Index;

        if (TrendLineMode == 2) // 直線模式
        {
            // 創建單一直線趨勢線
            var trendLine = new TrendLine
            {
                StartTime = HistoricalData[start.Index, SeekOriginHistory.Begin].TimeLeft,
                EndTime = HistoricalData[end.Index, SeekOriginHistory.Begin].TimeLeft,
                StartPriceType = start.PriceType,
                EndPriceType = end.PriceType,
                Slope = slope,
                Intercept = intercept,
                BatchId = currentBatchId,
                IsCurve = false, // 直線模式
                CustomColor = Color.Empty // 使用默認顏色
            };

            trendLines.Add(trendLine);
            Core.Instance.Loggers.Log($"[DEBUG] Created straight trend line");
        }
        else if (TrendLineMode == 3) // 混合模式（同時顯示曲線和直線）
        {
            // 首先創建直線趨勢線
            var straightLine = new TrendLine
            {
                StartTime = HistoricalData[start.Index, SeekOriginHistory.Begin].TimeLeft,
                EndTime = HistoricalData[end.Index, SeekOriginHistory.Begin].TimeLeft,
                StartPriceType = start.PriceType,
                EndPriceType = end.PriceType,
                Slope = slope,
                Intercept = intercept,
                BatchId = currentBatchId,
                IsCurve = false, // 直線模式
                CustomColor = Color.FromArgb(128, 128, 128) // 灰色用於區分
            };
            trendLines.Add(straightLine);

            // 然後創建斐波那契弧線趨勢線
            var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };
            var ratioNames = new[] { "FIBONACCI_RATIO", "GOLDEN_RATIO" };
            var ratioColors = new[] { Color.FromArgb(255, 255, 255),  Color.FromArgb(255, 0, 255) }; // 純白色、純洋紅色

            for (int i = 0; i < fibonacciRatios.Length; i++)
            {
                var trendLine = new TrendLine
                {
                    StartTime = HistoricalData[start.Index, SeekOriginHistory.Begin].TimeLeft,
                    EndTime = HistoricalData[end.Index, SeekOriginHistory.Begin].TimeLeft,
                    StartPriceType = start.PriceType,
                    EndPriceType = end.PriceType,
                    Slope = slope,
                    Intercept = intercept,
                    BatchId = currentBatchId,
                    IsCurve = true, // 啟用弧線模式
                    FibonacciCurvature = fibonacciRatios[i],
                    CustomColor = ratioColors[i] // 設置自定義顏色
                };

                // 調試信息：確認顏色設置
                Core.Instance.Loggers.Log($"[DEBUG] TrendLine created - Ratio: {fibonacciRatios[i]:F6}, Color: R={ratioColors[i].R}, G={ratioColors[i].G}, B={ratioColors[i].B}");

                // 計算斐波那契弧線係數
                try
                {
                    CalculateFibonacciArcCoefficients(start, end, trendLine, fibonacciRatios[i]);
                    Core.Instance.Loggers.Log($"[DEBUG] Created trend line with {ratioNames[i]} ({fibonacciRatios[i]:F6}) - Color: {ratioColors[i].Name}");
                }
                catch (Exception ex)
                {
                    Core.Instance.Loggers.Log($"[ERROR] AddTrendLine - Failed to calculate arc coefficients for {ratioNames[i]}: {ex.Message}");
                    trendLine.IsCurve = false; // 降級為直線
                }

                trendLines.Add(trendLine);
            }

            Core.Instance.Loggers.Log($"[DEBUG] Created mixed mode trend lines (1 straight + {fibonacciRatios.Length} curves)");
        }
        else // 曲線模式 (TrendLineMode == 1)
        {
            // 創建三個版本的趨勢線，分別使用不同的斐波那契比例和顏色
            var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };
            var ratioNames = new[] { "FIBONACCI_RATIO", "GOLDEN_RATIO" };
            var ratioColors = new[] { Color.FromArgb(255, 255, 255),  Color.FromArgb(255, 0, 255) }; // 純白色、純洋紅色

            for (int i = 0; i < fibonacciRatios.Length; i++)
            {
                var trendLine = new TrendLine
                {
                    StartTime = HistoricalData[start.Index, SeekOriginHistory.Begin].TimeLeft,
                    EndTime = HistoricalData[end.Index, SeekOriginHistory.Begin].TimeLeft,
                    StartPriceType = start.PriceType,
                    EndPriceType = end.PriceType,
                    Slope = slope,
                    Intercept = intercept,
                    BatchId = currentBatchId,
                    IsCurve = true, // 啟用弧線模式
                    FibonacciCurvature = fibonacciRatios[i],
                    CustomColor = ratioColors[i] // 設置自定義顏色
                };

                // 調試信息：確認顏色設置
                Core.Instance.Loggers.Log($"[DEBUG] TrendLine created - Ratio: {fibonacciRatios[i]:F6}, Color: R={ratioColors[i].R}, G={ratioColors[i].G}, B={ratioColors[i].B}");

                // 計算斐波那契弧線係數
                try
                {
                    CalculateFibonacciArcCoefficients(start, end, trendLine, fibonacciRatios[i]);
                    Core.Instance.Loggers.Log($"[DEBUG] Created trend line with {ratioNames[i]} ({fibonacciRatios[i]:F6}) - Color: {ratioColors[i].Name}");
                }
                catch (Exception ex)
                {
                    Core.Instance.Loggers.Log($"[ERROR] AddTrendLine - Failed to calculate arc coefficients for {ratioNames[i]}: {ex.Message}");
                    trendLine.IsCurve = false; // 降級為直線
                }

                trendLines.Add(trendLine);
            }
        }
    }

    private double GetPrice(int index, PriceType priceType)
    {
        var bar = HistoricalData[index, SeekOriginHistory.Begin] as HistoryItemBar;
        if (bar == null) return 0;

        int barsWidth = this.CurrentChart.BarsWidth;
        double bodyWidth = barsWidth * 0.8; // 使用80%的蠟燭寬度作為實體寬度

        switch (priceType)
        {
            case PriceType.High: return bar.High;
            case PriceType.Low: return bar.Low;
            case PriceType.BodyHighRight: return Math.Max(bar.Open, bar.Close);
            case PriceType.BodyHighLeft: return Math.Max(bar.Open, bar.Close);
            case PriceType.BodyLowRight: return Math.Min(bar.Open, bar.Close);
            case PriceType.BodyLowLeft: return Math.Min(bar.Open, bar.Close);
            default: return bar.Close;
        }
    }

    private void RemoveLastTrendLines()
    {
        if (trendLines.Count > 0)
        {
            int lastBatchId = trendLines.Max(t => t.BatchId);
            trendLines.RemoveAll(t => t.BatchId == lastBatchId);
        }
    }

    #endregion

    #region 畫圖

    public double FormatSymbolPrice(double price, bool roundUp = true)
        {
            Symbol symbol = this.Symbol;
            if (symbol == null)
                throw new ArgumentNullException(nameof(symbol));

            double tickSize = symbol.TickSize;

            // 計算需要進位的小數位數
            int decimalPlaces = BitConverter.GetBytes(decimal.GetBits((decimal)tickSize)[3])[2];

            // 計算價格除以 tickSize 的商和餘數
            double quotient = Math.Floor(price / tickSize);
            double remainder = price % tickSize;

            double roundedPrice;
            if (roundUp)
            {
                // 向上取整：如果有餘數，就進位到下一個 tick
                roundedPrice = remainder > 0
                    ? (quotient + 1) * tickSize
                    : quotient * tickSize;
            }
            else
            {
                // 向下取整：直接捨去餘數
                roundedPrice = quotient * tickSize;
            }

            // 格式化價格
            if (double.TryParse(symbol.FormatPrice(roundedPrice), out double formattedPrice))
            {
                return formattedPrice;
            }

            return roundedPrice;
        }

        // 為了方便使用，可以添加兩個輔助方法
        public double FormatSymbolPriceUp(double price)
        {
            return FormatSymbolPrice(price, true);
        }

        public double FormatSymbolPriceDown(double price)
        {
            return FormatSymbolPrice(price, false);
        }
    private void SendOrderStrategyMarket(string side, double slTicks)
    {
        try
        {
                string orderType = "Market";

            

            // 創建下單請求
            var request = new PlaceOrderRequestParameters()
            {
                Account = this.CurrentChart.Account,
                Symbol = this.Symbol,
                TimeInForce = TimeInForce.GTC,
                Quantity = 1,
                Side = side == "Buy" ? Side.Buy : Side.Sell,
                StopLoss = SlTpHolder.CreateSL(slTicks, PriceMeasurement.Offset)
            };

            // 根據訂單類型設置價格
            string selectedOrderType = Core.OrderTypes.FirstOrDefault(x => 
                x.ConnectionId == this.Symbol.ConnectionId && 
                x.Behavior.ToString().ToUpper() == orderType.ToUpper())?.Id;

            if (string.IsNullOrEmpty(selectedOrderType))
            {
                Core.Instance.Loggers.Log($"Connection does not support {orderType} orders");
                return;
            }

            request.OrderTypeId = selectedOrderType;
            

            // 發送訂單
            var result = Core.Instance.PlaceOrder(request);

            if (result.Status == TradingOperationResultStatus.Failure)
            {
                Core.Instance.Loggers.Log($"Place {side} {orderType} order refused: {result.Message}");
            }
            else
            {
                Core.Instance.Loggers.Log($"{side} {orderType} order placed ");
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"Error placing order: {ex.Message}");
        }
    }
    

    public override void OnPaintChart(PaintChartEventArgs args)
    {
        base.OnPaintChart(args);

        var mainWindow = CurrentChart.MainWindow;
        Graphics g = args.Graphics;

        var prevClip = g.ClipBounds;
        g.SetClip(mainWindow.ClientRectangle);

        try
        {
            int buttonWidth = 100;
            int buttonHeight = 30;
            int margin = 20;
            int spacing = 10;

            int totalWidth = buttonWidth * 6 + spacing * 5;  // 增加一個按鈕的寬度
            int x = (mainWindow.ClientRectangle.Width - totalWidth) / 2;
            int y = mainWindow.ClientRectangle.Height - buttonHeight - margin - buttonHeight - spacing;  // 往上移動一個按鈕的距離

            buttonRect = new Rectangle(x, y, buttonWidth, buttonHeight);
            clearButtonRect = new Rectangle(x + buttonWidth + spacing, y, buttonWidth, buttonHeight);
            clearAllButtonRect = new Rectangle(x + (buttonWidth + spacing)*2, y, buttonWidth, buttonHeight);
            todayButtonRect = new Rectangle(x + (buttonWidth + spacing)*3, y, buttonWidth, buttonHeight);
            showDistanceButtonRect = new Rectangle(x + (buttonWidth + spacing)*4, y, buttonWidth, buttonHeight);
            orderPlaceButtonRect = new Rectangle(x + (buttonWidth + spacing)*5, y, buttonWidth, buttonHeight);

            DrawButton(g, buttonRect, isButtonHovered, isLinesLocked ? "解鎖趨勢線" : "鎖定趨勢線");
            DrawButton(g, clearButtonRect, isClearButtonHovered, "清除上一次");
            DrawButton(g, clearAllButtonRect, isClearAllButtonHovered, "清除全部");
            DrawButton(g, todayButtonRect, isTodayButtonHovered, showTodayTrendLines ? "隱藏範圍線" : "顯示範圍線");
            DrawButton(g, showDistanceButtonRect, isShowDistanceButtonHovered, showDistance ? "清除選中趨勢線" : "啟動選中趨勢線");
            DrawButton(g, orderPlaceButtonRect, isOrderPlaceButtonHovered, IsOrderPlaceAllowed ? "關閉趨勢線開倉" : "啟用趨勢線開倉");

            // 繪製一般趨勢線
            foreach (var trendLine in trendLines)
            {
                DrawExtendedTrendLine(g, trendLine, mainWindow, Color.LightGray);
            }

            // 調試信息：顯示趨勢線統計
            if (trendLines.Count > 0)
            {
                var colorCounts = trendLines.GroupBy(t => t.CustomColor).ToDictionary(g => g.Key, g => g.Count());
                string debugInfo = $"趨勢線統計: 總數={trendLines.Count}";
                foreach (var kvp in colorCounts)
                {
                    debugInfo += $", {kvp.Key.Name}={kvp.Value}";
                }

                using (Font debugFont = new Font("Arial", 10))
                using (SolidBrush debugBrush = new SolidBrush(Color.Orange))
                {
                    g.DrawString(debugInfo, debugFont, debugBrush, 10, 10);
                }
            }

            // 繪製「今日」趨勢線
            if (showTodayTrendLines)
            {
                foreach (var trendLine in todayTrendLines)
                {
                    DrawExtendedTrendLine(g, trendLine, mainWindow, Color.Yellow);
                }
            }

            // 繪製鼠標提起目標點標記（如果有的話）
            if (hasMouseReleaseTarget && mouseReleasePoint != Point.Empty)
            {
                DrawMouseReleaseTargetInfo(g, mainWindow);

                // 如果文字正在顯示，需要定期重绘以更新顯示狀態
                if (showTargetText && targetTextShowTime != DateTime.MinValue)
                {
                    double elapsedSeconds = (DateTime.Now - targetTextShowTime).TotalSeconds;
                    if (elapsedSeconds <= TARGET_TEXT_DISPLAY_SECONDS + 0.1) // 稍微延長一點確保最後一次重绘
                    {
                        // 使用異步方式觸發重绘，避免在繪製過程中直接調用
                        System.Threading.Tasks.Task.Run(() =>
                        {
                            System.Threading.Thread.Sleep(100); // 等待100毫秒
                            try
                            {
                                this.CurrentChart?.RedrawBuffer();
                            }
                            catch { } // 忽略可能的異常
                        });
                    }
                }
            }

            // 如果正在畫線，在右上角顯示即時角度
            if (isDrawing)
            {
                double dragAngle = CalculateDragAngle();
                using (Font font = new Font("Arial", 14, FontStyle.Bold))
                using (SolidBrush brush = new SolidBrush(Color.Yellow))
                {
                    string angleText = $"拖拽角度: {dragAngle:F1}°";
                    float textX = mainWindow.ClientRectangle.Width - 200;
                    float textY = 10;
                    g.DrawString(angleText, font, brush, textX, textY);
                }
            }

            // 如果開啟了距離顯示，且有選中的趨勢線，則顯示距離
            if (showDistance && (selectedLongLine != null || selectedShortLine != null))
            {
                var currentBar = this.HistoricalData[0] as HistoryItemBar;
                if (currentBar != null)
                {
                    int currentIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(currentBar.TimeLeft);
                    List<string> infoLines = new List<string>();

                    // 檢查持倉狀態
                    bool hasPosition = GetPositions().Any();
                    infoLines.Add($"持倉狀態: {(hasPosition ? "有持倉" : "無持倉")}");

                    // 顯示趨勢線模式
                    string modeText = TrendLineMode == 1 ? "曲線模式" :
                                     TrendLineMode == 2 ? "直線模式" : "混合模式";
                    infoLines.Add($"趨勢線模式: {modeText}");

                    // 顯示鼠標拖拽角度（如果有的話）
                    if (hasMouseAngle)
                    {
                        infoLines.Add($"鼠標拖拽角度: {mouseSelectionAngle:F2}°");
                    }

                    // 顯示鼠標提起目標點信息（只在顯示期間）
                    if (hasMouseReleaseTarget && showTargetText)
                    {
                        infoLines.Add($"目標點: 索引{mouseReleaseIndex}, 價格{mouseReleasePrice:F6}");
                        infoLines.Add($"延長線容忍度: {MOUSE_RELEASE_EXTENSION_TOLERANCE:F1}px");
                        infoLines.Add($"延長線加分: {MOUSE_RELEASE_BONUS_SCORE:F1}");
                    }

                    // 顯示趨勢線價格和距離
                    if (selectedLongLine != null)
                    {
                        double longLinePrice = CalculateLinePrice(selectedLongLine, currentIndex);
                        double longDistance = currentBar.Close - longLinePrice;
                        double longLineAngle = CalculateTrendLineAngle(selectedLongLine);
                        infoLines.Add($"綠線價格: {longLinePrice:F2} High: {currentBar.High} Low: {currentBar.Low}");
                        infoLines.Add($"綠線距離: {longDistance:F2}");
                        infoLines.Add($"綠線角度: {longLineAngle:F2}°");
                        infoLines.Add($"綠線開倉: {IsOrderPlaceAllowed}");
                        infoLines.Add($"指標數值 {this.LDInd.GetValue(0,2)} {this.LDInd.GetValue(0,3)}");
                    }

                    if (selectedShortLine != null)
                    {
                        double shortLinePrice = CalculateLinePrice(selectedShortLine, currentIndex);
                        double shortDistance = currentBar.Close - shortLinePrice;
                        double shortLineAngle = CalculateTrendLineAngle(selectedShortLine);
                        infoLines.Add($"紅線價格: {shortLinePrice:F2} High: {currentBar.High} Low: {currentBar.Low}");
                        infoLines.Add($"紅線距離: {shortDistance:F2}");
                        infoLines.Add($"紅線角度: {shortLineAngle:F2}°");
                        infoLines.Add($"紅線開倉: {IsOrderPlaceAllowed}");
                        infoLines.Add($"指標數值 {this.LDInd.GetValue(0,2)} {this.LDInd.GetValue(0,3)}");
                    }

                    // 在圖表右上角顯示所有資訊
                    using (Font font = new Font("Arial", 12))
                    using (SolidBrush brush = new SolidBrush(Color.White))
                    {
                        float textX = mainWindow.ClientRectangle.Width - 300;
                        float textY = 30;
                        float lineHeight = font.GetHeight() + 5;

                        foreach (string line in infoLines)
                        {
                            g.DrawString(line, font, brush, textX, textY);
                            textY += lineHeight;
                        }
                    }
                }
            }

            // 繪製斐波那契強度圖例
            DrawFibonacciLegend(g, mainWindow);
        }
        finally
        {
            g.SetClip(prevClip);
        }
    }

    private void DrawButton(Graphics g, Rectangle rect, bool isHovered, string text)
    {
        using (SolidBrush brush = new SolidBrush(isHovered ? buttonHoverColor : buttonColor))
        {
            g.FillRectangle(brush, rect);
        }
        using (Pen pen = new Pen(Color.White, 1))
        {
            g.DrawRectangle(pen, rect);
        }
        using (Font font = new Font("Arial", 10))
        using (SolidBrush textBrush = new SolidBrush(Color.White))
        {
            SizeF textSize = g.MeasureString(text, font);
            float textX = rect.X + (rect.Width - textSize.Width) / 2;
            float textY = rect.Y + (rect.Height - textSize.Height) / 2;
            g.DrawString(text, font, textBrush, textX, textY);
        }
    }

    /// <summary>
    /// 繪製斐波那契強度圖例
    /// </summary>
    private void DrawFibonacciLegend(Graphics g, TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        try
        {
            // 只有當有趨勢線時才顯示圖例
            if (trendLines.Count == 0 && todayTrendLines.Count == 0) return;

            var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };
            var ratioNames = new[] { "斐波那契 0.618 (白色)", "黃金比例 1.618 (洋紅)" };
            var ratioColors = new[] { Color.FromArgb(255, 255, 255),  Color.FromArgb(255, 0, 255) }; // 純白色、純青色、純洋紅色

            using (Font font = new Font("Arial", 10))
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            {
                float startX = 10;
                float startY = mainWindow.ClientRectangle.Height - 100; // 左下角位置
                float lineHeight = 20;

                // 繪製標題
                g.DrawString("斐波那契強度圖例:", font, textBrush, startX, startY);
                startY += lineHeight;

                // 繪製每個比例的圖例
                for (int i = 0; i < fibonacciRatios.Length; i++)
                {
                    // 繪製顏色線條
                    using (Pen colorPen = new Pen(ratioColors[i], 3))
                    {
                        g.DrawLine(colorPen, startX, startY + 8, startX + 30, startY + 8);
                    }

                    // 繪製文字說明
                    string legendText = $"{ratioNames[i]} ({fibonacciRatios[i]:F3})";
                    g.DrawString(legendText, font, textBrush, startX + 35, startY);

                    startY += lineHeight;
                }
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] DrawFibonacciLegend: {ex.Message}");
        }
    }

    private void DrawExtendedTrendLine(Graphics g, TrendLine trendLine,
                                       TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow,
                                       Color lineColor)
    {
        // 如果是選中的綠色線（等待下穿）- 最高優先級
        if ((selectedLongLine == trendLine || selectedLongLines.Contains(trendLine)) && isSelectedLongLineActive)
        {
            lineColor = Color.FromArgb(144, 238, 144);  // 淺綠色 (LightGreen)
        }
        // 如果是選中的紅色線（等待上穿）- 最高優先級
        else if ((selectedShortLine == trendLine || selectedShortLines.Contains(trendLine)) && isSelectedShortLineActive)
        {
            lineColor = Color.FromArgb(255, 182, 193);  // 淺紅色 (LightPink)
        }
        // 優先使用自定義顏色（用於區分斐波那契強度）- 第二優先級
        else if (trendLine.CustomColor != Color.Empty)
        {
            lineColor = trendLine.CustomColor;
            // 如果是最近的線，使用明亮的白色覆蓋原色，更明顯
            if (nearestLine == trendLine || nearestLines.Contains(trendLine))
            {
                lineColor = Color.FromArgb(255, 255, 255, 255);  // 純白色，最大亮度
            }
        }
        // 如果是最近的線且沒有自定義顏色，使用明亮的白色
        else if (nearestLine == trendLine || nearestLines.Contains(trendLine))
        {
            lineColor = Color.FromArgb(255, 255, 255, 255);  // 純白色，最大亮度
        }

        // 計算基本坐標（用於延伸線）
        float x1 = (float)mainWindow.CoordinatesConverter.GetChartX(trendLine.StartTime);
        float x2 = (float)mainWindow.CoordinatesConverter.GetChartX(trendLine.EndTime);

        float y1 = (float)mainWindow.CoordinatesConverter.GetChartY(
            trendLine.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.StartTime)
            + trendLine.Intercept);

        float y2 = (float)mainWindow.CoordinatesConverter.GetChartY(
            trendLine.Slope * (float)mainWindow.CoordinatesConverter.GetBarIndex(trendLine.EndTime)
            + trendLine.Intercept);

        // 繪製主線段 - 支持弧線和直線
        // 鼠標懸停時使用更粗的線條
        int lineWidth = 1;
        if (selectedLongLine == trendLine || selectedShortLine == trendLine ||
            selectedLongLines.Contains(trendLine) || selectedShortLines.Contains(trendLine))
            lineWidth = 2;  // 選中的線用2像素
        else if (nearestLine == trendLine || nearestLines.Contains(trendLine))
            lineWidth = 3;  // 鼠標懸停的線用3像素，更明顯

        using (Pen pen = new Pen(lineColor, lineWidth))
        {
            if (trendLine.IsCurve)
            {
                // 繪製斐波那契弧線
                DrawFibonacciArc(g, pen, trendLine, mainWindow);
            }
            else
            {
                // 繪製直線
                g.DrawLine(pen, x1, y1, x2, y2);
            }
        }

        // 繪製延伸線（弧線或直線）
        Color dashedColor;

        // 鼠標懸停時優先使用白色，不管是否有自定義顏色
        if (nearestLine == trendLine || nearestLines.Contains(trendLine))
        {
            dashedColor = Color.FromArgb(255, 255, 255, 255);  // 鼠標懸停時用純白色，最大亮度
        }
        else if ((selectedLongLine == trendLine || selectedLongLines.Contains(trendLine)) && isSelectedLongLineActive)
        {
            dashedColor = Color.FromArgb(144, 238, 144);  // 淺綠色 (LightGreen)
        }
        else if ((selectedShortLine == trendLine || selectedShortLines.Contains(trendLine)) && isSelectedShortLineActive)
        {
            dashedColor = Color.FromArgb(255, 182, 193);  // 淺紅色 (LightPink)
        }
        else if (trendLine.CustomColor != Color.Empty)
        {
            dashedColor = Color.FromArgb(128, trendLine.CustomColor.R, trendLine.CustomColor.G, trendLine.CustomColor.B); // 50% 透明度
        }
        else
        {
            dashedColor = Color.Gray;  // 其他線用灰色
        }

        // 延長線保持1像素粗細，不需要變粗
        int dashedLineWidth = 1;
        if (selectedLongLine == trendLine || selectedShortLine == trendLine ||
            selectedLongLines.Contains(trendLine) || selectedShortLines.Contains(trendLine))
            dashedLineWidth = 2;  // 選中的線用2像素
        // 鼠標懸停時延長線不變粗，保持1像素

        using (Pen dashedPen = new Pen(dashedColor, dashedLineWidth))
        {
            dashedPen.DashStyle = DashStyle.Dash;

            if (trendLine.IsCurve)
            {
                // 繪製弧線延伸
                DrawArcExtensions(g, dashedPen, trendLine, mainWindow);
            }
            else
            {
                // 繪製直線延伸
                DrawStraightExtensions(g, dashedPen, trendLine, mainWindow, x1, y1, x2, y2);
            }
        }
    }

    #endregion

    #region 今日趨勢線示範

    private List<Region> SplitIntoRegions(int startIndex, int endIndex)
    {
        List<Region> regions = new List<Region>();
        for (int i = startIndex; i <= endIndex; i += REGION_SIZE)
        {
            regions.Add(new Region
            {
                StartIndex = i,
                EndIndex = Math.Min(i + REGION_SIZE - 1, endIndex)
            });
        }
        return regions;
    }

    private bool IsLineInRegion(TrendLineCandidate line, Region region)
    {
        return (line.StartPoint.Index >= region.StartIndex && line.StartPoint.Index <= region.EndIndex) ||
               (line.EndPoint.Index >= region.StartIndex && line.EndPoint.Index <= region.EndIndex);
    }

    private bool AreLinesTooClose(TrendLineCandidate line1, TrendLineCandidate line2)
    {
        return Math.Abs(line1.StartPoint.Index - line2.StartPoint.Index) < MIN_DISTANCE &&
               Math.Abs(line1.EndPoint.Index - line2.EndPoint.Index) < MIN_DISTANCE;
    }
    // 添加計算ATR的方法
        private double CalculateATR(int startIndex, int endIndex)
        {
            if (startIndex >= endIndex) return 0;

            List<double> trueRanges = new List<double>();
            double multiplier = 1.0; // ATR倍數，可以根據需要調整
          
            // 向前和向後多取K線來計算更準確的ATR
            int extendedStartIndex = Math.Max(0, startIndex - 60); // 向前多取60根K線
            int extendedEndIndex = Math.Min(this.HistoricalData.Count - 1, endIndex + 60); // 向後多取60根K線，但不超過數據範圍
            
            
            // 計算每根K線的TR
            for (int i = extendedStartIndex; i <= extendedEndIndex; i++)
            {
                if (i == 0) continue;

                var currentBar = this.HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                var previousBar = this.HistoricalData[i - 1, SeekOriginHistory.Begin] as HistoryItemBar;

                if (currentBar == null || previousBar == null) continue;

                // 計算三種範圍
                double tr1 = Math.Abs(currentBar.High - currentBar.Low);
                double tr2 = Math.Abs(currentBar.High - previousBar.Close);
                double tr3 = Math.Abs(currentBar.Low - previousBar.Close);

                // TR是三者中的最大值
                double tr = Math.Max(tr1, Math.Max(tr2, tr3));
                trueRanges.Add(tr);
            }

            // 計算平均值並乘以倍數
            return trueRanges.Count > 0 ? trueRanges.Average() * multiplier : 0;
        }
    private void CalculateTodayTrendLines()
    {
        todayTrendLines.Clear();
        var mainWindow = this.CurrentChart.MainWindow;

        Core.Instance.Loggers.Log($"[DEBUG] CalculateTodayTrendLines started - TrendLineMode: {TrendLineMode}");
        
        // 獲取可見區域的時間範圍
        DateTime visibleStartTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Left);
        DateTime visibleEndTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Right);
        DateTime currentTime = HistoricalData[HistoricalData.Count - 1, SeekOriginHistory.Begin].TimeLeft;
        
        if (visibleEndTime > currentTime)
            visibleEndTime = currentTime;
        
        // 轉換為索引範圍
        int fullStartIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(visibleStartTime);
        int fullEndIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(visibleEndTime);
        
        fullEndIndex = Math.Min(fullEndIndex, HistoricalData.Count - 1);
        if (fullStartIndex > fullEndIndex)
        {
            fullStartIndex = fullEndIndex - 100;
        }
        fullStartIndex = Math.Max(0, fullStartIndex);

        // 1. 找出所有主要高低點
        List<PivotPoint> pivots = FindAllPivotPoints(fullStartIndex, fullEndIndex);
        Core.Instance.Loggers.Log($"[DEBUG] Found {pivots.Count} pivot points in range [{fullStartIndex}, {fullEndIndex}]");
        if (pivots.Count < 2)
        {
            Core.Instance.Loggers.Log($"[DEBUG] Not enough pivot points ({pivots.Count}), trying to create simple test lines");

            // 如果找不到足夠的pivot點，創建一些測試線
            if (TrendLineMode == 2) // 直線模式
            {
                CreateTestRangeLines(fullStartIndex, fullEndIndex);
                return;
            }
            else if (TrendLineMode == 3) // 混合模式
            {
                CreateTestRangeLines(fullStartIndex, fullEndIndex);
                return;
            }
            else
            {
                Core.Instance.Loggers.Log($"[DEBUG] Curve mode requires pivot points, returning");
                return;
            }
        }

        // 2. 將區域分割成小塊
        List<Region> regions = SplitIntoRegions(fullStartIndex, fullEndIndex);

        // 3. 生成所有候選趨勢線
        List<TrendLineCandidate> allCandidates = new List<TrendLineCandidate>();
        for (int i = 0; i < pivots.Count - 1; i++)
        {
            for (int j = i + 1; j < pivots.Count; j++)
            {
                var cand = CreateCandidateLine(pivots[i], pivots[j]);
                if (cand == null) continue;

                double score = ScoreCandidate(cand, true);
                cand.Score = score;
                if (score > 0)
                {
                    allCandidates.Add(cand);
                }
            }
        }

        // 4. 按分數排序候選線
        allCandidates = allCandidates.OrderByDescending(c => c.Score).ToList();
        Core.Instance.Loggers.Log($"[DEBUG] Generated {allCandidates.Count} valid candidates");

        // 5. 遞迴選擇趨勢線
        List<TrendLineCandidate> selectedLines = new List<TrendLineCandidate>();
        HashSet<int> processedIndices = new HashSet<int>(); // 記錄已處理的索引
        int currentBatch = 0; // 當前批次號
        const int BATCH_SIZE = 50; // 每批處理的線數

        while (selectedLines.Count < MAX_RANGE_LINES && allCandidates.Any())
        {
            // 選擇當前批次的最佳候選線
            var currentBatchCandidates = allCandidates
                .Where(cand => !processedIndices.Contains(cand.StartPoint.Index) && 
                              !processedIndices.Contains(cand.EndPoint.Index))
                .Take(BATCH_SIZE)
                .ToList();

            if (!currentBatchCandidates.Any()) break;

            // 檢查是否有起點太接近的線
            var closeStartPoints = new List<TrendLineCandidate>();
            foreach (var cand in currentBatchCandidates)
            {
                bool isCloseToExisting = selectedLines.Any(line => 
                    Math.Abs(line.StartPoint.Index - cand.StartPoint.Index) < MIN_DISTANCE ||
                    Math.Abs(line.EndPoint.Index - cand.EndPoint.Index) < MIN_DISTANCE);

                if (isCloseToExisting)
                {
                    closeStartPoints.Add(cand);
                }
                else
                {
                    selectedLines.Add(cand);
                    processedIndices.Add(cand.StartPoint.Index);
                    processedIndices.Add(cand.EndPoint.Index);
                    allCandidates.Remove(cand);

                    // 更新區域覆蓋狀態
                    foreach (var region in regions)
                    {
                        if (IsLineInRegion(cand, region))
                        {
                            region.HasTrendLine = true;
                            region.Candidates.Add(cand);
                        }
                    }
                }
            }

            // 如果有起點太接近的線，保留它們並繼續尋找新的線
            if (closeStartPoints.Any())
            {
                foreach (var cand in closeStartPoints)
                {
                    selectedLines.Add(cand);
                    processedIndices.Add(cand.StartPoint.Index);
                    processedIndices.Add(cand.EndPoint.Index);
                    allCandidates.Remove(cand);

                    // 更新區域覆蓋狀態
                    foreach (var region in regions)
                    {
                        if (IsLineInRegion(cand, region))
                        {
                            region.HasTrendLine = true;
                            region.Candidates.Add(cand);
                        }
                    }
                }

                // 繼續尋找新的候選線
                currentBatch++;
                continue;
            }

            // 如果沒有太接近的線，移除已處理的候選線
            foreach (var cand in currentBatchCandidates)
            {
                allCandidates.Remove(cand);
            }
            currentBatch++;
        }

        // 6. 將選中的趨勢線添加到結果中
        foreach (var cand in selectedLines)
        {
            if (TrendLineMode == 2) // 直線模式 - 與參考文件保持一致
            {
                // 創建單一直線趨勢線（與參考文件相同的實現）
                var trendLine = new TrendLine
                {
                    StartTime = HistoricalData[cand.StartPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                    EndTime = HistoricalData[cand.EndPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                    StartPriceType = cand.StartPoint.PriceType,
                    EndPriceType = cand.EndPoint.PriceType,
                    Slope = (cand.EndPoint.Price - cand.StartPoint.Price) / (cand.EndPoint.Index - cand.StartPoint.Index),
                    Intercept = cand.StartPoint.Price -
                                ((cand.EndPoint.Price - cand.StartPoint.Price) / (cand.EndPoint.Index - cand.StartPoint.Index))
                                * cand.StartPoint.Index,
                    IsCurve = false // 直線模式
                };
                todayTrendLines.Add(trendLine);
            }
            else if (TrendLineMode == 3) // 混合模式（同時顯示曲線和直線）
            {
                double slope = (cand.EndPoint.Price - cand.StartPoint.Price) / (cand.EndPoint.Index - cand.StartPoint.Index);
                double intercept = cand.StartPoint.Price - slope * cand.StartPoint.Index;

                // 首先創建直線趨勢線
                var straightLine = new TrendLine
                {
                    StartTime = HistoricalData[cand.StartPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                    EndTime = HistoricalData[cand.EndPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                    StartPriceType = cand.StartPoint.PriceType,
                    EndPriceType = cand.EndPoint.PriceType,
                    Slope = slope,
                    Intercept = intercept,
                    IsCurve = false, // 直線模式
                    CustomColor = Color.FromArgb(128, 128, 128) // 灰色用於區分
                };
                todayTrendLines.Add(straightLine);

                // 然後創建斐波那契弧線趨勢線
                var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };
                var ratioNames = new[] { "FIBONACCI_RATIO", "GOLDEN_RATIO" };
                var ratioColors = new[] { Color.FromArgb(255, 255, 0),  Color.FromArgb(255, 0, 255) }; // 純黃色、純洋紅色

                for (int i = 0; i < fibonacciRatios.Length; i++)
                {
                    var trendLine = new TrendLine
                    {
                        StartTime = HistoricalData[cand.StartPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                        EndTime = HistoricalData[cand.EndPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                        StartPriceType = cand.StartPoint.PriceType,
                        EndPriceType = cand.EndPoint.PriceType,
                        Slope = slope,
                        Intercept = intercept,
                        IsCurve = true, // 啟用弧線模式
                        FibonacciCurvature = fibonacciRatios[i],
                        CustomColor = ratioColors[i] // 設置自定義顏色
                    };

                    // 計算斐波那契弧線係數
                    try
                    {
                        CalculateFibonacciArcCoefficients(cand.StartPoint, cand.EndPoint, trendLine, fibonacciRatios[i]);
                        Core.Instance.Loggers.Log($"[DEBUG] Created today trend line with {ratioNames[i]} ({fibonacciRatios[i]:F6})");
                    }
                    catch (Exception ex)
                    {
                        Core.Instance.Loggers.Log($"[ERROR] CalculateTodayTrendLines - Failed to calculate arc coefficients for {ratioNames[i]}: {ex.Message}");
                        trendLine.IsCurve = false; // 降級為直線
                    }

                    todayTrendLines.Add(trendLine);
                }
            }
            else // 曲線模式
            {
                double slope = (cand.EndPoint.Price - cand.StartPoint.Price) / (cand.EndPoint.Index - cand.StartPoint.Index);
                double intercept = cand.StartPoint.Price - slope * cand.StartPoint.Index;

                // 創建多個版本的趨勢線，分別使用不同的斐波那契比例和顏色
                var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };
                var ratioNames = new[] { "FIBONACCI_RATIO", "GOLDEN_RATIO" };
                var ratioColors = new[] { Color.FromArgb(255, 255, 0),  Color.FromArgb(255, 0, 255) }; // 純黃色、純洋紅色

                for (int i = 0; i < fibonacciRatios.Length; i++)
                {
                    var trendLine = new TrendLine
                    {
                        StartTime = HistoricalData[cand.StartPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                        EndTime = HistoricalData[cand.EndPoint.Index, SeekOriginHistory.Begin].TimeLeft,
                        StartPriceType = cand.StartPoint.PriceType,
                        EndPriceType = cand.EndPoint.PriceType,
                        Slope = slope,
                        Intercept = intercept,
                        IsCurve = true, // 啟用弧線模式
                        FibonacciCurvature = fibonacciRatios[i],
                        CustomColor = ratioColors[i] // 設置自定義顏色
                    };

                    // 計算斐波那契弧線係數
                    try
                    {
                        CalculateFibonacciArcCoefficients(cand.StartPoint, cand.EndPoint, trendLine, fibonacciRatios[i]);
                        Core.Instance.Loggers.Log($"[DEBUG] Created today trend line with {ratioNames[i]} ({fibonacciRatios[i]:F6})");
                    }
                    catch (Exception ex)
                    {
                        Core.Instance.Loggers.Log($"[ERROR] CalculateTodayTrendLines - Failed to calculate arc coefficients for {ratioNames[i]}: {ex.Message}");
                        trendLine.IsCurve = false; // 降級為直線
                    }

                    todayTrendLines.Add(trendLine);
                }
            }
        }

        Core.Instance.Loggers.Log($"[DEBUG] CalculateTodayTrendLines completed - Created {todayTrendLines.Count} trend lines from {selectedLines.Count} candidates");
    }

    /// <summary>
    /// 創建測試範圍線（當找不到足夠pivot點時使用）
    /// </summary>
    private void CreateTestRangeLines(int startIndex, int endIndex)
    {
        Core.Instance.Loggers.Log($"[DEBUG] CreateTestRangeLines: Creating test lines for range [{startIndex}, {endIndex}]");

        try
        {
            // 確保有足夠的數據
            if (endIndex - startIndex < 10)
            {
                Core.Instance.Loggers.Log($"[DEBUG] Range too small for test lines");
                return;
            }

            // 創建一些基於高低點的簡單趨勢線
            int step = Math.Max(5, (endIndex - startIndex) / 20); // 每5-20根K線取一個點

            for (int i = startIndex; i < endIndex - step * 2; i += step)
            {
                if (todayTrendLines.Count >= 50) break; // 限制數量

                try
                {
                    var bar1 = HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                    var bar2 = HistoricalData[i + step, SeekOriginHistory.Begin] as HistoryItemBar;

                    if (bar1 == null || bar2 == null) continue;

                    // 創建高點連線
                    var highLine = new TrendLine
                    {
                        StartTime = bar1.TimeLeft,
                        EndTime = bar2.TimeLeft,
                        StartPriceType = PriceType.High,
                        EndPriceType = PriceType.High,
                        Slope = (bar2.High - bar1.High) / step,
                        Intercept = bar1.High - ((bar2.High - bar1.High) / step) * i,
                        IsCurve = false
                    };
                    todayTrendLines.Add(highLine);

                    // 創建低點連線
                    var lowLine = new TrendLine
                    {
                        StartTime = bar1.TimeLeft,
                        EndTime = bar2.TimeLeft,
                        StartPriceType = PriceType.Low,
                        EndPriceType = PriceType.Low,
                        Slope = (bar2.Low - bar1.Low) / step,
                        Intercept = bar1.Low - ((bar2.Low - bar1.Low) / step) * i,
                        IsCurve = false
                    };
                    todayTrendLines.Add(lowLine);
                }
                catch (Exception ex)
                {
                    Core.Instance.Loggers.Log($"[ERROR] CreateTestRangeLines: Error creating line at index {i}: {ex.Message}");
                }
            }

            Core.Instance.Loggers.Log($"[DEBUG] CreateTestRangeLines completed - Created {todayTrendLines.Count} test lines");
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CreateTestRangeLines: {ex.Message}");
        }
    }

    #endregion

    #region 鼠標提起Pivot點繪製方法

    /// <summary>
    /// 繪製鼠標提起目標點信息（跟隨圖表時間軸，圓點和文字都定時隱藏）
    /// </summary>
    private void DrawMouseReleaseTargetInfo(Graphics g, TradingPlatform.BusinessLayer.Chart.IChartWindow mainWindow)
    {
        try
        {
            if (!hasMouseReleaseTarget) return;

            // 檢查圓點和文字是否應該隱藏
            if (showTargetText && targetTextShowTime != DateTime.MinValue)
            {
                double elapsedSeconds = (DateTime.Now - targetTextShowTime).TotalSeconds;
                if (elapsedSeconds > TARGET_TEXT_DISPLAY_SECONDS)
                {
                    showTargetText = false;
                    return; // 時間到了，完全不顯示
                }
            }
            else if (!showTargetText)
            {
                return; // 不顯示任何內容
            }

            // 使用保存的時間和價格坐標，而不是屏幕坐標
            float targetX = (float)mainWindow.CoordinatesConverter.GetChartX(mouseReleaseTime);
            float targetY = (float)mainWindow.CoordinatesConverter.GetChartY(mouseReleasePrice);

            // 檢查目標點是否在可見範圍內
            if (targetX < mainWindow.ClientRectangle.Left || targetX > mainWindow.ClientRectangle.Right)
                return; // 不在可見範圍內，不繪製

            // 繪製鼠標提起目標點（使用圖表坐標）- 圓點和文字都在時間內顯示
            using (SolidBrush brush = new SolidBrush(Color.Purple))
            {
                int pointSize = 8;
                g.FillEllipse(brush, targetX - pointSize/2, targetY - pointSize/2, pointSize, pointSize);
            }

            // 顯示文字
            using (Font font = new Font("Arial", 9, FontStyle.Bold))
            using (SolidBrush textBrush = new SolidBrush(Color.Purple))
            {
                string targetText = $"延長線目標點";
                float textX = targetX + 15;
                float textY = targetY - 10;

                // 確保文字不超出邊界
                if (textX + 120 > mainWindow.ClientRectangle.Width)
                    textX = targetX - 120;
                if (textY < 0)
                    textY = targetY + 20;

                g.DrawString(targetText, font, textBrush, textX, textY);
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] DrawMouseReleaseTargetInfo: {ex.Message}");
        }
    }

    #endregion

    #region 鼠標提起點作為延長線目標點

    /// <summary>
    /// 創建鼠標提起點對應的目標點（用於延長線加分）
    /// </summary>
    private void CreateMouseReleaseTarget()
    {
        hasMouseReleaseTarget = false;

        if (mouseReleasePoint == Point.Empty)
            return;

        try
        {
            var mainWindow = this.CurrentChart.MainWindow;
            if (mainWindow == null) return;

            // 將鼠標提起點轉換為價格和時間
            DateTime releaseTime = mainWindow.CoordinatesConverter.GetTime(mouseReleasePoint.X);
            double releasePrice = mainWindow.CoordinatesConverter.GetPrice(mouseReleasePoint.Y);
            int releaseIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(releaseTime);

            // 保存時間和價格信息
            mouseReleaseTime = releaseTime;
            mouseReleaseIndex = releaseIndex;
            mouseReleasePrice = releasePrice;

            // 檢查索引是否在有效範圍內
            if (releaseIndex < 0 || releaseIndex >= this.HistoricalData.Count)
            {
                Core.Instance.Loggers.Log($"[DEBUG] Mouse release index {releaseIndex} out of range [0, {this.HistoricalData.Count-1}]");
                return;
            }

            hasMouseReleaseTarget = true;

            // 啟動文字顯示計時
            showTargetText = true;
            targetTextShowTime = DateTime.Now;

            Core.Instance.Loggers.Log($"[DEBUG] Created mouse release target: index={releaseIndex}, price={releasePrice:F6}");
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CreateMouseReleaseTarget: {ex.Message}");
            hasMouseReleaseTarget = false;
        }
    }



    #endregion

    #region 計算角度相關方法

    /// <summary>
    /// 計算趨勢線的角度（用於顯示）
    /// </summary>
    private double CalculateTrendLineAngle(TrendLine trendLine)
    {
        try
        {
            var mainWindow = this.CurrentChart.MainWindow;
            if (mainWindow == null || trendLine == null) return 0;

            // 使用趨勢線的起點和終點計算角度
            // 假設我們使用當前可見範圍的兩個點來計算角度
            int currentIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(DateTime.Now);
            int startIndex = Math.Max(0, currentIndex - 100); // 往前100根K線作為起點

            double startPrice = trendLine.Slope * startIndex + trendLine.Intercept;
            double endPrice = trendLine.Slope * currentIndex + trendLine.Intercept;

            // 將索引轉換為像素座標
            int barsWidth = this.CurrentChart.BarsWidth;
            double pixelTimeSpan = (currentIndex - startIndex) * barsWidth;

            // 將價格轉換為像素座標
            double startY = mainWindow.CoordinatesConverter.GetChartY(startPrice);
            double endY = mainWindow.CoordinatesConverter.GetChartY(endPrice);
            double pixelPriceChange = Math.Abs(endY - startY);

            // 計算實際的像素斜率
            if (pixelTimeSpan == 0) return 0;
            double pixelSlope = pixelPriceChange / pixelTimeSpan;

            // 計算角度（以度為單位）
            double angleDeg = Math.Atan(pixelSlope) * 180.0 / Math.PI;

            // 保持角度的符號（上升為正，下降為負）
            if (endPrice < startPrice) angleDeg = -angleDeg;

            return angleDeg;
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[ERROR] CalculateTrendLineAngle: {ex.Message}");
            return 0;
        }
    }

    #region 計算滑鼠拖曳角度 (可自由使用)

    private double CalculateDragAngle()
    {
        if (startPoint == Point.Empty || endPoint == Point.Empty)
            return 0;

        // 使用像素坐標計算角度，但要修正Y軸方向以匹配趨勢線角度
        double deltaX = endPoint.X - startPoint.X;
        double deltaY = endPoint.Y - startPoint.Y;

        if (deltaX == 0)
        {
            // 修正Y軸方向：屏幕向下為正，但價格向上為正
            return deltaY < 0 ? 90 : -90;  // 注意這裡反轉了符號
        }

        double pixelSlope = Math.Abs(deltaY) / Math.Abs(deltaX);
        double angle = Math.Atan(pixelSlope) * 180.0 / Math.PI;

        // 修正角度符號以匹配趨勢線角度：
        // 屏幕坐標：向右向下拖拽 (deltaX > 0, deltaY > 0) 應該對應下降趨勢（負角度）
        // 屏幕坐標：向右向上拖拽 (deltaX > 0, deltaY < 0) 應該對應上升趨勢（正角度）
        if (deltaY > 0) angle = -angle;  // 向下拖拽 = 下降趨勢 = 負角度
        // deltaY < 0 時保持正角度（向上拖拽 = 上升趨勢 = 正角度）

        return angle;
    }

    #endregion


    #region Orders
    private void CloseAllPositions()
    {
        Core.AdvancedTradingOperations.Flatten(this.Symbol, this.CurrentChart.Account);
            
            
    }

    private double GetPercentATR()
    {
        var mainWindow = this.CurrentChart.MainWindow;
        
        // 獲取可見區域的時間範圍
        DateTime startTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Left);
        DateTime endTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Right);
        DateTime currentTime = HistoricalData[HistoricalData.Count - 1, SeekOriginHistory.Begin].TimeLeft;
        
        if (endTime > currentTime)
            endTime = currentTime;
        
        // 計算畫面中間80%的範圍
        int visibleBars = (int)(mainWindow.CoordinatesConverter.GetBarIndex(endTime) - mainWindow.CoordinatesConverter.GetBarIndex(startTime));
        int middle80PercentBars = (int)(visibleBars * 0.618);
        int offset = (visibleBars - middle80PercentBars) / 2;

        int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(startTime) + offset;
        int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(endTime) - offset;

        // 確保索引在有效範圍內
        startIndex = Math.Max(0, startIndex);
        endIndex = Math.Min(endIndex, HistoricalData.Count - 1);

        // 使用CalculateATR計算當前可見範圍的ATR
        return CalculateATR(startIndex, endIndex);
    }

    /// <summary>
    /// 調試：輸出當前商品的關鍵參數信息
    /// </summary>
    private void LogSymbolDebugInfo()
    {
        try
        {
            string symbolName = this.Symbol?.Name ?? "Unknown";
            var currentBar = this.HistoricalData[0] as HistoryItemBar;
            if (currentBar != null)
            {
                double currentPrice = currentBar.Close;
                double priceRange = currentBar.High - currentBar.Low;
                double priceRangePercent = priceRange / currentPrice * 100;

                Core.Instance.Loggers.Log($"[DEBUG] === Symbol Debug Info ===");
                Core.Instance.Loggers.Log($"[DEBUG] Symbol: {symbolName}");
                Core.Instance.Loggers.Log($"[DEBUG] Current Price: {currentPrice:F6}");
                Core.Instance.Loggers.Log($"[DEBUG] Current Bar Range: {priceRange:F6} ({priceRangePercent:F3}%)");

                // 顯示BarsWidth相關信息
                int barsWidth = this.CurrentChart.BarsWidth;
                var mainWindow = this.CurrentChart.MainWindow;
                if (mainWindow != null)
                {
                    double visibleHighPrice = mainWindow.CoordinatesConverter.GetPrice(mainWindow.ClientRectangle.Top);
                    double visibleLowPrice = mainWindow.CoordinatesConverter.GetPrice(mainWindow.ClientRectangle.Bottom);
                    double visiblePriceRange = Math.Abs(visibleHighPrice - visibleLowPrice);
                    int chartHeight = mainWindow.ClientRectangle.Height;
                    double pricePerPixel = visiblePriceRange / chartHeight;

                    Core.Instance.Loggers.Log($"[DEBUG] Chart Info: BarsWidth={barsWidth}px, ChartHeight={chartHeight}px");
                    Core.Instance.Loggers.Log($"[DEBUG] Visible Price Range: {visibleLowPrice:F6} to {visibleHighPrice:F6} (span={visiblePriceRange:F6})");
                    Core.Instance.Loggers.Log($"[DEBUG] Price Per Pixel: {pricePerPixel:F8}");
                }

                Core.Instance.Loggers.Log($"[DEBUG] Dynamic Touch Threshold: {GetDynamicTouchThreshold(currentPrice):F6}");
                Core.Instance.Loggers.Log($"[DEBUG] MIN_ABS_ANGLE: {MIN_ABS_ANGLE}°, MAX_ABS_ANGLE: {MAX_ABS_ANGLE}°");
                Core.Instance.Loggers.Log($"[DEBUG] MIN_TIME_SPAN: {MIN_TIME_SPAN}, MAX_TIME_SPAN: {MAX_TIME_SPAN}");
                Core.Instance.Loggers.Log($"[DEBUG] MIN_TOUCH_POINTS: {MIN_TOUCH_POINTS}");
                Core.Instance.Loggers.Log($"[DEBUG] === End Debug Info ===");
            }
        }
        catch (Exception ex)
        {
            Core.Instance.Loggers.Log($"[DEBUG] Error in LogSymbolDebugInfo: {ex.Message}");
        }
    }

    #endregion

#endregion
}



