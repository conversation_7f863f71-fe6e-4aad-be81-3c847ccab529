// Copyright QUANTOWER LLC. © 2017-2023. All rights reserved.

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using TradingPlatform.BusinessLayer;

namespace TrendIndicators;

public class MarkBuySell : Indicator
{
    #region Constants
    private const int N = 14;                          // WR週期
    private const double CH_WIDTH = 55;                // 通道寬度
    private const int MIN_LENGTH = 14;                 // 最小通道長度
    private const double FIBONACCI_EXTENSION = 1.618;  // 斐波那契擴展
    private const double WR_BUY_THRESHOLD = -75;       // WR買入閾值
    private const double WR_SELL_THRESHOLD = -25;      // WR賣出閾值
    private const int LOOKBACK_PERIOD = 60;            // 回溯週期
    private const int MIN_COUNT_THRESHOLD = 4;         // 最小計數閾值
    private const double CP_THRESHOLD = 0.8;           // CP閾值
    #endregion

    #region Historical Data Lists
    private readonly List<double> wrValues = new();
    private readonly List<double> upLines = new();
    private readonly List<double> downLines = new();
    private readonly List<bool> isChannels = new();
    private readonly List<bool> inChannels = new();
    private readonly List<double?> u2Values = new();
    private readonly List<double?> d2Values = new();
    private readonly List<double> udBoxValues = new();
    private readonly List<bool> readyBuys = new();
    private readonly List<bool> readySells = new();

    // 做多相關數據
    private readonly List<double> longFrontHighs = new();
    private readonly List<double> longDiffs = new();
    private readonly List<bool> longValids = new();
    private readonly List<double> long382Prices = new();
    private readonly List<double> long236Prices = new();
    private readonly List<int> longAValues = new();
    private readonly List<int> longBValues = new();
    private readonly List<int> longCCValues = new();
    private readonly List<bool> longValid2s = new();
    private readonly List<bool> longValid3s = new();
    private readonly List<bool> buySignals = new();

    // 做空相關數據
    private readonly List<double> shortFrontLows = new();
    private readonly List<double> shortDiffs = new();
    private readonly List<bool> shortValids = new();
    private readonly List<double> short382Prices = new();
    private readonly List<double> short236Prices = new();
    private readonly List<int> shortAValues = new();
    private readonly List<int> shortBValues = new();
    private readonly List<int> shortCCValues = new();
    private readonly List<bool> shortValid2s = new();
    private readonly List<bool> shortValid3s = new();
    private readonly List<bool> sellSignals = new();
    #endregion

    #region Parameters
    [InputParameter("Up color", 10)]
    public Color DefaultUpColor
    {
        get => this.upColor;
        set
        {
            this.upColor = value;
            this.upPen = new Pen(value);
        }
    }
    private Color upColor;
    private Pen upPen;

    [InputParameter("Down color", 20)]
    public Color DefaultDownColor
    {
        get => this.downColor;
        set
        {
            this.downColor = value;
            this.downPen = new Pen(value);
        }
    }
    private Color downColor;
    private Pen downPen;

    
    #endregion

    public MarkBuySell()
    {
        this.Name = "MarkBuySell";
        this.DefaultUpColor = Color.FromArgb(235, 96, 47);
        this.DefaultDownColor = Color.FromArgb(55, 219, 186);
        
        this.AddLineSeries("WR", Color.Gray, 1, LineStyle.Points);
        this.AddLineSeries("Channel", Color.Gray, 1, LineStyle.Points);
        this.AddLineSeries("BuySignal", this.DefaultUpColor, 2, LineStyle.Points);
        this.AddLineSeries("SellSignal", this.DefaultDownColor, 2, LineStyle.Points);

        
    }

    #region Helper Methods
    private double CalculateWR(int index)
    {
        if (index < N) return 0;

        double highestHigh = double.MinValue;
        double lowestLow = double.MaxValue;
        double close = Close(0); // 當前K線

        for (int i = 0; i < N; i++)
        {
            highestHigh = Math.Max(highestHigh, High(i));
            lowestLow = Math.Min(lowestLow, Low(i));
        }

        return highestHigh - lowestLow == 0 ? 0 : -100 * (highestHigh - close) / (highestHigh - lowestLow);
    }
    
    private double GetHighestWR(int index, int period)
    {
        if (index < period) return 0;
        double highest = double.MinValue;
        for (int i = 0; i < period; i++)
            highest = Math.Max(highest, wrValues[index - i]);
        return highest;
    }
    
    private double GetLowestWR(int index, int period)
    {
        if (index < period) return 0;
        double lowest = double.MaxValue;
        for (int i = 0; i < period; i++)
            lowest = Math.Min(lowest, wrValues[index - i]);
        return lowest;
    }

    /// <summary>
    /// BARSLAST函数：返回条件最近一次为true的K线距离
    /// </summary>
    private int BarsLast(Func<int, bool> condition, int startIndex, int maxLookback)
    {
        for (int i = 0; i < maxLookback && startIndex - i >= 0; i++)
            if (condition(startIndex - i))
                return i;
        return maxLookback; // 如果没找到，返回最大回溯期
    }

    /// <summary>
    /// FINDHIGH函数：寻找周期内的最高值
    /// </summary>
    private double FindHigh(Func<int, double> valueSelector, int index, int period)
    {
        double highest = double.MinValue;
        for (int i = 0; i < period && index - i >= 0; i++)
            highest = Math.Max(highest, valueSelector(i)); // 從當前K線開始倒數
        return highest;
    }

    /// <summary>
    /// FINDLOW函数：寻找周期内的最低值
    /// </summary>
    private double FindLow(Func<int, double> valueSelector, int index, int period)
    {
        double lowest = double.MaxValue;
        for (int i = 0; i < period && index - i >= 0; i++)
            lowest = Math.Min(lowest, valueSelector(i)); // 從當前K線開始倒數
        return lowest;
    }

    

    /// <summary>
    /// EXIST函数：检查在指定周期内是否存在满足条件的K线
    /// </summary>
    private bool Exist(Func<int, bool> condition, int startIndex, int lookbackPeriod)
    {
        for (int i = 0; i <= lookbackPeriod && startIndex - i >= 0; i++)
            if (condition(startIndex - i))
                return true;
        return false;
    }

    

    

    /// <summary>
    /// 計算FARSELLDAYS - 找到最後一次READYSELL信號的位置，其中BARSLAST(READYSELL)等於MAX(BARSLAST(READYBUY),BARSLAST(READYSELL))
    /// </summary>
    private int CalculateFarSellDays(int currentIndex)
    {
        for (int i = 0; i < LOOKBACK_PERIOD && currentIndex - i >= 0; i++)
        {
            // 從當前位置(currentIndex - i)計算BARSLAST(READYSELL)和BARSLAST(READYBUY)
            int lastSellDays = BarsLast(j => readySells[j], currentIndex - i, LOOKBACK_PERIOD);
            int lastBuyDays = BarsLast(j => readyBuys[j], currentIndex - i, LOOKBACK_PERIOD);
            
            // 如果在這個位置，BARSLAST(READYSELL) = MAX(BARSLAST(READYBUY),BARSLAST(READYSELL))
            if (lastSellDays == Math.Max(lastBuyDays, lastSellDays))
            {
                return i;
            }
        }
        return LOOKBACK_PERIOD;
    }

    /// <summary>
    /// 計算FARBUYDAYS - 找到最後一次READYBUY信號的位置，其中BARSLAST(READYBUY)等於MAX(BARSLAST(READYBUY),BARSLAST(READYSELL))
    /// </summary>
    private int CalculateFarBuyDays(int currentIndex)
    {
        for (int i = 0; i < LOOKBACK_PERIOD && currentIndex - i >= 0; i++)
        {
            // 從當前位置(currentIndex - i)計算BARSLAST(READYBUY)和BARSLAST(READYSELL)
            int lastBuyDays = BarsLast(j => readyBuys[j], currentIndex - i, LOOKBACK_PERIOD);
            int lastSellDays = BarsLast(j => readySells[j], currentIndex - i, LOOKBACK_PERIOD);
            
            // 如果在這個位置，BARSLAST(READYBUY) = MAX(BARSLAST(READYBUY),BARSLAST(READYSELL))
            if (lastBuyDays == Math.Max(lastBuyDays, lastSellDays))
            {
                return i;
            }
        }
        return LOOKBACK_PERIOD;
    }
    #endregion

    protected override void OnUpdate(UpdateArgs args)
    {
        int currentIndex = this.Count - 1;
        if (currentIndex < N) return;

        // 確保列表容量
        EnsureListCapacity(currentIndex);

        // 計算WR和通道相關數據
        CalculateWRAndChannel(currentIndex);

        // 計算做多相關數據
        CalculateLongConditions(currentIndex);

        // 計算做空相關數據
        CalculateShortConditions(currentIndex);

        // 處理當前K線的買賣訊號
        ProcessCurrentBarSignals(currentIndex);
    }

    private void EnsureListCapacity(int index)
    {
        while (wrValues.Count <= index)
        {
            // WR和通道相關
            wrValues.Add(0);
            upLines.Add(0);
            downLines.Add(0);
            isChannels.Add(false);
            inChannels.Add(false);
            u2Values.Add(null);
            d2Values.Add(null);
            udBoxValues.Add(0);
            readyBuys.Add(false);
            readySells.Add(false);

            // 做多相關
            longFrontHighs.Add(0);
            longDiffs.Add(0);
            longValids.Add(false);
            long382Prices.Add(0);
            long236Prices.Add(0);
            longAValues.Add(0);
            longBValues.Add(0);
            longCCValues.Add(0);
            longValid2s.Add(false);
            longValid3s.Add(false);
            buySignals.Add(false);

            // 做空相關
            shortFrontLows.Add(0);
            shortDiffs.Add(0);
            shortValids.Add(false);
            short382Prices.Add(0);
            short236Prices.Add(0);
            shortAValues.Add(0);
            shortBValues.Add(0);
            shortCCValues.Add(0);
            shortValid2s.Add(false);
            shortValid3s.Add(false);
            sellSignals.Add(false);
        }
    }

    private void CalculateWRAndChannel(int index)
    {
        // 計算WR
        double wr = CalculateWR(index);
        wrValues[index] = wr;

        // 計算通道
        double upLine = index >= MIN_LENGTH ? GetHighestWR(index - 1, MIN_LENGTH) : 0;
        double downLine = index >= MIN_LENGTH ? GetLowestWR(index - 1, MIN_LENGTH) : 0;

        upLines[index] = upLine;
        downLines[index] = downLine;

        // 判斷通道條件
        bool isChannel = Math.Abs(upLine - downLine) <= CH_WIDTH;
        bool inChannel = wr <= upLine && wr >= downLine;

        isChannels[index] = isChannel;
        inChannels[index] = inChannel;

        // 計算U2/D2/UDBOX
        double? u2 = inChannel && isChannel ? upLine : null;
        double? d2 = inChannel && isChannel ? downLine : null;
        double udBox = u2.HasValue && d2.HasValue ? Math.Abs(u2.Value - d2.Value) : 0;

        u2Values[index] = u2;
        d2Values[index] = d2;
        udBoxValues[index] = udBox;

        // 計算READYBUY/READYSELL
        bool readyBuy = inChannel && isChannel && u2.HasValue && 
                       u2.Value - udBox * FIBONACCI_EXTENSION < WR_BUY_THRESHOLD && 
                       wr < WR_BUY_THRESHOLD;
        bool readySell = inChannel && isChannel && d2.HasValue && 
                        d2.Value + udBox * FIBONACCI_EXTENSION > WR_SELL_THRESHOLD && 
                        wr > WR_SELL_THRESHOLD;

        readyBuys[index] = readyBuy;
        readySells[index] = readySell;
    }

    private void CalculateLongConditions(int index)
    {
        // 1. 計算前高和有效性
        double frontHigh = Math.Max(
            FindHigh(i => Open(i), index, LOOKBACK_PERIOD),
            FindHigh(i => Close(i), index, LOOKBACK_PERIOD)
        );
        double currentLow = Low(0);
        double diffLong = frontHigh - currentLow;
        bool isValidLong = diffLong > 0 && FindLow(i => Low(i), index, LOOKBACK_PERIOD) == currentLow;

        longFrontHighs[index] = frontHigh;
        longDiffs[index] = diffLong;
        longValids[index] = isValidLong;

        if (!isValidLong) return;

        // 2. 計算斐波那契價位
        double price382Long = currentLow + diffLong * 0.382;
        double price236Long = currentLow + diffLong * 0.236;

        long382Prices[index] = price382Long;
        long236Prices[index] = price236Long;

        // 3. 計算A/B/CC值
        CalculateLongABCValues(index, frontHigh, price382Long, price236Long);

        // 4. 計算VALID2/VALID3條件
        CalculateLongValidConditions(index);

        // 5. 計算買入信號 - 按照偽代碼完整實現
        // BUY_S := BUY_A + BUY_B + BUY_CC;
        int buyS = longAValues[index] + longBValues[index] + longCCValues[index];

        // BUY_CP := IF( BUY_S > 0 , BUY_A / BUY_S, 0 );
        // BUY_NCP := IF( BUY_S > 0 , BUY_B / BUY_S, 0 );
        double buyCP = buyS > 0 ? (double)longAValues[index] / buyS : 0;
        double buyNCP = buyS > 0 ? (double)longBValues[index] / buyS : 0;

        // 最終買入條件：BUY_ISVALID AND BUY_ISVALID2 AND BUY_CP>0.8*(BUY_CP+BUY_NCP)
        bool finalBuyCondition = longValids[index] &&
                                 longValid2s[index] &&
                                 buyCP > 0.8 * (buyCP + buyNCP);

        buySignals[index] = finalBuyCondition;
    }

    private void CalculateShortConditions(int index)
    {
        // 1. 計算前低和有效性
        double frontLow = Math.Min(
            FindLow(i => Open(i), index, LOOKBACK_PERIOD),
            FindLow(i => Close(i), index, LOOKBACK_PERIOD)
        );
        double currentHigh = High(0);
        double diffShort = currentHigh - frontLow;
        bool isValidShort = diffShort > 0 && FindHigh(i => High(i), index, LOOKBACK_PERIOD) == currentHigh;

        shortFrontLows[index] = frontLow;
        shortDiffs[index] = diffShort;
        shortValids[index] = isValidShort;

        if (!isValidShort) return;

        // 2. 計算斐波那契價位
        double price382Short = currentHigh - diffShort * 0.382;
        double price236Short = currentHigh - diffShort * 0.236;

        short382Prices[index] = price382Short;
        short236Prices[index] = price236Short;

        // 3. 計算A/B/CC值
        CalculateShortABCValues(index, frontLow, price382Short, price236Short);

        // 4. 計算VALID2/VALID3條件
        CalculateShortValidConditions(index);

        // 5. 計算賣出信號 - 按照偽代碼完整實現
        // SELL_S := SELL_A + SELL_B + SELL_CC;
        int sellS = shortAValues[index] + shortBValues[index] + shortCCValues[index];

        // SELL_CP := IF( SELL_S > 0, SELL_A / SELL_S, 0 );
        // SELL_NCP := IF( SELL_S > 0, SELL_B / SELL_S, 0 );
        double sellCP = sellS > 0 ? (double)shortAValues[index] / sellS : 0;
        double sellNCP = sellS > 0 ? (double)shortBValues[index] / sellS : 0;

        // 最終賣出條件：SELL_ISVALID AND SELL_ISVALID2 AND SELL_CP>0.8*(SELL_CP+SELL_NCP)
        bool finalSellCondition = shortValids[index] &&
                                 shortValid2s[index] &&
                                 sellCP > 0.8 * (sellCP + sellNCP);

        sellSignals[index] = finalSellCondition;
    }

    private void ProcessCurrentBarSignals(int index)
    {
        // 清除當前K線上的標記
        LinesSeries[2].RemoveMarker(0);
        LinesSeries[3].RemoveMarker(0);

        // 處理買入信號
        if (buySignals[index])
        {
            SetValue(Low(0), 2);
            LinesSeries[2].SetMarker(0, new IndicatorLineMarker(
                this.DefaultUpColor,
                bottomIcon: IndicatorLineMarkerIconType.UpArrow
            ));
        }
        else
        {
            SetValue(0, 2);
        }

        // 處理賣出信號
        if (sellSignals[index])
        {
            SetValue(High(0), 3);
            LinesSeries[3].SetMarker(0, new IndicatorLineMarker(
                this.DefaultDownColor,
                upperIcon: IndicatorLineMarkerIconType.DownArrow
            ));
        }
        else
        {
            SetValue(0, 3);
        }
    }



    private void CalculateLongABCValues(int index, double frontHigh, double price382, double price236)
    {
        // 計算A值 - 找最近一次Close從下向上突破price382的K線
        int entryBarFor382 = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (Close(i) > price382 && Close(i + 1) < price382)
            {
                int countBelow = 0;
                for (int j = 1; j <= 5 && index - i - j >= 0; j++)
                {
                    if (Close(i + j) < price382)
                        countBelow++;
                }
                
                if (countBelow >= MIN_COUNT_THRESHOLD)
                {
                    entryBarFor382 = i;
                    break;
                }
            }
        }
        
        // 統計A區間K線數
        int aValue = 0;
        for (int i = 0; i <= entryBarFor382 && index - i >= 0; i++)
        {
            double closePrice = Close(i);
            if (closePrice >= price382 && closePrice <= frontHigh)
                aValue++;
        }
        
        // 計算B值 - 找最近一次Close從下向上突破price236的K線
        int entryBarFor236 = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (Close(i) > price236 && Close(i + 1) < price236)
            {
                int countBelow = 0;
                for (int j = 1; j <= 5 && index - i - j >= 0; j++)
                {
                    if (Close(i + j) < price236)
                        countBelow++;
                }
                
                if (countBelow >= MIN_COUNT_THRESHOLD)
                {
                    entryBarFor236 = i;
                    break;
                }
            }
        }
        
        // 統計B區間K線數 - 根據偽代碼：BUY_B := COUNT( L >= BUY_PRICE236 AND L <= BUY_PRICE382, BUY_ENTRYBARFOR236 );
        int bValue = 0;
        for (int i = 0; i <= entryBarFor236 && index - i >= 0; i++)
        {
            double lowPrice = Low(i);
            if (lowPrice >= price236 && lowPrice <= price382)
                bValue++;
        }
        
        // 計算CC值 - 找到從當前向前第一根收盤價低於price236的K線距離
        int ccValue = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (price236 > Close(i) && price236 <= Close(i + 1))
            {
                ccValue = i;
                break;
            }
        }
        
        // 保存計算結果
        longAValues[index] = aValue;
        longBValues[index] = bValue;
        longCCValues[index] = ccValue;
    }

    private void CalculateLongValidConditions(int index)
    {
        // 計算VALID2條件
        bool isValid2 = longBValues[index] < longAValues[index] && 
                       longCCValues[index] < LOOKBACK_PERIOD;
        
        // 計算FARSELLDAYS
        int farSellDays = CalculateFarSellDays(index);
        
        // 計算VALID3條件
        bool isValid3 = Exist(i => readyBuys[i], index, Math.Max(farSellDays, longAValues[index])) &&
                       Exist(i => readySells[i], index, Math.Max(farSellDays, longAValues[index]));
        
        // 保存結果
        longValid2s[index] = isValid2;
        longValid3s[index] = isValid3;
    }

    private void CalculateShortABCValues(int index, double frontLow, double price382, double price236)
    {
        // 計算A值 - 找最近一次Close從上向下突破price382的K線
        int entryBarFor382 = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (Close(i) < price382 && Close(i + 1) > price382)
            {
                int countAbove = 0;
                for (int j = 1; j <= 5 && index - i - j >= 0; j++)
                {
                    if (Close(i + j) > price382)
                        countAbove++;
                }
                
                if (countAbove >= MIN_COUNT_THRESHOLD)
                {
                    entryBarFor382 = i;
                    break;
                }
            }
        }
        
        // 統計A區間K線數
        int aValue = 0;
        for (int i = 0; i <= entryBarFor382 && index - i >= 0; i++)
        {
            double closePrice = Close(i);
            if (closePrice <= price382 && closePrice >= frontLow)
                aValue++;
        }
        
        // 計算B值 - 找最近一次Close從上向下突破price236的K線
        int entryBarFor236 = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (Close(i) < price236 && Close(i + 1) > price236)
            {
                int countAbove = 0;
                for (int j = 1; j <= 5 && index - i - j >= 0; j++)
                {
                    if (Close(i + j) > price236)
                        countAbove++;
                }
                
                if (countAbove >= MIN_COUNT_THRESHOLD)
                {
                    entryBarFor236 = i;
                    break;
                }
            }
        }
        
        // 統計B區間K線數
        int bValue = 0;
        for (int i = 0; i <= entryBarFor236 && index - i >= 0; i++)
        {
            double closePrice = Close(i);
            if (closePrice <= price236 && closePrice >= price382)
                bValue++;
        }
        
        // 計算CC值 - 找到從當前向前第一根收盤價高於price236的K線距離
        int ccValue = LOOKBACK_PERIOD;
        for (int i = 0; i < LOOKBACK_PERIOD && index - i >= 1; i++)
        {
            if (Close(i) > price236 && Close(i + 1) <= price236)
            {
                ccValue = i;
                break;
            }
        }
        
        // 保存計算結果
        shortAValues[index] = aValue;
        shortBValues[index] = bValue;
        shortCCValues[index] = ccValue;
    }

    private void CalculateShortValidConditions(int index)
    {
        // 計算VALID2條件
        bool isValid2 = shortBValues[index] < shortAValues[index] && 
                       shortCCValues[index] < LOOKBACK_PERIOD;
        
        // 計算FARBUYDAYS
        int farBuyDays = CalculateFarBuyDays(index);
        
        // 計算VALID3條件
        bool isValid3 = Exist(i => readyBuys[i], index, Math.Max(farBuyDays, shortAValues[index])) &&
                       Exist(i => readySells[i], index, Math.Max(farBuyDays, shortAValues[index]));
        
        // 保存結果
        shortValid2s[index] = isValid2;
        shortValid3s[index] = isValid3;
    }

    // ... 其他輔助方法 ...
}