// Copyright QUANTOWER LLC. © 2017-2020. All rights reserved.

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Xml.Linq;
using TradingPlatform.BusinessLayer;
using TradingPlatform.BusinessLayer.Serialization;

namespace InsidebarboxesQT
{
    /// <summary>
    /// An example of blank indicator. Add your code, compile it and use on the charts in the assigned trading terminal.
    /// Information about API you can find here: http://api.quantower.com
    /// </summary>
	public class InsidebarboxesQT : Indicator
    {
        /// <summary>
        /// Indicator's constructor. Contains general information: name, description, LineSeries etc. 
        /// </summary>
        private int barIndex = 1;
        private List<Box> listboxes = null;


        public InsidebarboxesQT()
            : base()
        {
            // Defines indicator's name and description.
            Name = "negbookboxes2";
            Description = "My indicator's annotation";

            SeparateWindow = false;

        }

        /// <summary>
        /// This function will be called after creating an indicator as well as after its input params reset or chart (symbol or timeframe) updates.
        /// </summary>
        protected override void OnInit()
        {
            // Add your initialization code here
            listboxes = new List<Box>();
        }

        /// <summary>
        /// Calculation entry point. This function is called when a price data updates. 
        /// Will be runing under the HistoricalBar mode during history loading. 
        /// Under NewTick during realtime. 
        /// Under NewBar if start of the new bar is required.
        /// </summary>
        /// <param name="args">Provides data of updating reason and incoming price.</param>
        protected override void OnUpdate(UpdateArgs args)
        {
            if (isInsideBar(1, barIndex + 1))
            {
                barIndex++;
            }

            if (isInsideBar(1, barIndex) && !isInsideBar(0, barIndex))
            {

                DateTime CurrentTime = Time(1);
                DateTime PreviusTime = Time(barIndex + 1);

                Double PreviusHigh = Math.Max(Open(barIndex + 1), Close(barIndex + 1));
                Double PreviusLow = Math.Min(Open(barIndex + 1), Close(barIndex + 1));

                //Log("Box Found", LoggingLevel.System);


                if (barIndex > 1)
                {
                    Box box = new Box();
                    box.CurrentTime = CurrentTime;
                    box.PreviusTime = PreviusTime;
                    box.PreviusHigh = PreviusHigh;
                    box.PreviusLow = PreviusLow;

                    listboxes.Add(box);

                    barIndex = 1;
                }



            }



        }

        private void Log(string v, LoggingLevel information)
        {
            Core.Instance.Loggers.Log(v, information);
        }

        public override void OnPaintChart(PaintChartEventArgs args)
        {
            Graphics gr = args.Graphics;





            if (listboxes.Count > 0)
            {

                Brush myBrush = new System.Drawing.SolidBrush(BoxColor);
                Pen myPen = new Pen(BoxColor, 1);
                //Log("OnPaintChart", LoggingLevel.System);

                foreach (var box in listboxes)
                {


                    var Y1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(box.PreviusHigh);
                    var Y2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(box.PreviusLow);
                    var iX1 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(box.PreviusTime);
                    var iX2 = (int)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(box.CurrentTime);

                    Rectangle rectangle = new Rectangle(iX1, Math.Min(Y1, Y2), Math.Abs(iX2 - iX1), Math.Abs(Y1 - Y2));
                    //gr.FillRectangle(myBrush, rectangle);
                    gr.DrawRectangle(myPen, rectangle);


                    //Log("Box draw desde" + iX1.ToString(), LoggingLevel.System);
                    //Log("Box draw hasta" + iX2.ToString(), LoggingLevel.System);

                    //box = null;

                }
            }

        }

        private Boolean isInsideBar(int currentbarp, int previusbarp)
        {
            int currentbar = currentbarp + 1;
            int previusbar = previusbarp + 1;
            double hp = Math.Max(Open(previusbar), Close(previusbar));
            double lp = Math.Min(Open(previusbar), Close(previusbar));
            double boxrange = hp - lp;
            double fibo2_hp = lp + boxrange * 1.618;
            double fibo2_lp = hp - boxrange * 1.618;
            Boolean isIB = (Close(currentbar) <= fibo2_hp && Close(currentbar) >= fibo2_lp) && (Open(currentbar) <= fibo2_hp && Open(currentbar) >= fibo2_lp);
            return isIB;
        }



        [InputParameter("BoxColor", 1)]
        public Color BoxColor = Color.LightBlue;


    }
}
