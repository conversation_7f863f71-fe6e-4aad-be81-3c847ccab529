<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Platforms>AnyCPU</Platforms>
    <AlgoType>Strategy</AlgoType>
    <AssemblyName>InsideBarStrategy</AssemblyName>
    <RootNamespace>InsideBarStrategy</RootNamespace>
    <StartAction>Program</StartAction>
    <StartProgram>D:\Quantower\TradingPlatform\v1.140.14\Console.StarterNew.exe</StartProgram>
    <StartArguments>--address 127.0.0.1 --port 54458</StartArguments>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.140.14\..\..\Settings\Scripts\Strategies\InsideBarStrategy</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.140.14\..\..\Settings\Scripts\Strategies\InsideBarStrategy</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="TradingPlatform.BusinessLayer">
      <HintPath>D:\Quantower\TradingPlatform\v1.140.14\bin\TradingPlatform.BusinessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
</Project>