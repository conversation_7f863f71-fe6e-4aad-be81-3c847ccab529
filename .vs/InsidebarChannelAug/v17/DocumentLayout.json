{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannelAug.csproj|c:\\users\\<USER>\\source\\repos\\insidebarchannelaug\\insidebarchannelaug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannelAug.csproj|solutionrelative:insidebarchannelaug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannelAug.csproj|c:\\users\\<USER>\\source\\repos\\insidebarchannelaug\\insidebarfibostrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannelAug.csproj|solutionrelative:insidebarfibostrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "InsidebarChannelAug.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsidebarChannelAug.cs", "RelativeDocumentMoniker": "InsidebarChannelAug.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsidebarChannelAug.cs", "RelativeToolTip": "InsidebarChannelAug.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T03:41:17.141Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "InsideBarFiboStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsideBarFiboStrategy.cs", "RelativeDocumentMoniker": "InsideBarFiboStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsideBarFiboStrategy.cs", "RelativeToolTip": "InsideBarFiboStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T07:40:08.234Z"}]}]}]}