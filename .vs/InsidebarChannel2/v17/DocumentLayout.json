{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannel2\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannel2.csproj|c:\\users\\<USER>\\source\\repos\\insidebarchannel2\\insidebarchannel2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannel2.csproj|solutionrelative:insidebarchannel2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannel2.csproj|c:\\users\\<USER>\\source\\repos\\insidebarchannel2\\insidebarchannel2.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{F9013F7E-77EF-4DF4-8A10-AD0D3D5090F0}|InsidebarChannel2.csproj|solutionrelative:insidebarchannel2.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "InsidebarChannel2", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannel2\\InsidebarChannel2.csproj", "RelativeDocumentMoniker": "InsidebarChannel2.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannel2\\InsidebarChannel2.csproj", "RelativeToolTip": "InsidebarChannel2.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-09-04T10:01:19.08Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "InsidebarChannel2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannel2\\InsidebarChannel2.cs", "RelativeDocumentMoniker": "InsidebarChannel2.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannel2\\InsidebarChannel2.cs", "RelativeToolTip": "InsidebarChannel2.cs", "ViewState": "AgIAAKwBAAAAAAAAAAAAAMYBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-04T09:59:55.726Z", "EditorCaption": ""}]}]}]}