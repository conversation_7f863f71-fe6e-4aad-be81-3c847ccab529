{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EFE08E00-4C59-4D0A-BB09-E89AD4652BE9}|InsideBarFiboStrategy.csproj|c:\\users\\<USER>\\source\\repos\\insidebarchannelaug\\insidebarfibostrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EFE08E00-4C59-4D0A-BB09-E89AD4652BE9}|InsideBarFiboStrategy.csproj|solutionrelative:insidebarfibostrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "InsideBarFiboStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsideBarFiboStrategy.cs", "RelativeDocumentMoniker": "InsideBarFiboStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\InsidebarChannelAug\\InsideBarFiboStrategy.cs", "RelativeToolTip": "InsideBarFiboStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T09:33:51.282Z", "EditorCaption": ""}]}]}]}