// Copyright QUANTOWER LLC. © 2017-2022. All rights reserved.

using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using TradingPlatform.BusinessLayer;

namespace Strategy1k
{
    public sealed class InsideBarFiboStrategy : Strategy, ICurrentAccount, ICurrentSymbol
    {
        [InputParameter("Symbol", 0)]
        public Symbol CurrentSymbol { get; set; }

        [InputParameter("Account", 1)]
        public Account CurrentAccount { get; set; }

        [InputParameter("Quantity", 2, 0.1, ********, 0.1, 2)]
        public double Quantity { get; set; } = 1.0;

        [InputParameter("Period", 3)]
        public Period Period { get; set; }

        [InputParameter("Start point", 4)]
        public DateTime StartPoint { get; set; }

        [InputParameter("ATR Stop Loss Multiplier", 5, 0.5, 5.0, 0.1, 1)]
        public double ATRMultiplier { get; set; } = 1.0;

        [InputParameter("Use Trading Time Limit", 6)]
        public bool UseTradingTimeLimit { get; set; } = false;

        [InputParameter("FIBO Lookback Bars", 7, 1, 50, 1, 0)]
        public int FiboLookbackBars { get; set; } = 20;

        [InputParameter("Use Daily FIBO Cache", 8)]
        public bool UseDailyFiboCache { get; set; } = true;

        [InputParameter("Max Daily FIBO Levels", 9, 1, 10, 1, 0)]
        public int MaxDailyFiboLevels { get; set; } = 3;

        [InputParameter("Trading Direction", 10, variants: new object[]{
            "Both (Long & Short)", 1,
            "Long Only", 2,
            "Short Only", 3
        })]
        public int TradingDirection { get; set; } = 2;

        [InputParameter("Use Previous Complete Bar", 11)]
        public bool UsePreviousCompleteBar { get; set; } = false;

        [InputParameter("TP Target Type", 12, variants: new object[]{
            "Default Mode", 1,
            "Long Mode", 2,
            "Opposite FIBO Level", 3
        })]
        public int TPTargetType { get; set; } = 1;

        [InputParameter("Enable Trend Line Check", 13)]
        public bool EnableTrendLineCheck { get; set; } = true;

        [InputParameter("Trend Line Range Start", 14, 100, 1000, 50, 0)]
        public int TrendLineRangeStart { get; set; } = 600;

        [InputParameter("Trend Line Range End", 15, 10, 100, 5, 0)]
        public int TrendLineRangeEnd { get; set; } = 20;

        [InputParameter("Max Daily Trades", 16, 1, 50, 1, 0)]
        public int MaxDailyTrades { get; set; } = 10;

        // Trading time constraints (參考 MarkBuySellLine.cs)
        private const int TRADING_START_TIME = 1300;  // 下午1點 (13:00)
        private const int TRADING_END_TIME = 100;     // 凌晨1點 (01:00)

        // CheckTime constants (參考 InsidebarChannelAug.cs)
        private const int TIME_CHECK1_START = 1200;  // 12:00
        private const int TIME_CHECK1_END = 1300;    // 13:00
        private const int TIME_CHECK2_START = 1500;  // 15:00
        private const int TIME_CHECK2_END = 1600;    // 16:00
        private const int TIME_CHECK3_START = 2000;  // 20:00
        private const int TIME_CHECK3_END = 2300;    // 23:00

        public override string[] MonitoringConnectionsIds => new string[] { this.CurrentSymbol?.ConnectionId, this.CurrentAccount?.ConnectionId };

        private Indicator insideBarIndicator;
        private Indicator atrIndicator;
        private HistoricalData historicalData;

        private string orderTypeIdMarket;
        private bool waitingForOrder = false;

        // Wait variables for order management (參考 SimpleMACross.cs)
        private bool waitOpenPosition;
        private bool waitClosePositions;

        // FIBO cache variables (每個CheckTime更新一次)
        private List<FiboLevel> cachedFiboLevels = new List<FiboLevel>();
        private DateTime lastFiboCacheTime = DateTime.MinValue;

        // 買賣信號記錄變量（基於 MarkBuySell）
        private List<BuySellSignalRecord> buySellSignals = new List<BuySellSignalRecord>();
        private DateTime lastSignalCheckDate = DateTime.MinValue;

        // 趨勢線相關變量
        private List<TrendLineData> cachedTrendLines = new List<TrendLineData>();

        // 當日趨勢線記錄（兩次記錄機制）
        private List<TrendLineData> firstTrendLines = new List<TrendLineData>();  // 第一次記錄的趨勢線
        private List<TrendLineData> secondTrendLines = new List<TrendLineData>(); // 第二次記錄的趨勢線
        private DateTime lastSignalDate = DateTime.MinValue;
        private int signalCount = 0; // 當日信號計數
        private int recordedRangeStart = -1; // 記錄的判定範圍起始K

        // 每日交易次數控制
        private int dailyTradeCount = 0; // 當日交易次數
        private DateTime lastTradeDate = DateTime.MinValue;

        // VAH/VAL相關變量（參考Selected_Range_Lines_Indicator.cs的FiboType2）
        private double vah = 0; // Value Area High
        private double val = 0; // Value Area Low
        private double poc = 0; // Point of Control

        // waitingForOrder超時保護
        private DateTime waitingForOrderStartTime = DateTime.MinValue;
        private const int WAITING_FOR_ORDER_TIMEOUT_SECONDS = 30; // 30秒超時

        // 參考文件中的常數
        private const int PIVOT_LOOKBACK = 3;
        private const int MIN_TOUCH_POINTS = 3;
        private const int MIN_TOUCH_POINTS_RANGE = 4;
        private const int MIN_BODY_CROSS_TOLERANCE = 1;
        private const double MAX_ABS_ANGLE = 61.8;
        private const double MIN_ABS_ANGLE = 21.0;
        private const int MAX_RANGE_LINES = 200;
        private const int REGION_SIZE = 15;
        private const double MIN_DISTANCE = 8;
        private const int MIN_TIME_SPAN = 5;
        private const int MAX_TIME_SPAN = 60;
        private const double FIBONACCI_RATIO = 0.618033988749895;
        private const double FIBONACCI_RATIO_2 = 0.381966011250105;
        private const double GOLDEN_RATIO = 1.618033988749895;

        // Metrics variables (參考 InsideBarStrategy.cs)
        private int longPositionsCount;
        private int shortPositionsCount;
        private double totalNetPl;
        private double totalGrossPl;
        private double totalFee;
        private int totalTrades;

        public InsideBarFiboStrategy()
            : base()
        {
            this.Name = "InsideBar Fibonacci Strategy";
            this.Description = "FIBO strategy: Touch Fibo levels -> TP to Box OC or opposite FIBO, SL = 1ATR";
            this.Period = Period.MIN5;
            this.StartPoint = Core.TimeUtils.DateTimeUtcNow.AddDays(-30);

            // Initialize metrics variables (參考 InsideBarStrategy.cs)
            this.totalNetPl = 0.0;
            this.totalGrossPl = 0.0;
            this.totalFee = 0.0;
            this.totalTrades = 0;
            this.longPositionsCount = 0;
            this.shortPositionsCount = 0;
        }

        protected override void OnRun()
        {
            if (!ValidateInputs()) return;

            this.historicalData = this.CurrentSymbol.GetHistory(this.Period, this.CurrentSymbol.HistoryType, this.StartPoint);

            SetupIndicators();

            this.orderTypeIdMarket = Core.OrderTypes.FirstOrDefault(x => x.ConnectionId == this.CurrentSymbol.ConnectionId && x.Behavior == OrderTypeBehavior.Market)?.Id;

            if (string.IsNullOrEmpty(this.orderTypeIdMarket))
            {
                this.Log("Connection does not support market orders", StrategyLoggingLevel.Error);
                return;
            }

            this.historicalData.NewHistoryItem += this.OnNewHistoryItem;
            Core.PositionAdded += this.OnPositionAdded;
            Core.PositionRemoved += this.OnPositionRemoved;
            Core.TradeAdded += this.OnTradeAdded;

            // 策略啟動時重置所有狀態變量
            InitializeStrategyState();

            this.Log("Strategy started successfully", StrategyLoggingLevel.Trading);
        }

        protected override void OnStop()
        {
            if (this.historicalData != null)
            {
                this.historicalData.NewHistoryItem -= this.OnNewHistoryItem;
                this.historicalData.RemoveIndicator(this.insideBarIndicator);
                this.historicalData.RemoveIndicator(this.atrIndicator);
                this.historicalData.Dispose();
            }

            Core.PositionAdded -= this.OnPositionAdded;
            Core.PositionRemoved -= this.OnPositionRemoved;
            Core.TradeAdded -= this.OnTradeAdded;

            base.OnStop();
        }

        protected override void OnInitializeMetrics(Meter meter)
        {
            base.OnInitializeMetrics(meter);

            meter.CreateObservableCounter("total-long-positions", () => this.longPositionsCount, description: "Total long positions");
            meter.CreateObservableCounter("total-short-positions", () => this.shortPositionsCount, description: "Total short positions");
            meter.CreateObservableCounter("total-trades", () => this.totalTrades, description: "Total number of trades");
            meter.CreateObservableCounter("total-pl-net", () => this.totalNetPl, description: "Total Net profit/loss");
            meter.CreateObservableCounter("total-pl-gross", () => this.totalGrossPl, description: "Total Gross profit/loss");
            meter.CreateObservableCounter("total-fee", () => this.totalFee, description: "Total fee");
        }

        private bool ValidateInputs()
        {
            if (this.CurrentSymbol?.State == BusinessObjectState.Fake)
                this.CurrentSymbol = Core.Instance.GetSymbol(this.CurrentSymbol.CreateInfo());

            if (this.CurrentSymbol == null)
            {
                this.Log("Symbol not specified", StrategyLoggingLevel.Error);
                return false;
            }

            if (this.CurrentAccount?.State == BusinessObjectState.Fake)
                this.CurrentAccount = Core.Instance.GetAccount(this.CurrentAccount.CreateInfo());

            if (this.CurrentAccount == null)
            {
                this.Log("Account not specified", StrategyLoggingLevel.Error);
                return false;
            }

            if (this.CurrentSymbol.ConnectionId != this.CurrentAccount.ConnectionId)
            {
                this.Log("Symbol and Account from different connections", StrategyLoggingLevel.Error);
                return false;
            }

            return true;
        }

        private void SetupIndicators()
        {
            var indicatorInfo = Core.Instance.Indicators.All.FirstOrDefault(info => info.Name == "Inside Bar Aug Channel indicator");
            if (indicatorInfo != null)
            {
                this.insideBarIndicator = Core.Instance.Indicators.CreateIndicator(indicatorInfo);
                this.historicalData.AddIndicator(this.insideBarIndicator);
            }
            else
            {
                this.Log("InsideBar Channel indicator not found", StrategyLoggingLevel.Error);
                return;
            }

            this.atrIndicator = Core.Instance.Indicators.BuiltIn.ATR(14, MaMode.SMMA, IndicatorCalculationType.AllAvailableData);
            this.historicalData.AddIndicator(this.atrIndicator);
        }

        private void OnNewHistoryItem(object sender, HistoryEventArgs e) => this.ProcessSignals();

        private void OnPositionAdded(Position position)
        {
            if (position.Symbol == this.CurrentSymbol && position.Account == this.CurrentAccount)
            {
                this.waitingForOrder = false;
                this.waitOpenPosition = false; // 參考 SimpleMACross.cs
                this.waitingForOrderStartTime = DateTime.MinValue; // 重置等待時間戳

                // 計算等待時間
                if (this.waitingForOrderStartTime != DateTime.MinValue)
                {
                    DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                        this.historicalData[0].TimeLeft,
                        Core.Instance.TimeUtils.SelectedTimeZone);
                    double waitTime = (currentTime - this.waitingForOrderStartTime).TotalSeconds;
                    this.Log($"Position opened: {position.Side} {position.Quantity}, 等待時間: {waitTime:F1}秒", StrategyLoggingLevel.Trading);
                }
                else
                {
                    this.Log($"Position opened: {position.Side} {position.Quantity}", StrategyLoggingLevel.Trading);
                }

                // Update position counts for metrics (參考 InsideBarStrategy.cs)
                this.UpdatePositionCounts();
            }
        }

        private void OnPositionRemoved(Position position)
        {
            if (position.Symbol == this.CurrentSymbol && position.Account == this.CurrentAccount)
            {
                this.Log($"Position closed: {position.Side} {position.Quantity}", StrategyLoggingLevel.Trading);

                // Update position counts for metrics (參考 InsideBarStrategy.cs)
                this.UpdatePositionCounts();

                // Reset wait flags when all positions are closed (參考 SimpleMACross.cs)
                var remainingPositions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
                if (!remainingPositions.Any())
                {
                    this.waitClosePositions = false;
                }
            }
        }

        private void OnTradeAdded(Trade trade)
        {
            if (trade.Account != this.CurrentAccount || trade.Symbol != this.CurrentSymbol) return;

            this.totalTrades++;

            if (trade.NetPnl != null)
                this.totalNetPl += trade.NetPnl.Value;

            if (trade.GrossPnl != null)
                this.totalGrossPl += trade.GrossPnl.Value;

            if (trade.Fee != null)
                this.totalFee += trade.Fee.Value;

            this.Log($"Trade completed: {trade.Side} {trade.Quantity}, NetPnL: {trade.NetPnl:F2}, GrossPnL: {trade.GrossPnl:F2}", StrategyLoggingLevel.Trading);
        }

        // 價格格式化方法 (參考 Selected_Range_Lines_Indicator.cs)
        public double FormatSymbolPrice(double price, bool roundUp = true)
        {
            Symbol symbol = this.CurrentSymbol;
            if (symbol == null)
                throw new ArgumentNullException(nameof(symbol));

            double tickSize = symbol.TickSize;

            // 計算需要進位的小數位數
            int decimalPlaces = BitConverter.GetBytes(decimal.GetBits((decimal)tickSize)[3])[2];

            // 計算價格除以 tickSize 的商和餘數
            double quotient = Math.Floor(price / tickSize);
            double remainder = price % tickSize;

            double roundedPrice;
            if (roundUp)
            {
                // 向上取整：如果有餘數，就進位到下一個 tick
                roundedPrice = remainder > 0
                    ? (quotient + 1) * tickSize
                    : quotient * tickSize;
            }
            else
            {
                // 向下取整：直接捨去餘數
                roundedPrice = quotient * tickSize;
            }

            // 格式化價格
            if (double.TryParse(symbol.FormatPrice(roundedPrice), out double formattedPrice))
            {
                return formattedPrice;
            }

            return roundedPrice;
        }

        // 為了方便使用，可以添加兩個輔助方法
        public double FormatSymbolPriceUp(double price)
        {
            return FormatSymbolPrice(price, true);
        }

        public double FormatSymbolPriceDown(double price)
        {
            return FormatSymbolPrice(price, false);
        }

        private void UpdatePositionCounts()
        {
            var positions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            this.longPositionsCount = positions.Count(x => x.Side == Side.Buy);
            this.shortPositionsCount = positions.Count(x => x.Side == Side.Sell);
        }

        private void ProcessSignals()
        {
            // 首先檢查並重置狀態變量
            ResetStaleWaitFlags();

            if (this.waitingForOrder)
            {
                this.Log("ProcessSignals skipped: waitingForOrder", StrategyLoggingLevel.Trading);
                return;
            }
            if (this.waitOpenPosition)
            {
                this.Log("ProcessSignals skipped: waitOpenPosition", StrategyLoggingLevel.Trading);
                return;
            }
            if (this.waitClosePositions)
            {
                this.Log("ProcessSignals skipped: waitClosePositions", StrategyLoggingLevel.Trading);
                return;
            }
            if (this.historicalData.Count < 2)
            {
                this.Log("ProcessSignals skipped: insufficient historical data", StrategyLoggingLevel.Trading);
                return;
            }

            var existingPositions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();

            // Check for daily close at 3 AM
            if (this.ShouldCloseAllPositions())
            {
                this.CloseAllPositions(existingPositions);
                return;
            }

            // 檢查現有持倉，避免同向重複下單
            bool hasLongPosition = existingPositions.Any(p => p.Side == Side.Buy);
            bool hasShortPosition = existingPositions.Any(p => p.Side == Side.Sell);

            // Check if current time is within trading hours (參考 MarkBuySellLine.cs)
            if (this.UseTradingTimeLimit && !IsWithinTradingHours())
            {
                this.Log("ProcessSignals skipped: outside trading hours", StrategyLoggingLevel.Trading);
                return;
            }

            // Update FIBO cache if needed (每個CheckTime更新一次)
            if (this.UseDailyFiboCache)
            {
                this.UpdateFiboCacheAtCheckTime();
            }

            // 選擇使用當前K線還是前一根完整K線
            int barIndex = this.UsePreviousCompleteBar ? 1 : 0;
            if (barIndex >= this.historicalData.Count)
            {
                this.Log("Insufficient historical data for selected bar index", StrategyLoggingLevel.Trading);
                return;
            }

            var currentBar = (HistoryItemBar)this.historicalData[barIndex];

            // 添加調試信息：檢查K線是否完整
            string barType = this.UsePreviousCompleteBar ? "previous complete" : "current";
            this.Log($"Using {barType} bar at index {barIndex}, Time: {currentBar.TimeLeft}, Current time: {Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(DateTime.UtcNow, Core.Instance.TimeUtils.SelectedTimeZone):yyyy-MM-dd HH:mm:ss}", StrategyLoggingLevel.Trading);

            // Get Fibonacci levels (either from cache or search)
            var fiboData = this.UseDailyFiboCache ? GetCachedFiboLevels() : GetValidFiboLevels();
            int actualLookback = fiboData.ActualLookback;

            // 計算VAH和VAL，並篩選FIBO位置（參考Selected_Range_Lines_Indicator.cs的FiboType2邏輯）
            var filteredFiboData = FilterFiboLevelsByVAHVAL(fiboData, actualLookback);

            double bottom1382 = filteredFiboData.Bottom1382;
            double bottom1618 = filteredFiboData.Bottom1618;
            double top1382 = filteredFiboData.Top1382;
            double top1618 = filteredFiboData.Top1618;

            // Debug: Log Fibonacci levels
            this.Log($"Fibo Levels ({actualLookback} bars ago) - Bottom1382: {bottom1382:F5}, Bottom1618: {bottom1618:F5}, Top1382: {top1382:F5}, Top1618: {top1618:F5}", StrategyLoggingLevel.Trading);

            // Check for valid levels (not NaN)
            if (double.IsNaN(bottom1382) && double.IsNaN(bottom1618) && double.IsNaN(top1382) && double.IsNaN(top1618))
            {
                this.Log("All Fibonacci levels are NaN, skipping signal check", StrategyLoggingLevel.Trading);
                return;
            }

            double currentPrice = currentBar.Close;
            double atrValue = this.atrIndicator.GetValue(0);

            // 獲取當前K線的OHLC數據
            double currentOpen = currentBar.Open;
            double currentHigh = currentBar.High;
            double currentLow = currentBar.Low;
            double currentClose = currentBar.Close;

            // 計算實體範圍
            double bodyMin = Math.Min(currentOpen, currentClose);  // min(O,C)
            double bodyMax = Math.Max(currentOpen, currentClose);  // max(O,C)

            // Debug: Log current bar data
            this.Log($"Current Bar - O:{currentOpen:F5}, H:{currentHigh:F5}, L:{currentLow:F5}, C:{currentClose:F5}, BodyRange:[{bodyMin:F5}-{bodyMax:F5}]", StrategyLoggingLevel.Trading);

            // 檢查是否為一字板或數據異常
            if (currentOpen == currentHigh && currentHigh == currentLow && currentLow == currentClose)
            {
                this.Log("WARNING: Current bar is a flat line (O=H=L=C) - no price movement detected", StrategyLoggingLevel.Trading);
                this.Log($"Bar time: {currentBar.TimeLeft}, Period: {this.Period}", StrategyLoggingLevel.Trading);

                // 檢查前一根K線作為對比
                if (this.historicalData.Count > 1)
                {
                    var prevBar = (HistoryItemBar)this.historicalData[1];
                    this.Log($"Previous Bar - O:{prevBar.Open:F5}, H:{prevBar.High:F5}, L:{prevBar.Low:F5}, C:{prevBar.Close:F5}", StrategyLoggingLevel.Trading);
                }

                // 一字板情況下，如果當前使用的是當前K線(index 0)，嘗試切換到前一根K線
                if (!this.UsePreviousCompleteBar && this.historicalData.Count > 1)
                {
                    this.Log("Flat line detected with current bar - trying previous bar for signal detection", StrategyLoggingLevel.Trading);
                    barIndex = 1; // 強制使用前一根K線
                    currentBar = (HistoryItemBar)this.historicalData[barIndex];

                    // 重新計算K線數據
                    currentOpen = currentBar.Open;
                    currentHigh = currentBar.High;
                    currentLow = currentBar.Low;
                    currentClose = currentBar.Close;
                    bodyMin = Math.Min(currentOpen, currentClose);
                    bodyMax = Math.Max(currentOpen, currentClose);

                    this.Log($"Using Previous Bar - O:{currentOpen:F5}, H:{currentHigh:F5}, L:{currentLow:F5}, C:{currentClose:F5}, BodyRange:[{bodyMin:F5}-{bodyMax:F5}]", StrategyLoggingLevel.Trading);

                    // 再次檢查是否還是一字板
                    if (currentOpen == currentHigh && currentHigh == currentLow && currentLow == currentClose)
                    {
                        this.Log("Previous bar is also a flat line - skipping signal check", StrategyLoggingLevel.Trading);
                        return;
                    }
                }
                else
                {
                    // 已經在使用前一根K線或沒有更多歷史數據，跳過信號檢查
                    this.Log("Flat line detected and no alternative bar available - skipping signal check", StrategyLoggingLevel.Trading);
                    return;
                }
            }

            // 檢查實體範圍是否過小
            double bodyRange = Math.Abs(bodyMax - bodyMin);
            if (bodyRange < this.CurrentSymbol.TickSize)
            {
                this.Log($"WARNING: Body range ({bodyRange:F5}) is smaller than tick size ({this.CurrentSymbol.TickSize:F5})", StrategyLoggingLevel.Trading);
            }

            // Check for buy signals (做多：min(O,C)到Low的範圍包含底部FIBO位置)
            // 正確的範圍應該是從bodyMin延伸到Low，即[bodyMin, currentLow]（如果bodyMin > currentLow則為[currentLow, bodyMin]）
            bool touchBottom1382 = !double.IsNaN(bottom1382) &&
                                   bottom1382 >= Math.Min(bodyMin, currentLow) &&
                                   bottom1382 <= Math.Max(bodyMin, currentLow);
            bool touchBottom1618 = !double.IsNaN(bottom1618) &&
                                   bottom1618 >= Math.Min(bodyMin, currentLow) &&
                                   bottom1618 <= Math.Max(bodyMin, currentLow);

            // Debug: 詳細的觸碰檢查日志
            if (!double.IsNaN(bottom1382))
            {
                double rangeMin = Math.Min(bodyMin, currentLow);
                double rangeMax = Math.Max(bodyMin, currentLow);
                this.Log($"Bottom1382 check: Level={bottom1382:F5}, Range=[{rangeMin:F5}, {rangeMax:F5}], InRange={touchBottom1382}", StrategyLoggingLevel.Trading);
            }
            if (!double.IsNaN(bottom1618))
            {
                double rangeMin = Math.Min(bodyMin, currentLow);
                double rangeMax = Math.Max(bodyMin, currentLow);
                this.Log($"Bottom1618 check: Level={bottom1618:F5}, Range=[{rangeMin:F5}, {rangeMax:F5}], InRange={touchBottom1618}", StrategyLoggingLevel.Trading);
            }

            if ((touchBottom1382 || touchBottom1618) && (this.TradingDirection == 1 || this.TradingDirection == 2)) // Both or LongOnly
            {
                string touchedLevel = touchBottom1382 ? $"Bottom1382({bottom1382:F5})" : $"Bottom1618({bottom1618:F5})";

                // 檢查每日交易次數限制
                if (!CheckDailyTradeLimit())
                {
                    this.Log($"BUY SIGNAL IGNORED: Daily trade limit reached ({this.dailyTradeCount}/{this.MaxDailyTrades}). Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
                    return;
                }

                // 檢查趨勢線交互（三重交互要求）- 做多信號
                bool hasTrendLineInteraction = CheckTrendLineInteraction(currentBar, true);

                if (!hasLongPosition)
                {
                    // 沒有多倉，檢查是否需要平空倉
                    if (hasShortPosition)
                    {
                        this.Log($"BUY SIGNAL: Price {currentPrice:F5} touched {touchedLevel}. Flattening short positions first.", StrategyLoggingLevel.Trading);
                        this.FlattenAllPositionsAndOrders();
                        // 設置等待標誌，下一個週期再下單
                        this.waitClosePositions = true;
                        return;
                    }
                    else if (hasTrendLineInteraction)
                    {
                        this.Log($"BUY SIGNAL: Price {currentPrice:F5} touched {touchedLevel} with trend line interaction, distance: {(touchBottom1382 ? Math.Abs(currentPrice - bottom1382) : Math.Abs(currentPrice - bottom1618)):F5}", StrategyLoggingLevel.Trading);
                        this.ExecuteBuyOrder(currentPrice, atrValue, bottom1382, bottom1618, actualLookback);
                        IncrementDailyTradeCount(); // 增加交易次數
                    }
                    else
                    {
                        this.Log($"BUY SIGNAL IGNORED: Price {currentPrice:F5} touched {touchedLevel} but no trend line interaction", StrategyLoggingLevel.Trading);
                    }
                }
                else
                {
                    this.Log($"BUY SIGNAL IGNORED: Already have long position. Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
                }
            }
            else if ((touchBottom1382 || touchBottom1618) && this.TradingDirection == 3) // ShortOnly
            {
                string touchedLevel = touchBottom1382 ? $"Bottom1382({bottom1382:F5})" : $"Bottom1618({bottom1618:F5})";
                this.Log($"BUY SIGNAL IGNORED: Strategy set to Short Only. Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
            }
            // Check for sell signals (做空：max(O,C)到High的範圍包含頂部FIBO位置)
            // 正確的範圍應該是從bodyMax延伸到High，即[bodyMax, currentHigh]（如果bodyMax > currentHigh則為[currentHigh, bodyMax]）
            bool touchTop1382 = !double.IsNaN(top1382) &&
                                top1382 >= Math.Min(bodyMax, currentHigh) &&
                                top1382 <= Math.Max(bodyMax, currentHigh);
            bool touchTop1618 = !double.IsNaN(top1618) &&
                                top1618 >= Math.Min(bodyMax, currentHigh) &&
                                top1618 <= Math.Max(bodyMax, currentHigh);

            // Debug: 詳細的觸碰檢查日志
            if (!double.IsNaN(top1382))
            {
                double rangeMin = Math.Min(bodyMax, currentHigh);
                double rangeMax = Math.Max(bodyMax, currentHigh);
                this.Log($"Top1382 check: Level={top1382:F5}, Range=[{rangeMin:F5}, {rangeMax:F5}], InRange={touchTop1382}", StrategyLoggingLevel.Trading);
            }
            if (!double.IsNaN(top1618))
            {
                double rangeMin = Math.Min(bodyMax, currentHigh);
                double rangeMax = Math.Max(bodyMax, currentHigh);
                this.Log($"Top1618 check: Level={top1618:F5}, Range=[{rangeMin:F5}, {rangeMax:F5}], InRange={touchTop1618}", StrategyLoggingLevel.Trading);
            }

            if ((touchTop1382 || touchTop1618) && (this.TradingDirection == 1 || this.TradingDirection == 3)) // Both or ShortOnly
            {
                string touchedLevel = touchTop1382 ? $"Top1382({top1382:F5})" : $"Top1618({top1618:F5})";

                // 檢查每日交易次數限制
                if (!CheckDailyTradeLimit())
                {
                    this.Log($"SELL SIGNAL IGNORED: Daily trade limit reached ({this.dailyTradeCount}/{this.MaxDailyTrades}). Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
                    return;
                }

                // 檢查趨勢線交互（三重交互要求）- 做空信號
                bool hasTrendLineInteraction = CheckTrendLineInteraction(currentBar, false);

                if (!hasShortPosition)
                {
                    // 沒有空倉，檢查是否需要平多倉
                    if (hasLongPosition)
                    {
                        this.Log($"SELL SIGNAL: Price {currentPrice:F5} touched {touchedLevel}. Flattening long positions first.", StrategyLoggingLevel.Trading);
                        this.FlattenAllPositionsAndOrders();
                        // 設置等待標誌，下一個週期再下單
                        this.waitClosePositions = true;
                        return;
                    }
                    else if (hasTrendLineInteraction)
                    {
                        this.Log($"SELL SIGNAL: Price {currentPrice:F5} touched {touchedLevel} with trend line interaction, distance: {(touchTop1382 ? Math.Abs(currentPrice - top1382) : Math.Abs(currentPrice - top1618)):F5}", StrategyLoggingLevel.Trading);
                        this.ExecuteSellOrder(currentPrice, atrValue, top1382, top1618, actualLookback);
                        IncrementDailyTradeCount(); // 增加交易次數
                    }
                    else
                    {
                        this.Log($"SELL SIGNAL IGNORED: Price {currentPrice:F5} touched {touchedLevel} but no trend line interaction", StrategyLoggingLevel.Trading);
                    }
                }
                else
                {
                    this.Log($"SELL SIGNAL IGNORED: Already have short position. Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
                }
            }
            else if ((touchTop1382 || touchTop1618) && this.TradingDirection == 2) // LongOnly
            {
                string touchedLevel = touchTop1382 ? $"Top1382({top1382:F5})" : $"Top1618({top1618:F5})";
                this.Log($"SELL SIGNAL IGNORED: Strategy set to Long Only. Price {currentPrice:F5} touched {touchedLevel}", StrategyLoggingLevel.Trading);
            }
        }

        private void ExecuteBuyOrder(double currentPrice, double atrValue, double bottom1382, double bottom1618, int actualFiboLookback)
        {
            // 記錄當前 FIBO 位置的買入信號
            this.RecordBuySellSignal(currentPrice, true, bottom1382, bottom1618, actualFiboLookback);

            // 獲取 BOX 邊界用於止盈目標（做多到 BOX 下界）
            var boxData = GetValidBoxBoundaries(actualFiboLookback);
            double targetPrice;
            string entryLevel;
            string targetLevel;

            // 判斷觸碰的是哪個底部 FIBO 位置
            // 獲取K線數據（遵循 UsePreviousCompleteBar 設置）
            int barIndex = this.UsePreviousCompleteBar ? 1 : 0;
            var currentBar = (HistoryItemBar)this.historicalData[barIndex];
            double bodyMin = Math.Min(currentBar.Open, currentBar.Close);
            bool touching1382 = !double.IsNaN(bottom1382) &&
                               bottom1382 >= Math.Min(bodyMin, currentBar.Low) &&
                               bottom1382 <= Math.Max(bodyMin, currentBar.Low);

            if (touching1382)
            {
                entryLevel = "Bottom 1.382";
            }
            else
            {
                entryLevel = "Bottom 1.618";
            }

            // 根據參數選擇止盈目標類型（參考Selected_Range_Lines_Indicator.cs邏輯）
            if (this.TPTargetType == 1) // Default Mode
            {
                // 默認模式：1.382到空間內對面0.618位置，1.618到Same Side Box Boundary
                if (touching1382)
                {
                    // 1.382觸碰：TP到空間內對面0.618位置
                    var fiboData = GetValidFiboLevels();
                    if (!double.IsNaN(fiboData.Top1382) && !double.IsNaN(fiboData.Bottom1382))
                    {
                        // 計算0.618位置（在BOX空間內）
                        double range = fiboData.Top1382 - fiboData.Bottom1382;
                        double goldenFraction = 0.618033988749895;
                        targetPrice = fiboData.Bottom1382 + range * goldenFraction;
                        targetLevel = "Inside 0.618 (1.382 Default)";
                    }
                    else if (!double.IsNaN(boxData.TopBoxOC))
                    {
                        targetPrice = boxData.TopBoxOC;
                        targetLevel = "Top BOX max(o,c) (1.382 fallback)";
                    }
                    else
                    {
                        double fallbackFiboLevel = bottom1382;
                        targetPrice = currentPrice + Math.Abs(currentPrice - fallbackFiboLevel) * 2;
                        targetLevel = "1:2 Risk Reward (1.382 fallback)";
                    }
                }
                else // touching1618
                {
                    // 1.618觸碰：TP到Same Side Box Boundary
                    if (!double.IsNaN(boxData.BottomBoxOC))
                    {
                        targetPrice = boxData.BottomBoxOC;
                        targetLevel = "Bottom BOX min(o,c) (1.618 Default)";
                    }
                    else
                    {
                        double fallbackFiboLevel = bottom1618;
                        targetPrice = currentPrice + Math.Abs(currentPrice - fallbackFiboLevel) * 2;
                        targetLevel = "1:2 Risk Reward (1.618 fallback)";
                    }
                }
            }
            else if (this.TPTargetType == 2) // Long Mode
            {
                // 長模式：1.382和1.618都到對面的Opposite Fibo Level
                var fiboData = GetValidFiboLevels();
                if (!double.IsNaN(fiboData.Top1382))
                {
                    targetPrice = fiboData.Top1382;
                    targetLevel = "Top 1.382 (Long Mode)";
                }
                else if (!double.IsNaN(fiboData.Top1618))
                {
                    targetPrice = fiboData.Top1618;
                    targetLevel = "Top 1.618 (Long Mode)";
                }
                else if (!double.IsNaN(boxData.TopBoxOC))
                {
                    targetPrice = boxData.TopBoxOC;
                    targetLevel = "Top BOX max(o,c) (Long Mode fallback)";
                }
                else
                {
                    double fallbackFiboLevel = touching1382 ? bottom1382 : bottom1618;
                    targetPrice = currentPrice + Math.Abs(currentPrice - fallbackFiboLevel) * 2;
                    targetLevel = "1:2 Risk Reward (Long Mode fallback)";
                }
            }
            else // TPTargetType == 3, Opposite FIBO Level
            {
                // 做多的止盈目標：對面的 FIBO 位置
                var fiboData = GetValidFiboLevels();
                if (!double.IsNaN(fiboData.Top1382))
                {
                    targetPrice = fiboData.Top1382;
                    targetLevel = "Top 1.382 (FIBO)";
                }
                else if (!double.IsNaN(fiboData.Top1618))
                {
                    targetPrice = fiboData.Top1618;
                    targetLevel = "Top 1.618 (FIBO)";
                }
                else if (!double.IsNaN(boxData.BottomBoxOC))
                {
                    // 備用：使用 BOX 邊界
                    targetPrice = boxData.BottomBoxOC;
                    targetLevel = "Bottom BOX min(o,c) (FIBO fallback)";
                }
                else
                {
                    // 最後備用：1:2 風險回報
                    double fallbackFiboLevel = touching1382 ? bottom1382 : bottom1618;
                    targetPrice = currentPrice + Math.Abs(currentPrice - fallbackFiboLevel) * 2;
                    targetLevel = "1:2 Risk Reward (FIBO fallback)";
                }
            }

            double stopLoss = currentPrice - (atrValue * this.ATRMultiplier);
            double fiboLevel = touching1382 ? bottom1382 : bottom1618;

            this.Log($"BUY ORDER: Entry at {entryLevel} ({currentPrice:F5}) → Target: {targetLevel} ({targetPrice:F5})", StrategyLoggingLevel.Trading);
            this.PlaceOrder(Side.Buy, currentPrice, stopLoss, targetPrice, fiboLevel);
        }

        private void ExecuteSellOrder(double currentPrice, double atrValue, double top1382, double top1618, int actualFiboLookback)
        {
            // 記錄當前 FIBO 位置的賣出信號
            this.RecordBuySellSignal(currentPrice, false, top1382, top1618, actualFiboLookback);

            // 獲取 BOX 邊界用於止盈目標（做空到 BOX 上界）
            var boxData = GetValidBoxBoundaries(actualFiboLookback);
            double targetPrice;
            string entryLevel;
            string targetLevel;

            // 判斷觸碰的是哪個頂部 FIBO 位置
            // 獲取K線數據（遵循 UsePreviousCompleteBar 設置）
            int barIndex = this.UsePreviousCompleteBar ? 1 : 0;
            var currentBar = (HistoryItemBar)this.historicalData[barIndex];
            double bodyMax = Math.Max(currentBar.Open, currentBar.Close);
            bool touching1382 = !double.IsNaN(top1382) &&
                               top1382 >= Math.Min(bodyMax, currentBar.High) &&
                               top1382 <= Math.Max(bodyMax, currentBar.High);

            if (touching1382)
            {
                entryLevel = "Top 1.382";
            }
            else
            {
                entryLevel = "Top 1.618";
            }

            // 根據參數選擇止盈目標類型（參考Selected_Range_Lines_Indicator.cs邏輯）
            if (this.TPTargetType == 1) // Default Mode
            {
                // 默認模式：1.382到空間內對面0.618位置，1.618到Same Side Box Boundary
                if (touching1382)
                {
                    // 1.382觸碰：TP到空間內對面0.618位置
                    var fiboData = GetValidFiboLevels();
                    if (!double.IsNaN(fiboData.Top1382) && !double.IsNaN(fiboData.Bottom1382))
                    {
                        // 計算0.618位置（在BOX空間內）
                        double range = fiboData.Top1382 - fiboData.Bottom1382;
                        double goldenFraction = 0.618033988749895;
                        targetPrice = fiboData.Top1382 - range * goldenFraction;
                        targetLevel = "Inside 0.618 (1.382 Default)";
                    }
                    else if (!double.IsNaN(boxData.BottomBoxOC))
                    {
                        targetPrice = boxData.BottomBoxOC;
                        targetLevel = "Bottom BOX min(o,c) (1.382 fallback)";
                    }
                    else
                    {
                        double fallbackFiboLevel = top1382;
                        targetPrice = currentPrice - Math.Abs(fallbackFiboLevel - currentPrice) * 2;
                        targetLevel = "1:2 Risk Reward (1.382 fallback)";
                    }
                }
                else // touching1618
                {
                    // 1.618觸碰：TP到Same Side Box Boundary
                    if (!double.IsNaN(boxData.TopBoxOC))
                    {
                        targetPrice = boxData.TopBoxOC;
                        targetLevel = "Top BOX max(o,c) (1.618 Default)";
                    }
                    else
                    {
                        double fallbackFiboLevel = top1618;
                        targetPrice = currentPrice - Math.Abs(fallbackFiboLevel - currentPrice) * 2;
                        targetLevel = "1:2 Risk Reward (1.618 fallback)";
                    }
                }
            }
            else if (this.TPTargetType == 2) // Long Mode
            {
                // 長模式：1.382和1.618都到對面的Opposite Fibo Level
                var fiboData = GetValidFiboLevels();
                if (!double.IsNaN(fiboData.Bottom1382))
                {
                    targetPrice = fiboData.Bottom1382;
                    targetLevel = "Bottom 1.382 (Long Mode)";
                }
                else if (!double.IsNaN(fiboData.Bottom1618))
                {
                    targetPrice = fiboData.Bottom1618;
                    targetLevel = "Bottom 1.618 (Long Mode)";
                }
                else if (!double.IsNaN(boxData.BottomBoxOC))
                {
                    targetPrice = boxData.BottomBoxOC;
                    targetLevel = "Bottom BOX min(o,c) (Long Mode fallback)";
                }
                else
                {
                    double fallbackFiboLevel = touching1382 ? top1382 : top1618;
                    targetPrice = currentPrice - Math.Abs(fallbackFiboLevel - currentPrice) * 2;
                    targetLevel = "1:2 Risk Reward (Long Mode fallback)";
                }
            }
            else // TPTargetType == 3, Opposite FIBO Level
            {
                // 做空的止盈目標：對面的 FIBO 位置
                var fiboData = GetValidFiboLevels();
                if (!double.IsNaN(fiboData.Bottom1382))
                {
                    targetPrice = fiboData.Bottom1382;
                    targetLevel = "Bottom 1.382 (FIBO)";
                }
                else if (!double.IsNaN(fiboData.Bottom1618))
                {
                    targetPrice = fiboData.Bottom1618;
                    targetLevel = "Bottom 1.618 (FIBO)";
                }
                else if (!double.IsNaN(boxData.TopBoxOC))
                {
                    // 備用：使用 BOX 邊界
                    targetPrice = boxData.TopBoxOC;
                    targetLevel = "Top BOX max(o,c) (FIBO fallback)";
                }
                else
                {
                    // 最後備用：1:2 風險回報
                    double fallbackFiboLevel = touching1382 ? top1382 : top1618;
                    targetPrice = currentPrice - Math.Abs(fallbackFiboLevel - currentPrice) * 2;
                    targetLevel = "1:2 Risk Reward (FIBO fallback)";
                }
            }

            double stopLoss = currentPrice + (atrValue * this.ATRMultiplier);
            double fiboLevel = touching1382 ? top1382 : top1618;

            this.Log($"SELL ORDER: Entry at {entryLevel} ({currentPrice:F5}) → Target: {targetLevel} ({targetPrice:F5})", StrategyLoggingLevel.Trading);
            this.PlaceOrder(Side.Sell, currentPrice, stopLoss, targetPrice, fiboLevel);
        }

        private void RecordBuySellSignal(double currentPrice, bool isBuySignal, double fiboLevel1, double fiboLevel2, int actualFiboLookback)
        {
            // 記錄買賣信號發生時的 FIBO 位置，用於後續的 BOX 邊界止盈
            var signal = new BuySellSignalRecord
            {
                Time = this.historicalData[0].TimeLeft,
                Price = currentPrice,
                IsBuySignal = isBuySignal,
                FiboLevel1 = fiboLevel1,
                FiboLevel2 = fiboLevel2,
                ActualLookback = actualFiboLookback
            };

            this.buySellSignals.Add(signal);

            // 保持記錄數量在合理範圍內
            if (this.buySellSignals.Count > 100)
            {
                this.buySellSignals.RemoveAt(0);
            }

            string signalType = isBuySignal ? "BUY" : "SELL";
            this.Log($"{signalType} SIGNAL RECORDED: Price={currentPrice:F5}, Fibo1={fiboLevel1:F5}, Fibo2={fiboLevel2:F5}, Lookback={actualFiboLookback}", StrategyLoggingLevel.Trading);
        }

        private bool IsWithinTradingHours()
        {
            // 使用與 MarkBuySellLine.cs 相同的時間檢查邏輯
            DateTime utcTime = this.historicalData[0].TimeLeft;
            DateTime cTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(utcTime, Core.Instance.TimeUtils.SelectedTimeZone);
            int currentTime = cTime.Hour * 100 + cTime.Minute;

            // 下午1點到凌晨1點的交易時間
            // 13:00 (1300) 到 23:59 (2359) 或 00:00 (0) 到 01:00 (100)
            bool isAfternoonToMidnight = currentTime >= TRADING_START_TIME; // 13:00 到 23:59
            bool isMidnightToEarlyMorning = currentTime <= TRADING_END_TIME; // 00:00 到 01:00

            bool isWithinTradingHours = isAfternoonToMidnight || isMidnightToEarlyMorning;

            if (!isWithinTradingHours)
            {
                this.Log($"Outside trading hours: {cTime:HH:mm} (code: {currentTime}). Trading hours: 13:00-01:00", StrategyLoggingLevel.Trading);
            }

            return isWithinTradingHours;
        }

        private bool ShouldCloseAllPositions()
        {
            // 每日凌晨3點平倉
            DateTime utcTime = this.historicalData[0].TimeLeft;
            DateTime cTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(utcTime, Core.Instance.TimeUtils.SelectedTimeZone);

            // 檢查是否為凌晨3點（300）
            int currentTime = cTime.Hour * 100 + cTime.Minute;
            bool isCloseTime = currentTime >= 300 && currentTime <= 305; // 3:00-3:05 AM

            if (isCloseTime)
            {
                this.Log($"Daily close time reached: {cTime:HH:mm} (code: {currentTime})", StrategyLoggingLevel.Trading);
            }

            return isCloseTime;
        }

        private void CloseAllPositions(Position[] positions)
        {
            FlattenAllPositionsAndOrders();
        }

        private void FlattenAllPositionsAndOrders()
        {
            // 取消所有掛單
            var pendingOrders = Core.Instance.Orders.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            if (pendingOrders.Any())
            {
                this.Log($"Cancelling {pendingOrders.Length} pending orders", StrategyLoggingLevel.Trading);
                foreach (var order in pendingOrders)
                {
                    var cancelResult = order.Cancel();
                    if (cancelResult.Status == TradingOperationResultStatus.Failure)
                    {
                        this.Log($"Failed to cancel order: {cancelResult.Message}", StrategyLoggingLevel.Error);
                    }
                    else
                    {
                        this.Log($"Order cancelled: {order.Side}", StrategyLoggingLevel.Trading);
                    }
                }
            }

            // 平掉所有持倉
            var existingPositions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            if (existingPositions.Any())
            {
                this.Log($"Flattening {existingPositions.Length} positions for direction change", StrategyLoggingLevel.Trading);
                foreach (var position in existingPositions)
                {
                    var result = position.Close();
                    if (result.Status == TradingOperationResultStatus.Failure)
                    {
                        this.Log($"Failed to close position: {result.Message}", StrategyLoggingLevel.Error);
                    }
                    else
                    {
                        this.Log($"Position flattened: {position.Side} {position.Quantity}", StrategyLoggingLevel.Trading);
                    }
                }
            }
        }

        private void PlaceOrder(Side side, double currentPrice, double stopLoss, double targetPrice, double fiboLevel)
        {
            this.waitingForOrder = true;
            this.waitOpenPosition = true; // 參考 SimpleMACross.cs

            // 記錄開始等待的時間，用於超時檢查
            this.waitingForOrderStartTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);

            // 格式化價格以確保符合交易所要求 (參考 Selected_Range_Lines_Indicator.cs)
            double formattedCurrentPrice = side == Side.Buy ?
                this.FormatSymbolPriceUp(currentPrice) :
                this.FormatSymbolPriceDown(currentPrice);

            double formattedStopLoss = side == Side.Buy ?
                this.FormatSymbolPriceDown(stopLoss) :  // 買入止損向下取整
                this.FormatSymbolPriceUp(stopLoss);     // 賣出止損向上取整

            double formattedTargetPrice = side == Side.Buy ?
                this.FormatSymbolPriceUp(targetPrice) :  // 買入止盈向上取整
                this.FormatSymbolPriceDown(targetPrice); // 賣出止盈向下取整

            // 使用格式化後的價格計算止盈止損 ticks
            double slTicks, tpTicks;

            if (side == Side.Buy)
            {
                // 買入：SL = 從 止損價格 到 當前價格 的 ticks（正數）
                slTicks = this.CurrentSymbol.CalculateTicks(formattedStopLoss, formattedCurrentPrice);
                // 買入：TP = 從 當前價格 到 目標價格 的 ticks（正數）
                tpTicks = this.CurrentSymbol.CalculateTicks(formattedCurrentPrice, formattedTargetPrice);
            }
            else
            {
                // 賣出：SL = 從 當前價格 到 止損價格 的 ticks（正數）
                slTicks = this.CurrentSymbol.CalculateTicks(formattedCurrentPrice, formattedStopLoss);
                // 賣出：TP = 從 目標價格 到 當前價格 的 ticks（正數）
                tpTicks = this.CurrentSymbol.CalculateTicks(formattedTargetPrice, formattedCurrentPrice);
            }

            this.Log($"Placing {side} order: Price={formattedCurrentPrice:F5} (orig:{currentPrice:F5}), SL={formattedStopLoss:F5} ({slTicks:F1} ticks), TP={formattedTargetPrice:F5} ({tpTicks:F1} ticks)", StrategyLoggingLevel.Trading);

            var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
            {
                Account = this.CurrentAccount,
                Symbol = this.CurrentSymbol,
                OrderTypeId = this.orderTypeIdMarket,
                Side = side,
                Quantity = this.Quantity,
                StopLoss = SlTpHolder.CreateSL(slTicks, PriceMeasurement.Offset),
                TakeProfit = SlTpHolder.CreateTP(tpTicks, PriceMeasurement.Offset)
            });

            // 處理下單結果 (參考 SimpleMACross.cs)
            if (result.Status == TradingOperationResultStatus.Failure)
            {
                this.Log($"Place {side} order failed: {(string.IsNullOrEmpty(result.Message) ? result.Status : result.Message)}", StrategyLoggingLevel.Error);
                // 下單失敗時重置所有等待標誌
                this.waitingForOrder = false;
                this.waitOpenPosition = false;
                this.waitingForOrderStartTime = DateTime.MinValue;
                this.Log("下單失敗，重置等待標誌", StrategyLoggingLevel.Trading);
            }
            else if (result.Status == TradingOperationResultStatus.Success)
            {
                this.Log($"Order placed successfully: {result.Status}", StrategyLoggingLevel.Trading);
                this.Log($"{side} order placed: Price={currentPrice:F5}, SL={stopLoss:F5}, TP={targetPrice:F5}, Fibo={fiboLevel:F5}", StrategyLoggingLevel.Trading);
                // 市價單成功後，如果是市價單應該立即執行，設置較短的超時時間
                this.Log($"市價單已提交，等待執行確認，超時時間: {WAITING_FOR_ORDER_TIMEOUT_SECONDS}秒", StrategyLoggingLevel.Trading);
            }
            else
            {
                // 其他狀態（如Pending等）也重置標誌，避免卡住
                this.Log($"{side} order result: {result.Status}, Message: {result.Message}", StrategyLoggingLevel.Trading);
                this.waitingForOrder = false;
                this.waitOpenPosition = false;
                this.waitingForOrderStartTime = DateTime.MinValue;
                this.Log("下單結果非成功狀態，重置等待標誌", StrategyLoggingLevel.Trading);
            }
        }

        private FiboLevelsData GetValidFiboLevels()
        {
            this.Log("=== 開始正確的InsideBar FIBO位置計算 ===", StrategyLoggingLevel.Trading);

            int maxLookback = Math.Min(this.FiboSearchMaxRange, this.historicalData.Count - 1);
            this.Log($"FIBO位置搜索範圍: {this.FiboLookbackBars} 到 {maxLookback} (最大範圍參數: {this.FiboSearchMaxRange})", StrategyLoggingLevel.Trading);

            // 步驟1：檢測InsideBar並創建BOX
            var detectedBoxes = DetectInsideBars(maxLookback);
            this.Log($"檢測到 {detectedBoxes.Count} 個InsideBar BOX", StrategyLoggingLevel.Trading);

            if (detectedBoxes.Count == 0)
            {
                this.Log("沒有檢測到InsideBar BOX，返回空FIBO位置", StrategyLoggingLevel.Trading);
                return new FiboLevelsData { ActualLookback = this.FiboLookbackBars };
            }

            // 步驟2：找到有效的BOX配對
            var validPairs = FindValidBoxPairs(detectedBoxes);
            this.Log($"找到 {validPairs.Count} 個有效的BOX配對", StrategyLoggingLevel.Trading);

            if (validPairs.Count == 0)
            {
                this.Log("沒有找到有效的BOX配對，返回空FIBO位置", StrategyLoggingLevel.Trading);
                return new FiboLevelsData { ActualLookback = this.FiboLookbackBars };
            }

            // 步驟3：計算每個配對的FIBO位置
            var allFiboLevels = new List<FiboLevelInfo>();
            foreach (var pair in validPairs)
            {
                var fiboLevels = CalculateFiboLevelsFromPair(pair);
                allFiboLevels.AddRange(fiboLevels);
            }

            this.Log($"從配對計算出 {allFiboLevels.Count} 個FIBO位置", StrategyLoggingLevel.Trading);

            // 步驟4：過濾互動檢查
            var validFiboLevels = allFiboLevels.Where(fibo =>
                !HasInteractionInLast200Bars(fibo.Level, fibo.Type.Contains("Bottom") ? "Bottom" : "Top", fibo.Lookback)
            ).ToList();

            this.Log($"互動檢查後剩餘 {validFiboLevels.Count} 個FIBO位置", StrategyLoggingLevel.Trading);

            if (!validFiboLevels.Any())
            {
                this.Log("所有FIBO位置都有互動記錄，使用最近的配對", StrategyLoggingLevel.Trading);
                validFiboLevels = allFiboLevels.Take(8).ToList();
            }

            // 步驟5：選擇最佳FIBO位置組合
            var bestFiboData = SelectBestFiboLevels(validFiboLevels);

            this.Log($"=== FIBO位置計算完成 ===", StrategyLoggingLevel.Trading);
            this.Log($"最終選擇 - Bottom1382: {bestFiboData.Bottom1382:F5}, Bottom1618: {bestFiboData.Bottom1618:F5}, Top1382: {bestFiboData.Top1382:F5}, Top1618: {bestFiboData.Top1618:F5}, Lookback: {bestFiboData.ActualLookback}", StrategyLoggingLevel.Trading);

            return bestFiboData;
        }

        private BoxBoundariesData GetValidBoxBoundaries(int actualLookback)
        {
            // 從實際的 FIBO 回看週期開始，查找有效的 Box 邊界
            int maxLookback = Math.Min(100, this.historicalData.Count - 1);

            for (int lookback = actualLookback; lookback <= maxLookback; lookback++)
            {
                double bottomBoxOC = this.insideBarIndicator.GetValue(lookback, 4);
                double topBoxOC = this.insideBarIndicator.GetValue(lookback, 5);

                // 檢查是否有有效的 Box 邊界
                bool hasValidBox = !double.IsNaN(bottomBoxOC) || !double.IsNaN(topBoxOC);

                if (hasValidBox)
                {
                    if (lookback > actualLookback)
                    {
                        this.Log($"No valid Box boundaries at {actualLookback} bars ago, using {lookback} bars ago", StrategyLoggingLevel.Trading);
                    }

                    return new BoxBoundariesData
                    {
                        BottomBoxOC = bottomBoxOC,
                        TopBoxOC = topBoxOC,
                        ActualLookback = lookback
                    };
                }
            }

            // 如果找不到任何有效的 Box 邊界，返回 NaN 值
            this.Log($"No valid Box boundaries found within {maxLookback} bars", StrategyLoggingLevel.Trading);
            return new BoxBoundariesData
            {
                BottomBoxOC = double.NaN,
                TopBoxOC = double.NaN,
                ActualLookback = actualLookback
            };
        }

        private void UpdateFiboCacheAtCheckTime()
        {
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);

            int timeCode = currentTime.Hour * 100 + currentTime.Minute;

            // 檢查是否在CheckTime範圍內
            bool isCheckTime1 = timeCode >= TIME_CHECK1_START && timeCode <= TIME_CHECK1_END;
            bool isCheckTime2 = timeCode >= TIME_CHECK2_START && timeCode <= TIME_CHECK2_END;
            bool isCheckTime3 = timeCode >= TIME_CHECK3_START && timeCode <= TIME_CHECK3_END;
            bool isCheckTime = isCheckTime1 || isCheckTime2 || isCheckTime3;

            // 如果緩存為空（首次運行）或在CheckTime且距離上次更新超過1小時，則更新緩存
            bool shouldUpdate = !this.cachedFiboLevels.Any() ||
                               (isCheckTime && (currentTime - this.lastFiboCacheTime).TotalHours >= 1);

            if (shouldUpdate)
            {
                this.lastFiboCacheTime = currentTime;
                this.cachedFiboLevels.Clear();

                string reason = !this.cachedFiboLevels.Any() ? "Initial cache" : "CheckTime update";
                this.Log($"Updating FIBO cache ({reason}): {currentTime:yyyy-MM-dd HH:mm} (code: {timeCode})", StrategyLoggingLevel.Trading);

                // 搜索有效的 FIBO 級別（200根內沒有互動的）
                this.CollectUninteractedFiboLevels();

                this.Log($"FIBO cache updated: {this.cachedFiboLevels.Count} levels collected", StrategyLoggingLevel.Trading);
            }
            else
            {
                this.Log($"FIBO cache NOT updated: Time={currentTime:HH:mm} (code: {timeCode}), IsCheckTime={isCheckTime}, HoursSinceLastUpdate={(currentTime - this.lastFiboCacheTime).TotalHours:F1}, CacheCount={this.cachedFiboLevels.Count}", StrategyLoggingLevel.Trading);
            }
        }

        private void CollectUninteractedFiboLevels()
        {
            int maxLookback = Math.Min(500, this.historicalData.Count - 1); // 搜索500根K線
            var collectedLevels = new List<FiboLevel>();

            for (int lookback = this.FiboLookbackBars; lookback <= maxLookback; lookback++)
            {
                double bottom1382 = this.insideBarIndicator.GetValue(lookback, 0);
                double bottom1618 = this.insideBarIndicator.GetValue(lookback, 1);
                double top1382 = this.insideBarIndicator.GetValue(lookback, 2);
                double top1618 = this.insideBarIndicator.GetValue(lookback, 3);

                // 檢查每個有效的 FIBO 級別是否在過去200根內沒有互動
                if (!double.IsNaN(bottom1382) && !HasInteractionInLast200Bars(bottom1382, "Bottom", lookback))
                {
                    collectedLevels.Add(new FiboLevel
                    {
                        Level = bottom1382,
                        Type = "Bottom1382",
                        Lookback = lookback,
                        Timestamp = this.historicalData[lookback].TimeLeft
                    });
                }

                if (!double.IsNaN(bottom1618) && !HasInteractionInLast200Bars(bottom1618, "Bottom", lookback))
                {
                    collectedLevels.Add(new FiboLevel
                    {
                        Level = bottom1618,
                        Type = "Bottom1618",
                        Lookback = lookback,
                        Timestamp = this.historicalData[lookback].TimeLeft
                    });
                }

                if (!double.IsNaN(top1382) && !HasInteractionInLast200Bars(top1382, "Top", lookback))
                {
                    collectedLevels.Add(new FiboLevel
                    {
                        Level = top1382,
                        Type = "Top1382",
                        Lookback = lookback,
                        Timestamp = this.historicalData[lookback].TimeLeft
                    });
                }

                if (!double.IsNaN(top1618) && !HasInteractionInLast200Bars(top1618, "Top", lookback))
                {
                    collectedLevels.Add(new FiboLevel
                    {
                        Level = top1618,
                        Type = "Top1618",
                        Lookback = lookback,
                        Timestamp = this.historicalData[lookback].TimeLeft
                    });
                }
            }

            // 按時間排序，選擇最新的幾個級別
            var sortedLevels = collectedLevels
                .OrderByDescending(x => x.Timestamp)
                .Take(this.MaxDailyFiboLevels * 4) // 每種類型最多取 MaxDailyFiboLevels 個
                .ToList();

            this.cachedFiboLevels = sortedLevels;

            // 記錄收集到的級別
            foreach (var level in this.cachedFiboLevels)
            {
                this.Log($"Collected uninteracted FIBO: {level.Type} = {level.Level:F5} (lookback: {level.Lookback})", StrategyLoggingLevel.Trading);
            }
        }

        private bool HasInteractionInLast200Bars(double fiboLevel, string fiboType, int fiboLookback)
        {
            // 檢查從FIBO位置開始往當前200根K線內是否有任何互動
            // fiboLookback是FIBO位置距離當前的回看數，我們要檢查從fiboLookback到當前之間的200根K線
            int startCheck = Math.Max(0, Math.Min(fiboLookback, 200)); // 從FIBO位置開始，最多檢查200根
            int endCheck = 0; // 檢查到當前K線（index 0）

            for (int i = startCheck; i >= endCheck; i--)
            {
                if (i >= this.historicalData.Count) continue;

                var bar = (HistoryItemBar)this.historicalData[i];
                double bodyMin = Math.Min(bar.Open, bar.Close);
                double bodyMax = Math.Max(bar.Open, bar.Close);

                bool hasInteraction = false;

                if (fiboType == "Bottom")
                {
                    // 底部FIBO：檢查是否在 min(O,C) 到 Low 的範圍內
                    hasInteraction = fiboLevel >= Math.Min(bodyMin, bar.Low) && fiboLevel <= Math.Max(bodyMin, bar.Low);
                }
                else if (fiboType == "Top")
                {
                    // 頂部FIBO：檢查是否在 max(O,C) 到 High 的範圍內
                    hasInteraction = fiboLevel >= Math.Min(bodyMax, bar.High) && fiboLevel <= Math.Max(bodyMax, bar.High);
                }

                if (hasInteraction)
                {
                    this.Log($"FIBO {fiboType} {fiboLevel:F5} had interaction at bar {i} (lookback from current: {i})", StrategyLoggingLevel.Trading);
                    return true;
                }
            }

            this.Log($"FIBO {fiboType} {fiboLevel:F5} had NO interaction in last 200 bars", StrategyLoggingLevel.Trading);
            return false;
        }

        private FiboLevelsData GetCachedFiboLevels()
        {
            if (!this.cachedFiboLevels.Any())
            {
                this.Log("No cached FIBO levels available, falling back to search mode", StrategyLoggingLevel.Trading);
                return GetValidFiboLevels();
            }

            // 直接返回所有緩存的FIBO級別，不需要等待觸碰
            // 觸碰檢查在ProcessSignals()中進行
            var result = new FiboLevelsData
            {
                Bottom1382 = this.cachedFiboLevels.FirstOrDefault(x => x.Type == "Bottom1382")?.Level ?? double.NaN,
                Bottom1618 = this.cachedFiboLevels.FirstOrDefault(x => x.Type == "Bottom1618")?.Level ?? double.NaN,
                Top1382 = this.cachedFiboLevels.FirstOrDefault(x => x.Type == "Top1382")?.Level ?? double.NaN,
                Top1618 = this.cachedFiboLevels.FirstOrDefault(x => x.Type == "Top1618")?.Level ?? double.NaN,
                ActualLookback = this.cachedFiboLevels.FirstOrDefault()?.Lookback ?? this.FiboLookbackBars
            };

            this.Log($"Using cached FIBO levels: B1382={result.Bottom1382:F5}, B1618={result.Bottom1618:F5}, T1382={result.Top1382:F5}, T1618={result.Top1618:F5}", StrategyLoggingLevel.Trading);

            // 顯示當日趨勢線狀態
            string trendLineStats = GetDailyTrendLineStats();
            this.Log($"Trend line status: {trendLineStats}", StrategyLoggingLevel.Trading);

            // 顯示每日交易統計
            string tradeStats = GetDailyTradeStats();
            this.Log($"Trade status: {tradeStats}", StrategyLoggingLevel.Trading);

            return result;
        }

        #region 趨勢線檢測功能

        /// <summary>
        /// 獲取動態觸點閾值（基於價格和時間框架）
        /// </summary>
        private double GetDynamicTouchThreshold(double price)
        {
            // 簡化版本的動態閾值計算
            // 基於價格水平調整觸點容忍度
            double baseThreshold = 0.001; // 0.1%基礎閾值

            // 根據價格水平調整
            if (price > 100000) // 高價格資產（如比特幣）
                baseThreshold = 0.0005; // 0.05%
            else if (price > 10000)
                baseThreshold = 0.001; // 0.1%
            else if (price > 1000)
                baseThreshold = 0.002; // 0.2%
            else if (price > 100)
                baseThreshold = 0.003; // 0.3%
            else
                baseThreshold = 0.005; // 0.5%

            // 設置合理的上下限
            double minThreshold = 0.0001; // 最小0.01%
            double maxThreshold = 0.01;   // 最大1%

            double dynamicThreshold = Math.Max(minThreshold, Math.Min(maxThreshold, baseThreshold));

            // this.Log($"Dynamic threshold for price {price:F2}: {dynamicThreshold:F6} ({dynamicThreshold*100:F3}%)", StrategyLoggingLevel.Trading);
            return dynamicThreshold;
        }

        /// <summary>
        /// 檢查當前K線是否同時與直線和弧形趨勢線交互
        /// </summary>
        private bool CheckTrendLineInteraction(HistoryItemBar currentBar, bool isLongSignal)
        {
            if (!this.EnableTrendLineCheck)
            {
                this.Log("Trend line check disabled, skipping", StrategyLoggingLevel.Trading);
                return true; // 如果禁用趨勢線檢查，直接通過
            }

            // 檢查是否需要重置當日趨勢線記錄
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                currentBar.TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);
            DateTime currentDate = currentTime.Date;

            // 如果是新的一天，重置所有記錄
            if (currentDate != this.lastSignalDate.Date)
            {
                this.firstTrendLines.Clear();
                this.secondTrendLines.Clear();
                this.signalCount = 0;
                this.recordedRangeStart = -1;
                this.Log($"New day detected: {currentDate:yyyy-MM-dd}, resetting trend line records", StrategyLoggingLevel.Trading);
            }

            this.signalCount++;
            this.lastSignalDate = currentTime;

            List<TrendLineData> trendLinesToUse;

            if (this.signalCount == 1)
            {
                // 第一次信號：生成並記錄趨勢線，同時記錄判定範圍起始K
                this.Log("First signal: generating and recording trend lines + range start", StrategyLoggingLevel.Trading);

                UpdateTrendLineCache();

                if (!this.cachedTrendLines.Any())
                {
                    this.Log("No trend lines generated, skipping trend line check", StrategyLoggingLevel.Trading);
                    return true;
                }

                // 記錄第一次的趨勢線和範圍起始K
                this.firstTrendLines.Clear();
                this.firstTrendLines.AddRange(this.cachedTrendLines);
                this.recordedRangeStart = this.TrendLineRangeStart; // 記錄當前的範圍起始

                trendLinesToUse = this.firstTrendLines;
                this.Log($"First signal recorded: {trendLinesToUse.Count} trend lines, range start: {this.recordedRangeStart}", StrategyLoggingLevel.Trading);
            }
            else if (this.signalCount == 2)
            {
                // 第二次信號：用記錄的起始K到當前TrendLineRangeEnd重新生成趨勢線
                this.Log($"Second signal: regenerating trend lines from recorded start ({this.recordedRangeStart}) to current end ({this.TrendLineRangeEnd})", StrategyLoggingLevel.Trading);

                // 使用記錄的起始K和當前的結束K重新生成趨勢線
                UpdateTrendLineCacheWithCustomRange(this.recordedRangeStart, this.TrendLineRangeEnd);

                if (!this.cachedTrendLines.Any())
                {
                    this.Log("No trend lines generated for second signal, using first signal trend lines", StrategyLoggingLevel.Trading);
                    trendLinesToUse = this.firstTrendLines;
                }
                else
                {
                    // 記錄第二次的趨勢線
                    this.secondTrendLines.Clear();
                    this.secondTrendLines.AddRange(this.cachedTrendLines);
                    trendLinesToUse = this.secondTrendLines;
                    this.Log($"Second signal recorded: {trendLinesToUse.Count} trend lines (range: {this.recordedRangeStart} to {this.TrendLineRangeEnd})", StrategyLoggingLevel.Trading);
                }
            }
            else
            {
                // 第三次及以後：使用第二次記錄的趨勢線
                if (this.secondTrendLines.Any())
                {
                    trendLinesToUse = this.secondTrendLines;
                    this.Log($"Signal #{this.signalCount}: using second recorded trend lines ({trendLinesToUse.Count} lines)", StrategyLoggingLevel.Trading);
                }
                else
                {
                    // 如果第二次沒有記錄成功，使用第一次的
                    trendLinesToUse = this.firstTrendLines;
                    this.Log($"Signal #{this.signalCount}: using first recorded trend lines ({trendLinesToUse.Count} lines)", StrategyLoggingLevel.Trading);
                }
            }

            // 檢查是否同時與直線和弧形趨勢線交互
            bool hasStraightLineInteraction = false;
            bool hasCurveLineInteraction = false;

            string signalType = isLongSignal ? "Long" : "Short";
            string rangeType = isLongSignal ? "Low-Close range" : "High-Close range";

            foreach (var trendLine in trendLinesToUse)
            {
                if (IsBarInteractingWithTrendLine(currentBar, trendLine, isLongSignal))
                {
                    if (trendLine.IsCurve)
                    {
                        hasCurveLineInteraction = true;
                        this.Log($"{signalType} signal: Current bar {rangeType} interacts with curve trend line", StrategyLoggingLevel.Trading);
                    }
                    else
                    {
                        hasStraightLineInteraction = true;
                        this.Log($"{signalType} signal: Current bar {rangeType} interacts with straight trend line", StrategyLoggingLevel.Trading);
                    }
                }
            }

            // 改為雙重交互：只需要直線或弧形趨勢線其中一種即可
            bool hasTrendLineInteraction = hasStraightLineInteraction || hasCurveLineInteraction;

            string interactionType = "";
            if (hasStraightLineInteraction && hasCurveLineInteraction)
                interactionType = "Triple (FIBO + Straight + Curve)";
            else if (hasStraightLineInteraction)
                interactionType = "Double (FIBO + Straight)";
            else if (hasCurveLineInteraction)
                interactionType = "Double (FIBO + Curve)";
            else
                interactionType = "None";

            this.Log($"{signalType} trend line interaction: Straight={hasStraightLineInteraction}, Curve={hasCurveLineInteraction}, Result={interactionType}", StrategyLoggingLevel.Trading);

            return hasTrendLineInteraction;
        }

        /// <summary>
        /// 生成趨勢線（僅在需要時調用）
        /// </summary>
        private void UpdateTrendLineCache()
        {
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);

            // 清空緩存並重新生成趨勢線
            this.cachedTrendLines.Clear();

            this.Log($"Generating trend lines at {currentTime:yyyy-MM-dd HH:mm}", StrategyLoggingLevel.Trading);

            // 計算趨勢線範圍
            int startIndex = Math.Min(this.TrendLineRangeStart, this.historicalData.Count - 1);
            int endIndex = Math.Min(this.TrendLineRangeEnd, this.historicalData.Count - 1);

            // 生成趨勢線
            GenerateTrendLines(startIndex, endIndex);

            this.Log($"Trend lines generated: {this.cachedTrendLines.Count} lines", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 使用自定義範圍生成趨勢線（第二次信號專用）
        /// </summary>
        private void UpdateTrendLineCacheWithCustomRange(int customStartLookback, int customEndLookback)
        {
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);

            // 清空緩存並重新生成趨勢線
            this.cachedTrendLines.Clear();

            this.Log($"Generating trend lines with custom range at {currentTime:yyyy-MM-dd HH:mm}", StrategyLoggingLevel.Trading);

            // 使用自定義範圍
            int startIndex = Math.Min(customStartLookback, this.historicalData.Count - 1);
            int endIndex = Math.Min(customEndLookback, this.historicalData.Count - 1);

            this.Log($"Custom range: start={customStartLookback} (index:{startIndex}), end={customEndLookback} (index:{endIndex})", StrategyLoggingLevel.Trading);

            // 生成趨勢線
            GenerateTrendLines(startIndex, endIndex);

            this.Log($"Trend lines generated with custom range: {this.cachedTrendLines.Count} lines", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 生成趨勢線（從600根K線前到20根K線前的範圍）
        /// </summary>
        private void GenerateTrendLines(int startLookback, int endLookback)
        {
            // 找出pivot點
            var pivotPoints = FindPivotPoints(startLookback, endLookback);

            if (pivotPoints.Count < 2)
            {
                this.Log($"Not enough pivot points found: {pivotPoints.Count}", StrategyLoggingLevel.Trading);
                return;
            }

            // 生成直線趨勢線
            GenerateStraightTrendLines(pivotPoints);

            // 生成弧形趨勢線
            GenerateCurveTrendLines(pivotPoints);
        }

        /// <summary>
        /// 找出指定範圍內的pivot點（完全按照參考文件實現）
        /// </summary>
        private List<PivotPoint> FindPivotPoints(int startLookback, int endLookback)
        {
            var pivotPoints = new List<PivotPoint>();

            // 確保區間合理（轉換為正向索引）
            int startIndex = Math.Max(0, this.historicalData.Count - 1 - startLookback);
            int endIndex = Math.Max(0, this.historicalData.Count - 1 - endLookback);

            if (startIndex >= endIndex)
            {
                this.Log($"FindPivotPoints: Invalid range startIndex={startIndex}, endIndex={endIndex}", StrategyLoggingLevel.Trading);
                return pivotPoints;
            }

            // this.Log($"FindPivotPoints: Processing range [{startIndex + PIVOT_LOOKBACK}, {endIndex - PIVOT_LOOKBACK}]", StrategyLoggingLevel.Trading);

            HashSet<int> processedIndices = new HashSet<int>(); // 避免重複處理
            int totalBarsChecked = 0;
            int validBarsFound = 0;

            // 從 startIndex + PIVOT_LOOKBACK 到 endIndex - PIVOT_LOOKBACK
            for (int i = startIndex + PIVOT_LOOKBACK; i <= endIndex - PIVOT_LOOKBACK; i++)
            {
                totalBarsChecked++;
                if (processedIndices.Contains(i)) continue;
                if (i >= this.historicalData.Count) continue;

                var currentBar = (HistoryItemBar)this.historicalData[i];
                if (currentBar == null) continue;

                validBarsFound++;
                double currentHigh = currentBar.High;
                double currentLow = currentBar.Low;
                double currentOpen = currentBar.Open;
                double currentClose = currentBar.Close;
                double currentBodyHigh = Math.Max(currentOpen, currentClose);
                double currentBodyLow = Math.Min(currentOpen, currentClose);

                // 調試：記錄價格信息（僅前幾根K線）
                // if (totalBarsChecked <= 3)
                // {
                //     this.Log($"Bar {i}: H={currentHigh:F6}, L={currentLow:F6}, O={currentOpen:F6}, C={currentClose:F6}", StrategyLoggingLevel.Trading);
                //     this.Log($"Bar {i}: BodyH={currentBodyHigh:F6}, BodyL={currentBodyLow:F6}", StrategyLoggingLevel.Trading);
                // }

                // 恢復完整的6個端點檢測（保留優化的嚴格性）
                bool isHighPivot = true;
                bool isLowPivot = true;
                bool isBodyHighRightPivot = true;
                bool isBodyHighLeftPivot = true;
                bool isBodyLowRightPivot = true;
                bool isBodyLowLeftPivot = true;

                int comparedBars = 0;
                // 檢查前後 PIVOT_LOOKBACK 根K線
                for (int k = i - PIVOT_LOOKBACK; k <= i + PIVOT_LOOKBACK; k++)
                {
                    if (k == i) continue;
                    if (k < 0 || k >= this.historicalData.Count) continue;

                    var compBar = (HistoryItemBar)this.historicalData[k];
                    if (compBar == null) continue;

                    comparedBars++;

                    // 高點判斷（參考文件使用 >= 比較）
                    if (compBar.High >= currentHigh)
                    {
                        isHighPivot = false;
                    }

                    // 低點判斷（參考文件使用 <= 比較）
                    if (compBar.Low <= currentLow)
                    {
                        isLowPivot = false;
                    }

                    // 蠟燭實體高點判斷
                    double compBodyHigh = Math.Max(compBar.Open, compBar.Close);
                    if (compBodyHigh >= currentBodyHigh)
                    {
                        isBodyHighRightPivot = false;
                        isBodyHighLeftPivot = false;
                    }

                    // 蠟燭實體低點判斷
                    double compBodyLow = Math.Min(compBar.Open, compBar.Close);
                    if (compBodyLow <= currentBodyLow)
                    {
                        isBodyLowRightPivot = false;
                        isBodyLowLeftPivot = false;
                    }
                }

                // 調試：記錄pivot檢測結果（僅前幾根K線）
                // if (totalBarsChecked <= 3)
                // {
                //     this.Log($"Bar {i} pivot check: compared {comparedBars} bars", StrategyLoggingLevel.Trading);
                //     this.Log($"Bar {i} pivots: High={isHighPivot}, Low={isLowPivot}, BodyHR={isBodyHighRightPivot}, BodyHL={isBodyHighLeftPivot}, BodyLR={isBodyLowRightPivot}, BodyLL={isBodyLowLeftPivot}", StrategyLoggingLevel.Trading);
                // }

                // 添加找到的完整6個端點（按照參考文件的索引偏移）
                int pivotsAddedForThisBar = 0;
                if (isHighPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i,
                        PriceType = PriceType.High,
                        Price = currentHigh
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                if (isLowPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i,
                        PriceType = PriceType.Low,
                        Price = currentLow
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                if (isBodyHighRightPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i + 1, // BodyHighRight 是右上角的價格（參考文件偏移）
                        PriceType = PriceType.BodyHighRight,
                        Price = currentBodyHigh
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                if (isBodyHighLeftPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i, // BodyHighLeft 是左上角的價格
                        PriceType = PriceType.BodyHighLeft,
                        Price = currentBodyHigh
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                if (isBodyLowRightPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i + 1, // BodyLowRight 是右下角的價格（參考文件偏移）
                        PriceType = PriceType.BodyLowRight,
                        Price = currentBodyLow
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                if (isBodyLowLeftPivot)
                {
                    pivotPoints.Add(new PivotPoint
                    {
                        Index = i, // BodyLowLeft 是左下角的價格
                        PriceType = PriceType.BodyLowLeft,
                        Price = currentBodyLow
                    });
                    processedIndices.Add(i);
                    pivotsAddedForThisBar++;
                }

                // 調試：記錄添加的pivot點（僅前幾根K線）
                // if (totalBarsChecked <= 3 && pivotsAddedForThisBar > 0)
                // {
                //     this.Log($"Bar {i}: Added {pivotsAddedForThisBar} pivot points", StrategyLoggingLevel.Trading);
                // }
            }

            this.Log($"FindPivotPoints completed: checked {totalBarsChecked} bars, found {validBarsFound} valid bars, created {pivotPoints.Count} pivot points", StrategyLoggingLevel.Trading);

            // 依 Index 排序
            return pivotPoints.OrderBy(p => p.Index).ToList();
        }

        /// <summary>
        /// 生成直線趨勢線（使用完整的候選者評分系統）
        /// </summary>
        private void GenerateStraightTrendLines(List<PivotPoint> pivotPoints)
        {
            var candidates = new List<TrendLineCandidate>();

            // 生成所有候選線
            for (int i = 0; i < pivotPoints.Count - 1; i++)
            {
                for (int j = i + 1; j < pivotPoints.Count; j++)
                {
                    var candidate = CreateCandidateLine(pivotPoints[i], pivotPoints[j], false);
                    if (candidate != null)
                    {
                        double score = ScoreCandidate(candidate);
                        candidate.Score = score;
                        if (score > 0)
                        {
                            candidates.Add(candidate);
                        }
                    }
                }
            }

            // 選擇最佳候選線（參考文件：顯示範圍線最大200條）
            var bestCandidates = candidates
                .OrderByDescending(c => c.Score)
                .Take(200) // 參考MAX_RANGE_LINES = 200
                .Where(c => c.Score > 30) // 降低分數門檻以獲得更多線條
                .ToList();

            // 轉換為TrendLineData
            foreach (var candidate in bestCandidates)
            {
                double slope = (candidate.EndPoint.Price - candidate.StartPoint.Price) /
                              (candidate.EndPoint.Index - candidate.StartPoint.Index);
                double intercept = candidate.StartPoint.Price - slope * candidate.StartPoint.Index;

                var trendLine = new TrendLineData
                {
                    StartTime = this.historicalData[candidate.StartPoint.Index].TimeLeft,
                    EndTime = this.historicalData[candidate.EndPoint.Index].TimeLeft,
                    Slope = slope,
                    Intercept = intercept,
                    IsCurve = false,
                    StartIndex = candidate.StartPoint.Index,
                    EndIndex = candidate.EndPoint.Index
                };

                this.cachedTrendLines.Add(trendLine);
            }

            this.Log($"Generated {bestCandidates.Count} straight trend lines from {candidates.Count} candidates", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 生成弧形趨勢線（使用完整的候選者評分系統）
        /// </summary>
        private void GenerateCurveTrendLines(List<PivotPoint> pivotPoints)
        {
            const double FIBONACCI_RATIO = 0.618033988749895;
            var candidates = new List<TrendLineCandidate>();

            // 生成所有弧形候選線
            for (int i = 0; i < pivotPoints.Count - 1; i++)
            {
                for (int j = i + 1; j < pivotPoints.Count; j++)
                {
                    var candidate = CreateCandidateLine(pivotPoints[i], pivotPoints[j], true);
                    if (candidate != null)
                    {
                        double score = ScoreCandidate(candidate);
                        candidate.Score = score;
                        if (score > 0)
                        {
                            candidates.Add(candidate);
                        }
                    }
                }
            }

            // 選擇最佳弧形候選線（參考文件：顯示範圍線最大200條）
            var bestCandidates = candidates
                .OrderByDescending(c => c.Score)
                .Take(200) // 參考MAX_RANGE_LINES = 200
                .Where(c => c.Score > 30) // 降低分數門檻以獲得更多線條
                .ToList();

            // 轉換為TrendLineData（創建多個斐波那契比例的弧線）
            foreach (var candidate in bestCandidates)
            {
                // 創建兩個版本的弧線：FIBONACCI_RATIO 和 GOLDEN_RATIO
                var fibonacciRatios = new[] { FIBONACCI_RATIO, GOLDEN_RATIO };

                foreach (var ratio in fibonacciRatios)
                {
                    var curveTrendLine = new TrendLineData
                    {
                        StartTime = this.historicalData[candidate.StartPoint.Index].TimeLeft,
                        EndTime = this.historicalData[candidate.EndPoint.Index].TimeLeft,
                        IsCurve = true,
                        StartIndex = candidate.StartPoint.Index,
                        EndIndex = candidate.EndPoint.Index
                    };

                    // 計算斐波那契弧線係數
                    CalculateFibonacciArcCoefficients(candidate.StartPoint, candidate.EndPoint, curveTrendLine, ratio);

                    this.cachedTrendLines.Add(curveTrendLine);
                }
            }

            this.Log($"Generated {bestCandidates.Count} curve trend lines from {candidates.Count} candidates", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 檢查直線趨勢線是否有效
        /// </summary>
        private bool IsValidTrendLine(PivotPoint p1, PivotPoint p2, double slope)
        {
            // 檢查時間跨度
            int timeSpan = p2.Index - p1.Index;
            if (timeSpan < 5 || timeSpan > 200) return false;

            // 檢查角度
            double angle = Math.Atan(Math.Abs(slope)) * 180.0 / Math.PI;
            if (angle < 5 || angle > 75) return false;

            return true;
        }

        /// <summary>
        /// 檢查弧形趨勢線是否有效
        /// </summary>
        private bool IsValidCurveTrendLine(PivotPoint p1, PivotPoint p2, double curvature, double linearCoeff, double constantTerm)
        {
            // 檢查時間跨度
            int timeSpan = p2.Index - p1.Index;
            if (timeSpan < 10 || timeSpan > 200) return false;

            // 檢查曲率是否合理
            if (Math.Abs(curvature) > 0.01) return false;

            return true;
        }

        /// <summary>
        /// 檢查K線是否與趨勢線交互（根據交易方向使用不同的包含判定）
        /// 參考TrendLineSelection_Mix.cs的Nearest功能，但使用價格距離而非像素距離
        /// </summary>
        private bool IsBarInteractingWithTrendLine(HistoryItemBar bar, TrendLineData trendLine, bool isLongSignal)
        {
            // 獲取當前K線在歷史數據中的索引
            int currentIndex = 0; // 當前K線索引為0

            // 計算趨勢線在當前位置的價格
            double linePrice = CalculateTrendLinePrice(trendLine, currentIndex);

            // 根據交易方向使用不同的包含判定
            bool isContained;
            string rangeDescription;

            if (isLongSignal)
            {
                // 做多：檢查Low和Close之間是否包含趨勢線
                double rangeLow = Math.Min(bar.Low, bar.Close);
                double rangeHigh = Math.Max(bar.Low, bar.Close);

                isContained = linePrice >= rangeLow && linePrice <= rangeHigh;
                rangeDescription = $"Low-Close=[{rangeLow:F5}, {rangeHigh:F5}]";
            }
            else
            {
                // 做空：檢查High和Close之間是否包含趨勢線
                double rangeLow = Math.Min(bar.High, bar.Close);
                double rangeHigh = Math.Max(bar.High, bar.Close);

                isContained = linePrice >= rangeLow && linePrice <= rangeHigh;
                rangeDescription = $"High-Close=[{rangeLow:F5}, {rangeHigh:F5}]";
            }

            // 如果基本包含檢查失敗，使用Nearest風格的距離檢測
            if (!isContained)
            {
                double priceDistance = GetPriceDistanceToTrendLine(bar, trendLine, currentIndex, isLongSignal);
                double avgPrice = (bar.High + bar.Low) / 2.0;
                double toleranceRatio = GetDynamicTouchThreshold(avgPrice); // 使用動態閾值

                // 如果價格距離在容忍範圍內，視為交互
                if (priceDistance <= avgPrice * toleranceRatio)
                {
                    isContained = true;
                    rangeDescription += $" (Nearest: distance={priceDistance:F5}, tolerance={toleranceRatio:F6})";
                }
            }

            if (isContained)
            {
                string lineType = trendLine.IsCurve ? "curve" : "straight";
                string signalType = isLongSignal ? "Long" : "Short";
                this.Log($"{signalType} signal: Bar {rangeDescription} contains {lineType} trend line: linePrice={linePrice:F5}", StrategyLoggingLevel.Trading);
            }

            return isContained;
        }

        /// <summary>
        /// 計算趨勢線在指定索引處的價格（參考TrendLineSelection_Mix.cs的CalculateLinePrice）
        /// </summary>
        private double CalculateTrendLinePrice(TrendLineData trendLine, int index)
        {
            if (trendLine.IsCurve)
            {
                // 弧形趨勢線：使用二次方程（參考CalculateArcPrice）
                var startIndex = trendLine.StartIndex;
                double x = index - startIndex;

                return trendLine.CurvatureCoefficient * x * x +
                       trendLine.LinearCoefficient * x +
                       trendLine.ConstantTerm;
            }
            else
            {
                // 直線趨勢線：使用線性方程
                return trendLine.Slope * index + trendLine.Intercept;
            }
        }

        /// <summary>
        /// 計算K線到趨勢線的價格距離（參考TrendLineSelection_Mix.cs的GetDistanceToLine概念）
        /// </summary>
        private double GetPriceDistanceToTrendLine(HistoryItemBar bar, TrendLineData trendLine, int currentIndex, bool isLongSignal)
        {
            double linePrice = CalculateTrendLinePrice(trendLine, currentIndex);

            // 根據交易方向計算到相關價格範圍的最小距離
            if (isLongSignal)
            {
                // 做多：計算到Low-Close範圍的距離
                double rangeLow = Math.Min(bar.Low, bar.Close);
                double rangeHigh = Math.Max(bar.Low, bar.Close);

                if (linePrice >= rangeLow && linePrice <= rangeHigh)
                    return 0; // 在範圍內，距離為0
                else if (linePrice < rangeLow)
                    return rangeLow - linePrice; // 在範圍下方
                else
                    return linePrice - rangeHigh; // 在範圍上方
            }
            else
            {
                // 做空：計算到High-Close範圍的距離
                double rangeLow = Math.Min(bar.High, bar.Close);
                double rangeHigh = Math.Max(bar.High, bar.Close);

                if (linePrice >= rangeLow && linePrice <= rangeHigh)
                    return 0; // 在範圍內，距離為0
                else if (linePrice < rangeLow)
                    return rangeLow - linePrice; // 在範圍下方
                else
                    return linePrice - rangeHigh; // 在範圍上方
            }
        }

        /// <summary>
        /// 創建趨勢線候選者（完全按照參考文件實現）
        /// </summary>
        private TrendLineCandidate CreateCandidateLine(PivotPoint p1, PivotPoint p2, bool isCurve)
        {
            int timeSpan = p2.Index - p1.Index;

            // 檢查時間跨度（參考文件標準）
            if (timeSpan < MIN_TIME_SPAN || timeSpan > MAX_TIME_SPAN)
            {
                // this.Log($"CreateCandidateLine: timeSpan {timeSpan} out of range [{MIN_TIME_SPAN}, {MAX_TIME_SPAN}]", StrategyLoggingLevel.Trading);
                return null;
            }

            // 計算端點類型權重，平衡6端點的多樣性
            double endpointWeight = GetEndpointTypeWeight(p1.PriceType, p2.PriceType);
            if (endpointWeight < 0.6)  // 適中的權重門檻，保持6端點的有效性
            {
                // this.Log($"CreateCandidateLine: endpointWeight {endpointWeight:F3} too low for {p1.PriceType}-{p2.PriceType}", StrategyLoggingLevel.Trading);
                return null;
            }

            // 計算斜率（仍需要用於線性方程）
            double slope = (p2.Price - p1.Price) / timeSpan;

            // 檢查是否為有意義的趨勢線（避免幾乎水平的線）
            double priceRange = Math.Abs(p2.Price - p1.Price);
            double avgPrice = (p1.Price + p2.Price) / 2;
            double priceChangeRatio = priceRange / avgPrice;
            // 註釋掉過嚴格的檢查，適應大價格資產如比特幣
            // if (priceChangeRatio < 0.001) return null;

            // 計算截距
            double intercept = p1.Price - slope * p1.Index;

            // 收集「貼線」的 pivot index（或 K 線）
            List<int> touchedIndices = new List<int>();
            int bodyCrossCount = 0;

            // 優化的觸點檢測邏輯
            int leftExtendIndex = Math.Max(0, p1.Index - 200); // 減少延伸範圍
            List<int> qualityTouches = new List<int>(); // 高品質觸點

            // 計算動態閾值
            double avgPrice2 = (p1.Price + p2.Price) / 2;
            double dynamicThresholdBase = GetDynamicTouchThreshold(avgPrice2);
            double dynamicThresholdSameType = dynamicThresholdBase * 1.5;

            // this.Log($"Touch detection: avgPrice={avgPrice2:F6}, dynamicThresholdBase={dynamicThresholdBase:F6}, dynamicThresholdSameType={dynamicThresholdSameType:F6}", StrategyLoggingLevel.Trading);

            for (int i = leftExtendIndex; i <= p2.Index; i++)
            {
                if (i >= this.historicalData.Count) continue;

                double linePrice = slope * i + intercept;
                var bar = (HistoryItemBar)this.historicalData[i];
                if (bar == null) continue;

                double barHigh = bar.High;
                double barLow = bar.Low;
                double bodyHigh = Math.Max(bar.Open, bar.Close);
                double bodyLow = Math.Min(bar.Open, bar.Close);

                // 優先檢查主要價格類型（High/Low）
                bool isHighQualityTouch = false;
                bool isTouch = false;

                // 檢查High/Low觸點（高品質）- 使用動態閾值
                if (CheckPriceTouch(barHigh, linePrice, dynamicThresholdBase) ||
                    CheckPriceTouch(barLow, linePrice, dynamicThresholdBase))
                {
                    isHighQualityTouch = true;
                    isTouch = true;
                    // this.Log($"Touch detected at bar {i} (High/Low touch)", StrategyLoggingLevel.Trading);
                }

                // 檢查實體端點（中等品質）- 使用動態閾值
                if (!isTouch)
                {
                    if (CheckPriceTouch(bodyHigh, linePrice, dynamicThresholdSameType) ||
                        CheckPriceTouch(bodyLow, linePrice, dynamicThresholdSameType))
                    {
                        isTouch = true;
                        // this.Log($"Touch detected at bar {i} (body touch)", StrategyLoggingLevel.Trading);
                    }
                }

                if (isTouch)
                {
                    touchedIndices.Add(i);
                    if (isHighQualityTouch)
                    {
                        qualityTouches.Add(i);
                    }
                }

                // 檢查是否穿過蠟燭實體（更嚴格）
                if (linePrice > bodyLow + (bodyHigh - bodyLow) * 0.1 &&
                    linePrice < bodyHigh - (bodyHigh - bodyLow) * 0.1)
                {
                    bodyCrossCount++;
                }
            }

            // 計算觸點品質分數
            double touchQualityScore = qualityTouches.Count * 2.0 + (touchedIndices.Count - qualityTouches.Count);

            // 最少觸點要求
            if (touchedIndices.Count < MIN_TOUCH_POINTS) return null;

            // 實體穿越檢查
            if (bodyCrossCount > MIN_BODY_CROSS_TOLERANCE) return null;

            return new TrendLineCandidate
            {
                StartPoint = p1,
                EndPoint = p2,
                TouchIndices = touchedIndices,
                CrossedBodies = bodyCrossCount,
                EndpointTypeWeight = endpointWeight,
                TouchQualityScore = touchQualityScore  // 新增觸點品質分數
            };
        }

        /// <summary>
        /// 檢查價格觸點（完全按照參考文件實現）
        /// </summary>
        private bool CheckPriceTouch(double price, double linePrice, double threshold)
        {
            if (price == 0 || linePrice == 0)
            {
                this.Log($"CheckPriceTouch: Invalid price (price={price:F6}, linePrice={linePrice:F6})", StrategyLoggingLevel.Trading);
                return false;
            }

            double diffPct = Math.Abs(price - linePrice) / Math.Max(price, linePrice);
            bool isTouch = diffPct <= threshold;

            // 調試：記錄觸點檢測詳情（限制日誌數量）
            // if (isTouch)
            // {
            //     this.Log($"CheckPriceTouch: price={price:F6}, linePrice={linePrice:F6}, diffPct={diffPct:F6}, threshold={threshold:F6}, isTouch={isTouch}", StrategyLoggingLevel.Trading);
            // }

            return isTouch;
        }

        /// <summary>
        /// 計算端點類型權重（完全按照參考文件實現）
        /// </summary>
        private double GetEndpointTypeWeight(PriceType type1, PriceType type2)
        {
            // 定義價格類型的優先級和族群
            bool isType1Primary = (type1 == PriceType.High || type1 == PriceType.Low);
            bool isType2Primary = (type2 == PriceType.High || type2 == PriceType.Low);

            bool isType1High = (type1 == PriceType.High || type1 == PriceType.BodyHighLeft || type1 == PriceType.BodyHighRight);
            bool isType2High = (type2 == PriceType.High || type2 == PriceType.BodyHighLeft || type2 == PriceType.BodyHighRight);
            bool isType1Low = (type1 == PriceType.Low || type1 == PriceType.BodyLowLeft || type1 == PriceType.BodyLowRight);
            bool isType2Low = (type2 == PriceType.Low || type2 == PriceType.BodyLowLeft || type2 == PriceType.BodyLowRight);

            // 1. 兩個都是主要端點（High/Low）- 最高權重
            if (isType1Primary && isType2Primary)
            {
                // 同類型連線（High-High 或 Low-Low）
                if ((isType1High && isType2High) || (isType1Low && isType2Low))
                    return 1.0;
                // 異類型連線（High-Low）
                else
                    return 0.95;
            }

            // 2. 一個主要端點，一個實體端點
            if (isType1Primary || isType2Primary)
            {
                // 同族群連線（High系列 或 Low系列）
                if ((isType1High && isType2High) || (isType1Low && isType2Low))
                    return 0.85;
                // 異族群連線
                else
                    return 0.75;
            }

            // 3. 兩個都是實體端點
            // 同族群連線（High系列 或 Low系列）
            if ((isType1High && isType2High) || (isType1Low && isType2Low))
                return 0.7;
            // 異族群連線
            else
                return 0.6;
        }

        /// <summary>
        /// 評分候選趨勢線（完全按照參考文件實現）
        /// </summary>
        private double ScoreCandidate(TrendLineCandidate candidate)
        {
            int touchCount = candidate.TouchIndices.Count;

            // 最少觸點要求
            if (touchCount < MIN_TOUCH_POINTS) return 0;

            // 實體穿越檢查
            if (candidate.CrossedBodies > MIN_BODY_CROSS_TOLERANCE) return 0;

            // 計算各項分數
            int timeSpan = candidate.EndPoint.Index - candidate.StartPoint.Index;

            // 1. 觸點分數（使用品質分數）
            double touchScore = candidate.TouchQualityScore * 8.0; // 高品質觸點權重更高

            // 2. 時間跨度分數（參考文件邏輯）
            double timeSpanScore = CalculateTimeSpanScore(timeSpan);

            // 3. 角度分數（參考文件角度範圍）
            double slope = (candidate.EndPoint.Price - candidate.StartPoint.Price) / timeSpan;
            double angle = Math.Atan(Math.Abs(slope)) * 180.0 / Math.PI;
            double angleScore = CalculateAngleScore(angle);

            // 4. 觸點分佈分數
            double distributionScore = CalculateTouchDistributionScore(candidate.TouchIndices,
                candidate.StartPoint.Index, candidate.EndPoint.Index);

            // 5. 端點類型分數（參考文件權重系統）
            double endpointScore = candidate.EndpointTypeWeight * 20.0;

            // 6. 實體穿越懲罰
            double bodyCrossPenalty = candidate.CrossedBodies * -10.0;

            // 基礎分數
            double baseScore = touchScore + timeSpanScore + angleScore + distributionScore + endpointScore + bodyCrossPenalty;

            // 應用端點權重
            double finalScore = baseScore * candidate.EndpointTypeWeight;

            // 確保分數不為負數
            finalScore = Math.Max(0, finalScore);

            // this.Log($"ScoreCandidate: touchScore={touchScore:F2}, timeSpanScore={timeSpanScore:F2}, angleScore={angleScore:F2}, distributionScore={distributionScore:F2}, endpointScore={endpointScore:F2}, bodyCrossPenalty={bodyCrossPenalty:F2}, finalScore={finalScore:F2}", StrategyLoggingLevel.Trading);

            return finalScore;
        }

        /// <summary>
        /// 評分候選趨勢線（範圍線專用重載方法）
        /// </summary>
        private double ScoreCandidate(TrendLineCandidate candidate, bool isRangeLine)
        {
            if (isRangeLine)
            {
                // 範圍線使用更嚴格的標準
                int touchCount = candidate.TouchIndices.Count;

                // 範圍線最少觸點要求
                if (touchCount < MIN_TOUCH_POINTS_RANGE) return 0;

                // 實體穿越檢查
                if (candidate.CrossedBodies > MIN_BODY_CROSS_TOLERANCE) return 0;

                // 使用更高的權重計算
                double touchScore = candidate.TouchQualityScore * 10.0; // 範圍線觸點權重更高
                double endpointScore = candidate.EndpointTypeWeight * 25.0; // 端點權重更高
                double bodyCrossPenalty = candidate.CrossedBodies * -15.0; // 更嚴格的懲罰

                int timeSpan = candidate.EndPoint.Index - candidate.StartPoint.Index;
                double timeSpanScore = CalculateTimeSpanScore(timeSpan);

                double slope = (candidate.EndPoint.Price - candidate.StartPoint.Price) / timeSpan;
                double angle = Math.Atan(Math.Abs(slope)) * 180.0 / Math.PI;
                double angleScore = CalculateAngleScore(angle);

                double distributionScore = CalculateTouchDistributionScore(candidate.TouchIndices,
                    candidate.StartPoint.Index, candidate.EndPoint.Index);

                double baseScore = touchScore + timeSpanScore + angleScore + distributionScore + endpointScore + bodyCrossPenalty;
                double finalScore = Math.Max(0, baseScore * candidate.EndpointTypeWeight);

                // this.Log($"ScoreCandidate (Range): touchScore={touchScore:F2}, endpointScore={endpointScore:F2}, finalScore={finalScore:F2}", StrategyLoggingLevel.Trading);
                return finalScore;
            }
            else
            {
                // 普通趨勢線使用標準評分
                return ScoreCandidate(candidate);
            }
        }

        /// <summary>
        /// 計算時間跨度分數
        /// </summary>
        private double CalculateTimeSpanScore(int timeSpan)
        {
            if (timeSpan >= 15 && timeSpan <= 30) return 20.0;
            else if (timeSpan >= 10 && timeSpan <= 45) return 15.0;
            else if (timeSpan >= 5 && timeSpan <= 60) return 10.0;
            else return 5.0;
        }

        /// <summary>
        /// 計算角度分數（參考文件角度範圍：21.0-61.8度）
        /// </summary>
        private double CalculateAngleScore(double absAngle)
        {
            // 參考文件的角度範圍檢查
            if (absAngle < MIN_ABS_ANGLE || absAngle > MAX_ABS_ANGLE)
            {
                return 0.0; // 超出範圍的角度得0分
            }

            // 最佳角度範圍（30-50度）
            if (absAngle >= 30 && absAngle <= 50) return 20.0;
            // 良好角度範圍（25-55度）
            else if (absAngle >= 25 && absAngle <= 55) return 15.0;
            // 可接受角度範圍（21-61.8度）
            else if (absAngle >= MIN_ABS_ANGLE && absAngle <= MAX_ABS_ANGLE) return 10.0;
            else return 0.0;
        }

        /// <summary>
        /// 計算觸點分佈分數
        /// </summary>
        private double CalculateTouchDistributionScore(List<int> touchIndices, int startIndex, int endIndex)
        {
            if (touchIndices.Count < 2) return 0;

            // 計算觸點在時間軸上的分佈均勻度
            var sortedTouches = touchIndices.OrderBy(x => x).ToList();
            double totalSpan = endIndex - startIndex;
            double avgGap = totalSpan / (sortedTouches.Count - 1);

            double variance = 0;
            for (int i = 1; i < sortedTouches.Count; i++)
            {
                double gap = sortedTouches[i] - sortedTouches[i - 1];
                variance += Math.Pow(gap - avgGap, 2);
            }
            variance /= (sortedTouches.Count - 1);

            // 分佈越均勻，分數越高
            double distributionScore = Math.Max(0, 10.0 - variance / avgGap);
            return distributionScore;
        }

        /// <summary>
        /// 計算斐波那契弧線的二次方程係數（完全按照參考文件實現）
        /// 弧線方程: price = a * (index - startIndex)^2 + b * (index - startIndex) + c
        /// </summary>
        private void CalculateFibonacciArcCoefficients(PivotPoint start, PivotPoint end, TrendLineData trendLine, double fibonacciRatio = 0.618033988749895)
        {
            try
            {
                // 計算基本參數
                double deltaIndex = end.Index - start.Index;
                double deltaPrice = end.Price - start.Price;

                if (deltaIndex == 0)
                {
                    // 退化為直線
                    trendLine.IsCurve = false;
                    return;
                }

                // 使用傳入的斐波那契比例計算弧線的曲率 - 增加弧度強度
                // 在中點處，弧線偏離直線的距離為總價格變化的斐波那契比例
                double midIndex = deltaIndex * fibonacciRatio; // 斐波那契分割點
                double straightLinePrice = start.Price + (deltaPrice * fibonacciRatio); // 直線在該點的價格

                // 計算弧線在黃金分割點的偏移量（適度的美感弧度）
                // 根據價格範圍調整，保持自然的弧線效果
                double priceRange = Math.Abs(deltaPrice);
                double timeSpan = deltaIndex;

                // 大幅減小弧度：基於價格變化的2-8%，讓弧線更溫和
                double curvatureRatio = Math.Max(0.02, Math.Min(0.08, timeSpan / 500.0)); // 2%-8%的弧度
                double curvatureOffset = priceRange * curvatureRatio;

                // 關鍵改進：讓弧線延長線往右趨向水平
                bool isUpTrend = deltaPrice > 0;

                // 調整弧線中點，使用更小的弧度讓終點更容易趨向水平
                // 重點：弧度要足夠小，讓終點斜率能夠接近0
                double arcMidPrice;
                if (isUpTrend)
                {
                    // 上升趨勢：弧線輕微向上彎曲，減少弧度強度
                    arcMidPrice = straightLinePrice + curvatureOffset * 0.3; // 從0.8降到0.3
                }
                else
                {
                    // 下降趨勢：弧線輕微向下彎曲，減少弧度強度
                    arcMidPrice = straightLinePrice - curvatureOffset * 0.3; // 從0.8降到0.3
                }

                // 設定三個點來計算二次方程
                // 點1: 起點 (0, start.Price)
                // 點2: 黃金分割點 (midIndex, arcMidPrice)
                // 點3: 終點 (deltaIndex, end.Price)
                double x1 = 0;
                double y1 = start.Price;
                double x2 = midIndex;
                double y2 = arcMidPrice;
                double x3 = deltaIndex;
                double y3 = end.Price;

                // 解二次方程組 y = ax^2 + bx + c
                // 使用三點式求解
                double denominator = (x1 - x2) * (x1 - x3) * (x2 - x3);
                if (Math.Abs(denominator) < 1e-10)
                {
                    // 三點共線，退化為直線
                    trendLine.IsCurve = false;
                    double slope = (end.Price - start.Price) / deltaIndex;
                    trendLine.Slope = slope;
                    trendLine.Intercept = start.Price - slope * start.Index;
                    return;
                }

                double a = (x3 * (y2 - y1) + x2 * (y1 - y3) + x1 * (y3 - y2)) / denominator;
                double b = (x3 * x3 * (y1 - y2) + x2 * x2 * (y3 - y1) + x1 * x1 * (y2 - y3)) / denominator;
                double c = (x2 * x3 * (x2 - x3) * y1 + x3 * x1 * (x3 - x1) * y2 + x1 * x2 * (x1 - x2) * y3) / denominator;

                trendLine.CurvatureCoefficient = a;
                trendLine.LinearCoefficient = b;
                trendLine.ConstantTerm = c;

                // this.Log($"Arc coefficients: a={trendLine.CurvatureCoefficient:F6}, b={trendLine.LinearCoefficient:F6}, c={trendLine.ConstantTerm:F6}, ratio={fibonacciRatio:F6}", StrategyLoggingLevel.Trading);
            }
            catch (Exception ex)
            {
                this.Log($"CalculateFibonacciArcCoefficients error: {ex.Message}", StrategyLoggingLevel.Trading);
                trendLine.IsCurve = false; // 降級為直線
            }
        }

        /// <summary>
        /// 手動重置當日趨勢線記錄（可選功能）
        /// </summary>
        private void ResetDailyTrendLines()
        {
            this.firstTrendLines.Clear();
            this.secondTrendLines.Clear();
            this.signalCount = 0;
            this.recordedRangeStart = -1;
            this.lastSignalDate = DateTime.MinValue;
            this.Log("Daily trend lines manually reset", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 獲取當日趨勢線統計信息
        /// </summary>
        private string GetDailyTrendLineStats()
        {
            if (this.signalCount == 0)
            {
                return "No signals recorded today";
            }

            string stats = $"Signal count: {this.signalCount}, Range start: {this.recordedRangeStart}";

            if (this.firstTrendLines.Any())
            {
                int firstStraight = this.firstTrendLines.Count(t => !t.IsCurve);
                int firstCurve = this.firstTrendLines.Count(t => t.IsCurve);
                stats += $", First: {firstStraight}+{firstCurve}={this.firstTrendLines.Count}";
            }

            if (this.secondTrendLines.Any())
            {
                int secondStraight = this.secondTrendLines.Count(t => !t.IsCurve);
                int secondCurve = this.secondTrendLines.Count(t => t.IsCurve);
                stats += $", Second: {secondStraight}+{secondCurve}={this.secondTrendLines.Count}";
            }

            stats += $" (Date: {this.lastSignalDate:yyyy-MM-dd})";
            return stats;
        }

        /// <summary>
        /// 檢查每日交易次數限制（增強版本）
        /// </summary>
        private bool CheckDailyTradeLimit()
        {
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);
            DateTime currentDate = currentTime.Date;

            // 如果是新的一天，重置交易次數
            if (currentDate != this.lastTradeDate.Date)
            {
                int oldCount = this.dailyTradeCount;
                this.dailyTradeCount = 0;
                this.lastTradeDate = currentTime;
                this.Log($"新交易日: {currentDate:yyyy-MM-dd}，重置每日交易次數 {oldCount} -> 0", StrategyLoggingLevel.Trading);
            }

            // 檢查是否超過每日交易限制
            bool canTrade = this.dailyTradeCount < this.MaxDailyTrades;

            if (!canTrade)
            {
                this.Log($"達到每日交易限制: {this.dailyTradeCount}/{this.MaxDailyTrades}，當前時間: {currentTime:yyyy-MM-dd HH:mm:ss}", StrategyLoggingLevel.Trading);
            }
            else
            {
                this.Log($"每日交易次數檢查通過: {this.dailyTradeCount}/{this.MaxDailyTrades}，當前時間: {currentTime:yyyy-MM-dd HH:mm:ss}", StrategyLoggingLevel.Trading);
            }

            return canTrade;
        }

        /// <summary>
        /// 增加每日交易次數
        /// </summary>
        private void IncrementDailyTradeCount()
        {
            this.dailyTradeCount++;
            this.Log($"Daily trade count incremented: {this.dailyTradeCount}/{this.MaxDailyTrades}", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 獲取每日交易統計信息
        /// </summary>
        private string GetDailyTradeStats()
        {
            return $"Daily trades: {this.dailyTradeCount}/{this.MaxDailyTrades} (Date: {this.lastTradeDate:yyyy-MM-dd})";
        }

        #endregion

        #region 狀態變量復位和檢查方法

        /// <summary>
        /// 重置過期的等待標誌，防止策略卡住不下單
        /// </summary>
        private void ResetStaleWaitFlags()
        {
            var existingPositions = Core.Instance.Positions.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount).ToArray();
            var pendingOrders = Core.Instance.Orders.Where(x => x.Symbol == this.CurrentSymbol && x.Account == this.CurrentAccount && x.Status == OrderStatus.Opened).ToArray();

            // 檢查waitingForOrder超時
            if (this.waitingForOrder)
            {
                DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                    this.historicalData[0].TimeLeft,
                    Core.Instance.TimeUtils.SelectedTimeZone);

                // 如果waitingForOrder超時，強制重置
                if (this.waitingForOrderStartTime != DateTime.MinValue &&
                    (currentTime - this.waitingForOrderStartTime).TotalSeconds > WAITING_FOR_ORDER_TIMEOUT_SECONDS)
                {
                    this.Log($"waitingForOrder超時重置：已等待{(currentTime - this.waitingForOrderStartTime).TotalSeconds:F1}秒", StrategyLoggingLevel.Trading);
                    this.waitingForOrder = false;
                    this.waitOpenPosition = false;
                    this.waitingForOrderStartTime = DateTime.MinValue;
                }
                // 如果沒有掛單也沒有持倉，但還在等待，則重置
                else if (!pendingOrders.Any() && !existingPositions.Any())
                {
                    this.Log("重置waitingForOrder標誌：沒有掛單和持倉", StrategyLoggingLevel.Trading);
                    this.waitingForOrder = false;
                    this.waitOpenPosition = false;
                    this.waitingForOrderStartTime = DateTime.MinValue;
                }
                // 如果有持倉，說明下單成功了，重置等待標誌
                else if (existingPositions.Any())
                {
                    this.Log("重置waitingForOrder標誌：已有持倉，下單成功", StrategyLoggingLevel.Trading);
                    this.waitingForOrder = false;
                    this.waitOpenPosition = false;
                    this.waitingForOrderStartTime = DateTime.MinValue;
                }
            }

            // 如果沒有持倉也沒有掛單，但waitClosePositions還是true，則重置它
            if (!existingPositions.Any() && !pendingOrders.Any() && this.waitClosePositions)
            {
                this.Log("重置waitClosePositions標誌：沒有持倉和掛單", StrategyLoggingLevel.Trading);
                this.waitClosePositions = false;
            }

            // 如果有持倉，但waitOpenPosition還是true，則重置它
            if (existingPositions.Any() && this.waitOpenPosition)
            {
                this.Log("重置waitOpenPosition標誌：已有持倉", StrategyLoggingLevel.Trading);
                this.waitOpenPosition = false;
            }

            // 檢查每日交易次數是否需要重置
            CheckAndResetDailyTradeCount();
        }

        /// <summary>
        /// 檢查並重置每日交易次數（增強版本）
        /// </summary>
        private void CheckAndResetDailyTradeCount()
        {
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                this.historicalData[0].TimeLeft,
                Core.Instance.TimeUtils.SelectedTimeZone);
            DateTime currentDate = currentTime.Date;

            // 如果是新的一天，重置交易次數
            if (currentDate != this.lastTradeDate.Date)
            {
                int oldCount = this.dailyTradeCount;
                this.dailyTradeCount = 0;
                this.lastTradeDate = currentTime;
                this.Log($"新交易日開始: {currentDate:yyyy-MM-dd}，重置每日交易次數 {oldCount} -> 0", StrategyLoggingLevel.Trading);
            }

            // 每小時記錄一次當前狀態（用於調試）
            if (currentTime.Minute == 0)
            {
                this.Log($"當前狀態檢查 - 日期: {currentDate:yyyy-MM-dd}, 交易次數: {this.dailyTradeCount}/{this.MaxDailyTrades}, " +
                        $"waitingForOrder: {this.waitingForOrder}, waitOpenPosition: {this.waitOpenPosition}, waitClosePositions: {this.waitClosePositions}",
                        StrategyLoggingLevel.Trading);
            }
        }

        /// <summary>
        /// 強制重置所有等待標誌（緊急復位方法）
        /// </summary>
        private void ForceResetAllWaitFlags()
        {
            this.Log("執行強制重置所有等待標誌", StrategyLoggingLevel.Trading);
            this.waitingForOrder = false;
            this.waitOpenPosition = false;
            this.waitClosePositions = false;
            this.waitingForOrderStartTime = DateTime.MinValue;
        }

        /// <summary>
        /// 初始化策略狀態變量
        /// </summary>
        private void InitializeStrategyState()
        {
            this.Log("初始化策略狀態變量", StrategyLoggingLevel.Trading);

            // 重置所有等待標誌
            this.waitingForOrder = false;
            this.waitOpenPosition = false;
            this.waitClosePositions = false;
            this.waitingForOrderStartTime = DateTime.MinValue;

            // 重置每日交易計數
            DateTime currentTime = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(
                DateTime.UtcNow, Core.Instance.TimeUtils.SelectedTimeZone);
            this.lastTradeDate = currentTime.Date;
            this.dailyTradeCount = 0;

            // 重置VAH/VAL變量
            this.vah = 0;
            this.val = 0;
            this.poc = 0;

            // 清空緩存
            this.cachedFiboLevels.Clear();
            this.buySellSignals.Clear();
            this.cachedTrendLines.Clear();
            this.firstTrendLines.Clear();
            this.secondTrendLines.Clear();

            // 重置其他狀態變量
            this.lastFiboCacheTime = DateTime.MinValue;
            this.lastSignalCheckDate = DateTime.MinValue;
            this.lastSignalDate = DateTime.MinValue;
            this.signalCount = 0;
            this.recordedRangeStart = -1;

            this.Log($"策略狀態初始化完成 - 當前時間: {currentTime:yyyy-MM-dd HH:mm:ss}, 每日交易限制: {this.MaxDailyTrades}", StrategyLoggingLevel.Trading);
        }

        #endregion

        #region VAH/VAL計算和FIBO篩選方法（參考Selected_Range_Lines_Indicator.cs的FiboType2）

        /// <summary>
        /// 根據VAH和VAL篩選FIBO位置，只保留VAH到VAL範圍外的FIBO位置
        /// </summary>
        private FiboLevelsData FilterFiboLevelsByVAHVAL(FiboLevelsData originalFiboData, int lookbackBars)
        {
            try
            {
                // 計算VAH和VAL（使用與Selected_Range_Lines_Indicator.cs相同的邏輯）
                CalculateVAHVALForRange(lookbackBars);

                if (vah <= 0 || val <= 0 || vah <= val)
                {
                    this.Log($"VAH/VAL計算失敗或無效 (VAH: {vah:F5}, VAL: {val:F5})，使用原始FIBO位置", StrategyLoggingLevel.Trading);
                    return originalFiboData;
                }

                this.Log($"VAH/VAL計算成功 - VAH: {vah:F5}, VAL: {val:F5}, 範圍: {vah - val:F5}", StrategyLoggingLevel.Trading);

                // 篩選FIBO位置：只保留在VAH到VAL範圍外的位置
                var filteredData = new FiboLevelsData
                {
                    Bottom1382 = IsOutsideVAHVALRange(originalFiboData.Bottom1382) ? originalFiboData.Bottom1382 : double.NaN,
                    Bottom1618 = IsOutsideVAHVALRange(originalFiboData.Bottom1618) ? originalFiboData.Bottom1618 : double.NaN,
                    Top1382 = IsOutsideVAHVALRange(originalFiboData.Top1382) ? originalFiboData.Top1382 : double.NaN,
                    Top1618 = IsOutsideVAHVALRange(originalFiboData.Top1618) ? originalFiboData.Top1618 : double.NaN,
                    ActualLookback = originalFiboData.ActualLookback
                };

                // 記錄篩選結果
                LogFiboFilteringResults(originalFiboData, filteredData);

                return filteredData;
            }
            catch (Exception ex)
            {
                this.Log($"VAH/VAL篩選FIBO位置時發生錯誤: {ex.Message}，使用原始FIBO位置", StrategyLoggingLevel.Error);
                return originalFiboData;
            }
        }

        /// <summary>
        /// 檢查價格是否在VAH到VAL範圍外
        /// </summary>
        private bool IsOutsideVAHVALRange(double price)
        {
            if (double.IsNaN(price) || vah <= 0 || val <= 0)
                return true; // 如果價格無效或VAH/VAL無效，保留該位置

            // 價格在VAH之上或VAL之下才保留
            bool isOutside = price > vah || price < val;

            if (!isOutside)
            {
                this.Log($"FIBO位置 {price:F5} 在VAH/VAL範圍內 [{val:F5}, {vah:F5}]，被篩除", StrategyLoggingLevel.Trading);
            }
            else
            {
                string position = price > vah ? "VAH之上" : "VAL之下";
                this.Log($"FIBO位置 {price:F5} 在{position}，保留使用", StrategyLoggingLevel.Trading);
            }

            return isOutside;
        }

        /// <summary>
        /// 記錄FIBO篩選結果
        /// </summary>
        private void LogFiboFilteringResults(FiboLevelsData original, FiboLevelsData filtered)
        {
            this.Log("=== FIBO位置VAH/VAL篩選結果 ===", StrategyLoggingLevel.Trading);
            this.Log($"VAH: {vah:F5}, VAL: {val:F5}", StrategyLoggingLevel.Trading);

            LogFiboLevelFiltering("Bottom1382", original.Bottom1382, filtered.Bottom1382);
            LogFiboLevelFiltering("Bottom1618", original.Bottom1618, filtered.Bottom1618);
            LogFiboLevelFiltering("Top1382", original.Top1382, filtered.Top1382);
            LogFiboLevelFiltering("Top1618", original.Top1618, filtered.Top1618);

            this.Log("=== 篩選完成 ===", StrategyLoggingLevel.Trading);
        }

        /// <summary>
        /// 記錄單個FIBO位置的篩選結果
        /// </summary>
        private void LogFiboLevelFiltering(string levelName, double originalLevel, double filteredLevel)
        {
            if (double.IsNaN(originalLevel))
            {
                this.Log($"{levelName}: 原始無效 -> 保持無效", StrategyLoggingLevel.Trading);
            }
            else if (double.IsNaN(filteredLevel))
            {
                this.Log($"{levelName}: {originalLevel:F5} -> 被篩除（在VAH/VAL範圍內）", StrategyLoggingLevel.Trading);
            }
            else
            {
                this.Log($"{levelName}: {originalLevel:F5} -> 保留（在VAH/VAL範圍外）", StrategyLoggingLevel.Trading);
            }
        }

        /// <summary>
        /// 計算指定範圍內的VAH和VAL（參考Selected_Range_Lines_Indicator.cs的CalculateVolumeProfile方法）
        /// </summary>
        private void CalculateVAHVALForRange(int lookbackBars)
        {
            try
            {
                // 重置VAH、VAL、POC
                vah = 0;
                val = 0;
                poc = 0;

                // 確定計算範圍：從lookbackBars前到當前
                int startIndex = Math.Min(lookbackBars, this.historicalData.Count - 1);
                int endIndex = 0; // 當前K線

                if (startIndex <= endIndex || startIndex >= this.historicalData.Count)
                {
                    this.Log($"VAH/VAL計算範圍無效: startIndex={startIndex}, endIndex={endIndex}, dataCount={this.historicalData.Count}", StrategyLoggingLevel.Trading);
                    return;
                }

                this.Log($"開始計算VAH/VAL，範圍: {startIndex}根K線前到當前 (共{startIndex - endIndex + 1}根K線)", StrategyLoggingLevel.Trading);

                Dictionary<double, double> volumeProfile = new Dictionary<double, double>();

                // 計算Volume Profile（參考Selected_Range_Lines_Indicator.cs的邏輯）
                for (int i = startIndex; i >= endIndex; i--)
                {
                    if (i >= this.historicalData.Count) continue;

                    var bar = (HistoryItemBar)this.historicalData[i];
                    if (bar == null) continue;

                    // 使用VolumeAnalysisData（最精確的方法）
                    if (bar.VolumeAnalysisData != null && bar.VolumeAnalysisData.PriceLevels != null)
                    {
                        try
                        {
                            double tickSize = this.CurrentSymbol.TickSize;

                            foreach (var priceLevel in bar.VolumeAnalysisData.PriceLevels)
                            {
                                double rawPrice = priceLevel.Key;
                                var volumeItem = priceLevel.Value;

                                if (volumeItem != null)
                                {
                                    double volume = volumeItem.Volume;

                                    if (volume > 0)
                                    {
                                        // 確保價格對齊到正確的tick級別
                                        double normalizedPrice = Math.Round(rawPrice / tickSize) * tickSize;

                                        if (volumeProfile.ContainsKey(normalizedPrice))
                                            volumeProfile[normalizedPrice] += volume;
                                        else
                                            volumeProfile[normalizedPrice] = volume;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            this.Log($"處理K線 {i} 的VolumeAnalysisData時發生錯誤: {ex.Message}", StrategyLoggingLevel.Error);
                        }
                    }
                    else
                    {
                        // 備用方法：使用基本的OHLCV數據
                        AddVolumeAtPriceLevel(volumeProfile, bar.High, bar.Volume / 4.0);
                        AddVolumeAtPriceLevel(volumeProfile, bar.Low, bar.Volume / 4.0);
                        AddVolumeAtPriceLevel(volumeProfile, bar.Open, bar.Volume / 4.0);
                        AddVolumeAtPriceLevel(volumeProfile, bar.Close, bar.Volume / 4.0);
                    }
                }

                if (volumeProfile.Count == 0)
                {
                    this.Log("無法獲取Volume Profile數據", StrategyLoggingLevel.Trading);
                    return;
                }

                this.Log($"Volume Profile計算完成，共{volumeProfile.Count}個價格級別", StrategyLoggingLevel.Trading);

                // 找到POC（成交量最大的價格）
                var pocEntry = volumeProfile.OrderByDescending(v => v.Value).First();
                poc = pocEntry.Key;

                // 計算總成交量
                double totalVolume = volumeProfile.Values.Sum();
                double valueAreaThreshold = totalVolume * 0.70; // 70%的成交量

                this.Log($"POC: {poc:F5}, 總成交量: {totalVolume:F0}, Value Area閾值: {valueAreaThreshold:F0}", StrategyLoggingLevel.Trading);

                // 計算Value Area
                CalculateValueAreaFromPOC(volumeProfile, valueAreaThreshold);

                this.Log($"VAH/VAL計算完成 - VAH: {vah:F5}, VAL: {val:F5}, POC: {poc:F5}", StrategyLoggingLevel.Trading);
            }
            catch (Exception ex)
            {
                this.Log($"計算VAH/VAL時發生錯誤: {ex.Message}", StrategyLoggingLevel.Error);
                // 如果計算失敗，重置為0
                vah = 0;
                val = 0;
                poc = 0;
            }
        }

        /// <summary>
        /// 備用的Volume計算方法（當Volume Analysis數據未加載時使用）
        /// </summary>
        private void AddVolumeAtPriceLevel(Dictionary<double, double> volumeProfile, double price, double volume)
        {
            if (volume <= 0) return;

            double tickSize = this.CurrentSymbol.TickSize;
            if (tickSize <= 0) tickSize = 0.01; // 備用tick size

            double normalizedPrice = Math.Round(price / tickSize) * tickSize;

            if (volumeProfile.ContainsKey(normalizedPrice))
                volumeProfile[normalizedPrice] += volume;
            else
                volumeProfile[normalizedPrice] = volume;
        }

        /// <summary>
        /// 根據POC計算Value Area（參考Selected_Range_Lines_Indicator.cs的CalculateValueAreaFromPOC方法）
        /// </summary>
        private void CalculateValueAreaFromPOC(Dictionary<double, double> volumeProfile, double valueAreaThreshold)
        {
            if (volumeProfile.Count == 0 || poc == 0) return;

            double accumulatedVolume = volumeProfile.ContainsKey(poc) ? volumeProfile[poc] : 0;
            double tickSize = this.CurrentSymbol.TickSize;

            // 確保tickSize有效
            if (tickSize <= 0)
            {
                // 如果tickSize無效，嘗試從價格數據推斷最小價格間隔
                var sortedPrices = volumeProfile.Keys.OrderBy(p => p).ToList();
                if (sortedPrices.Count > 1)
                {
                    double minDiff = double.MaxValue;
                    for (int i = 1; i < sortedPrices.Count; i++)
                    {
                        double diff = sortedPrices[i] - sortedPrices[i - 1];
                        if (diff > 0 && diff < minDiff)
                            minDiff = diff;
                    }
                    tickSize = minDiff > 0 ? minDiff : 0.01;
                }
                else
                {
                    tickSize = 0.01;
                }
            }

            double currentHigh = poc;
            double currentLow = poc;

            this.Log($"開始從POC {poc:F5} 計算Value Area，初始累積成交量: {accumulatedVolume:F0}, 目標: {valueAreaThreshold:F0}", StrategyLoggingLevel.Trading);

            // 從POC開始，向上下擴展直到達到70%的成交量
            int iterations = 0;
            while (accumulatedVolume < valueAreaThreshold && iterations < 1000) // 防止無限循環
            {
                iterations++;
                double volumeAbove = 0;
                double volumeBelow = 0;

                // 檢查上方一個tick的成交量，使用精確的價格對齊
                double priceAbove = Math.Round((currentHigh + tickSize) / tickSize) * tickSize;
                if (volumeProfile.ContainsKey(priceAbove))
                    volumeAbove = volumeProfile[priceAbove];

                // 檢查下方一個tick的成交量，使用精確的價格對齊
                double priceBelow = Math.Round((currentLow - tickSize) / tickSize) * tickSize;
                if (volumeProfile.ContainsKey(priceBelow))
                    volumeBelow = volumeProfile[priceBelow];

                // 選擇成交量較大的方向擴展
                if (volumeAbove >= volumeBelow && volumeAbove > 0)
                {
                    currentHigh = priceAbove;
                    accumulatedVolume += volumeAbove;
                    this.Log($"向上擴展到 {currentHigh:F5}，增加成交量: {volumeAbove:F0}，累積: {accumulatedVolume:F0}", StrategyLoggingLevel.Trading);
                }
                else if (volumeBelow > 0)
                {
                    currentLow = priceBelow;
                    accumulatedVolume += volumeBelow;
                    this.Log($"向下擴展到 {currentLow:F5}，增加成交量: {volumeBelow:F0}，累積: {accumulatedVolume:F0}", StrategyLoggingLevel.Trading);
                }
                else
                {
                    // 如果兩個方向都沒有成交量，停止擴展
                    this.Log($"兩個方向都沒有成交量，停止擴展。當前範圍: [{currentLow:F5}, {currentHigh:F5}]", StrategyLoggingLevel.Trading);
                    break;
                }

                // 防止無限循環的安全檢查
                if (currentHigh - currentLow > 1000 * tickSize)
                {
                    this.Log($"Value Area範圍過大，停止擴展。當前範圍: [{currentLow:F5}, {currentHigh:F5}]", StrategyLoggingLevel.Trading);
                    break;
                }
            }

            vah = currentHigh;
            val = currentLow;

            this.Log($"Value Area計算完成，迭代次數: {iterations}，最終範圍: VAL={val:F5}, VAH={vah:F5}，累積成交量: {accumulatedVolume:F0}/{valueAreaThreshold:F0}", StrategyLoggingLevel.Trading);
        }

        #endregion
    }

    // 數據結構用於存儲 FIBO 級別
    public class FiboLevel
    {
        public double Level { get; set; }
        public string Type { get; set; } // "Bottom1382", "Bottom1618", "Top1382", "Top1618"
        public int Lookback { get; set; }
        public DateTime Timestamp { get; set; }
    }

    // 數據結構用於存儲 FIBO 位置信息
    public class FiboLevelsData
    {
        public double Bottom1382 { get; set; }
        public double Bottom1618 { get; set; }
        public double Top1382 { get; set; }
        public double Top1618 { get; set; }
        public int ActualLookback { get; set; }
    }

    // 數據結構用於存儲 Box 邊界信息
    public class BoxBoundariesData
    {
        public double BottomBoxOC { get; set; }
        public double TopBoxOC { get; set; }
        public int ActualLookback { get; set; }
    }

    // 數據結構用於記錄買賣信號
    public class BuySellSignalRecord
    {
        public DateTime Time { get; set; }
        public double Price { get; set; }
        public bool IsBuySignal { get; set; }
        public double FiboLevel1 { get; set; }
        public double FiboLevel2 { get; set; }
        public int ActualLookback { get; set; }
    }

    // 趨勢線數據結構
    public class TrendLineData
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public double Slope { get; set; }
        public double Intercept { get; set; }
        public bool IsCurve { get; set; }
        public double CurvatureCoefficient { get; set; } = 0.0;
        public double LinearCoefficient { get; set; } = 0.0;
        public double ConstantTerm { get; set; } = 0.0;
        public int StartIndex { get; set; }
        public int EndIndex { get; set; }
    }

    // Pivot點數據結構
    public class PivotPoint
    {
        public int Index { get; set; }
        public double Price { get; set; }
        public PriceType PriceType { get; set; }
    }

    // 價格類型枚舉
    public enum PriceType
    {
        High,
        Low,
        BodyHighRight,
        BodyHighLeft,
        BodyLowRight,
        BodyLowLeft
    }

    // 趨勢線候選者類
    public class TrendLineCandidate
    {
        public PivotPoint StartPoint { get; set; }
        public PivotPoint EndPoint { get; set; }
        public List<int> TouchIndices { get; set; } = new List<int>();
        public double Score { get; set; }
        public int CrossedBodies { get; set; }
        public double EndpointTypeWeight { get; set; }
        public double TouchQualityScore { get; set; }
        public double TimeSpanScore { get; set; }
        public double AngleScore { get; set; }
    }

    // FIBO級別信息類
    public class FiboLevelInfo
    {
        public double Level { get; set; }
        public string Type { get; set; }
        public int Lookback { get; set; }
    }

    // InsideBar BOX數據結構
    public class InsideBarBox
    {
        public int Index { get; set; }
        public double High { get; set; }
        public double Low { get; set; }
        public int BarCount { get; set; }
        public DateTime Time { get; set; }
        public bool IsLargeBox => BarCount >= 15;
        public bool IsSmallBox => !IsLargeBox;
        public double MaxOC { get; set; }
        public double MinOC { get; set; }
    }

    // InsideBar配對數據結構
    public class InsideBarPair
    {
        public InsideBarBox TopBox { get; set; }
        public InsideBarBox BottomBox { get; set; }
        public int LookbackDistance { get; set; }
        public double Range => TopBox.High - BottomBox.Low;
        public bool IsValidPair => (TopBox.IsLargeBox && BottomBox.IsSmallBox) ||
                                  (TopBox.IsSmallBox && BottomBox.IsLargeBox) ||
                                  (TopBox.IsLargeBox && BottomBox.IsLargeBox);
        #region InsideBar檢測和配對方法（參考InsidebarChannelAug.cs和InsidebarChannel2.cs）

        /// <summary>
        /// 檢測InsideBar並創建BOX（參考InsidebarChannel2.cs的isInsideBar方法）
        /// </summary>
        private List<InsideBarBox> DetectInsideBars(int maxLookback)
    {
        var detectedBoxes = new List<InsideBarBox>();

        this.Log($"開始檢測InsideBar，範圍: {this.FiboLookbackBars} 到 {maxLookback}", StrategyLoggingLevel.Trading);

        // 每5根K線檢查一次，提高效率
        for (int lookback = this.FiboLookbackBars; lookback <= maxLookback; lookback += 5)
        {
            if (lookback + 1 >= this.historicalData.Count) continue;

            var currentBar = (HistoryItemBar)this.historicalData[lookback];
            var previousBar = (HistoryItemBar)this.historicalData[lookback + 1];

            // 檢查是否為InsideBar（參考InsidebarChannel2.cs的邏輯）
            if (IsInsideBar(currentBar, previousBar))
            {
                // 計算BOX的範圍和特性
                var box = CreateInsideBarBox(lookback, currentBar, previousBar);
                if (box != null)
                {
                    detectedBoxes.Add(box);
                    this.Log($"檢測到InsideBar BOX: Index={box.Index}, High={box.High:F5}, Low={box.Low:F5}, BarCount={box.BarCount}, IsLarge={box.IsLargeBox}", StrategyLoggingLevel.Trading);
                }
            }
        }

        this.Log($"InsideBar檢測完成，共找到 {detectedBoxes.Count} 個BOX", StrategyLoggingLevel.Trading);
        return detectedBoxes;
    }

    /// <summary>
    /// 檢查是否為InsideBar（參考InsidebarChannel2.cs的isInsideBar方法）
    /// </summary>
    private bool IsInsideBar(HistoryItemBar currentBar, HistoryItemBar previousBar)
    {
        // InsideBar定義：當前K線的High <= 前一根K線的High 且 當前K線的Low >= 前一根K線的Low
        bool isInside = currentBar.High <= previousBar.High && currentBar.Low >= previousBar.Low;

        if (isInside)
        {
            // 額外檢查：使用黃金比例驗證（參考InsidebarChannelAug.cs的邏輯）
            double previousRange = previousBar.High - previousBar.Low;
            double currentRange = currentBar.High - currentBar.Low;

            // 檢查當前K線是否足夠小（相對於前一根）
            double ratio = currentRange / previousRange;
            bool isValidRatio = ratio <= FIBONACCI_RATIO_2; // 小於0.382

            if (!isValidRatio)
            {
                return false; // 不符合黃金比例要求
            }
        }

        return isInside;
    }

    /// <summary>
    /// 創建InsideBar BOX（參考InsidebarChannelAug.cs的BOX創建邏輯）
    /// </summary>
    private InsideBarBox CreateInsideBarBox(int index, HistoryItemBar currentBar, HistoryItemBar previousBar)
    {
        try
        {
            // 計算BOX的範圍（使用前一根K線的範圍作為BOX邊界）
            double boxHigh = previousBar.High;
            double boxLow = previousBar.Low;

            // 計算BOX的K線數量（向前掃描連續的InsideBar）
            int barCount = CountConsecutiveInsideBars(index);

            // 計算開收盤價範圍
            double maxOC = Math.Max(currentBar.Open, currentBar.Close);
            double minOC = Math.Min(currentBar.Open, currentBar.Close);

            var box = new InsideBarBox
            {
                Index = index,
                High = boxHigh,
                Low = boxLow,
                BarCount = barCount,
                Time = Core.Instance.TimeUtils.ConvertFromUTCToTimeZone(currentBar.TimeLeft, Core.Instance.TimeUtils.SelectedTimeZone),
                MaxOC = maxOC,
                MinOC = minOC
            };

            return box;
        }
        catch (Exception ex)
        {
            this.Log($"創建InsideBar BOX時發生錯誤: {ex.Message}", StrategyLoggingLevel.Error);
            return null;
        }
    }

    /// <summary>
    /// 計算連續InsideBar的數量
    /// </summary>
    private int CountConsecutiveInsideBars(int startIndex)
    {
        int count = 1; // 至少包含當前的InsideBar

        // 向前檢查連續的InsideBar
        for (int i = startIndex - 1; i >= 0 && i + 1 < this.historicalData.Count; i--)
        {
            if (i + 1 >= this.historicalData.Count) break;

            var currentBar = (HistoryItemBar)this.historicalData[i];
            var previousBar = (HistoryItemBar)this.historicalData[i + 1];

            if (IsInsideBar(currentBar, previousBar))
            {
                count++;
            }
            else
            {
                break; // 不再是連續的InsideBar
            }
        }

        return count;
    }

    /// <summary>
    /// 找到有效的BOX配對（參考InsidebarChannelAug.cs的配對邏輯）
    /// </summary>
    private List<InsideBarPair> FindValidBoxPairs(List<InsideBarBox> boxes)
    {
        var validPairs = new List<InsideBarPair>();

        this.Log($"開始BOX配對，共有 {boxes.Count} 個BOX", StrategyLoggingLevel.Trading);

        // 按時間順序排序（最新的在前）
        var sortedBoxes = boxes.OrderBy(b => b.Index).ToList();

        for (int i = 0; i < sortedBoxes.Count; i++)
        {
            for (int j = i + 1; j < sortedBoxes.Count; j++)
            {
                var box1 = sortedBoxes[i];
                var box2 = sortedBoxes[j];

                // 確定哪個是上方BOX，哪個是下方BOX
                var topBox = box1.High > box2.High ? box1 : box2;
                var bottomBox = box1.High > box2.High ? box2 : box1;

                // 檢查配對有效性（參考InsidebarChannelAug.cs的validPair邏輯）
                if (IsValidBoxPair(topBox, bottomBox))
                {
                    var pair = new InsideBarPair
                    {
                        TopBox = topBox,
                        BottomBox = bottomBox,
                        LookbackDistance = Math.Min(topBox.Index, bottomBox.Index) // 使用較近的距離
                    };

                    validPairs.Add(pair);
                    this.Log($"找到有效配對: Top({topBox.Index}, {topBox.High:F5}, {(topBox.IsLargeBox ? "Large" : "Small")}) - Bottom({bottomBox.Index}, {bottomBox.Low:F5}, {(bottomBox.IsLargeBox ? "Large" : "Small")}), Range={pair.Range:F5}", StrategyLoggingLevel.Trading);
                }
            }
        }

        // 限制配對數量，避免過度計算
        if (validPairs.Count > 20)
        {
            validPairs = validPairs.OrderBy(p => p.LookbackDistance).Take(20).ToList();
            this.Log($"限制配對數量為20個，選擇距離最近的配對", StrategyLoggingLevel.Trading);
        }

        this.Log($"BOX配對完成，共找到 {validPairs.Count} 個有效配對", StrategyLoggingLevel.Trading);
        return validPairs;
    }

    /// <summary>
    /// 檢查BOX配對是否有效（參考InsidebarChannelAug.cs的配對規則）
    /// </summary>
    private bool IsValidBoxPair(InsideBarBox topBox, InsideBarBox bottomBox)
    {
        // 基本檢查：上方BOX的Low應該高於下方BOX的High（不重疊）
        if (topBox.Low <= bottomBox.High)
        {
            return false; // BOX重疊，無效配對
        }

        // 配對規則：大配小、小配大、大配大（參考InsidebarChannelAug.cs）
        bool validPairRule = (topBox.IsLargeBox && bottomBox.IsSmallBox) ||
                            (topBox.IsSmallBox && bottomBox.IsLargeBox) ||
                            (topBox.IsLargeBox && bottomBox.IsLargeBox);

        if (!validPairRule)
        {
            return false; // 不符合配對規則
        }

        // 時間距離檢查：兩個BOX之間的距離不能太遠
        int timeDifference = Math.Abs(topBox.Index - bottomBox.Index);
        if (timeDifference > 100) // 最多100根K線的距離
        {
            return false; // 距離太遠
        }

        // 範圍合理性檢查
        double range = topBox.High - bottomBox.Low;
        if (range <= 0)
        {
            return false; // 範圍無效
        }

        return true;
    }

    /// <summary>
    /// 基於BOX配對計算FIBO位置（參考Selected_Range_Lines_Indicator.cs的FIBO計算）
    /// </summary>
    private List<FiboLevelInfo> CalculateFiboLevelsFromPair(InsideBarPair pair)
    {
        var fiboLevels = new List<FiboLevelInfo>();

        try
        {
            // 計算通道範圍
            double channelHigh = pair.TopBox.High;
            double channelLow = pair.BottomBox.Low;
            double range = channelHigh - channelLow;

            this.Log($"計算配對FIBO - 通道範圍: {channelLow:F5} 到 {channelHigh:F5}, 範圍: {range:F5}", StrategyLoggingLevel.Trading);

            // 計算四種FIBO位置（參考Selected_Range_Lines_Indicator.cs）
            double bottom1382 = channelLow + range * (1 - FIBONACCI_RATIO); // 1.382擴展
            double bottom1618 = channelLow + range * (1 - FIBONACCI_RATIO_2); // 1.618擴展
            double top1382 = channelHigh - range * (1 - FIBONACCI_RATIO); // 1.382擴展
            double top1618 = channelHigh - range * (1 - FIBONACCI_RATIO_2); // 1.618擴展

            // 添加到結果列表
            fiboLevels.Add(new FiboLevelInfo { Level = bottom1382, Type = "Bottom1382", Lookback = pair.LookbackDistance });
            fiboLevels.Add(new FiboLevelInfo { Level = bottom1618, Type = "Bottom1618", Lookback = pair.LookbackDistance });
            fiboLevels.Add(new FiboLevelInfo { Level = top1382, Type = "Top1382", Lookback = pair.LookbackDistance });
            fiboLevels.Add(new FiboLevelInfo { Level = top1618, Type = "Top1618", Lookback = pair.LookbackDistance });

            this.Log($"計算出FIBO位置 - B1382: {bottom1382:F5}, B1618: {bottom1618:F5}, T1382: {top1382:F5}, T1618: {top1618:F5}", StrategyLoggingLevel.Trading);
        }
        catch (Exception ex)
        {
            this.Log($"計算FIBO位置時發生錯誤: {ex.Message}", StrategyLoggingLevel.Error);
        }

        return fiboLevels;
    }

    /// <summary>
    /// 選擇最佳FIBO位置組合（保持現有邏輯）
    /// </summary>
    private FiboLevelsData SelectBestFiboLevels(List<FiboLevelInfo> validFiboLevels)
    {
        // 選擇最佳的FIBO位置（優先選擇最近的、沒有互動的）
        var selectedBottom1382 = validFiboLevels.Where(f => f.Type == "Bottom1382").OrderBy(f => f.Lookback).FirstOrDefault();
        var selectedBottom1618 = validFiboLevels.Where(f => f.Type == "Bottom1618").OrderBy(f => f.Lookback).FirstOrDefault();
        var selectedTop1382 = validFiboLevels.Where(f => f.Type == "Top1382").OrderBy(f => f.Lookback).FirstOrDefault();
        var selectedTop1618 = validFiboLevels.Where(f => f.Type == "Top1618").OrderBy(f => f.Lookback).FirstOrDefault();

        var result = new FiboLevelsData
        {
            Bottom1382 = selectedBottom1382?.Level ?? double.NaN,
            Bottom1618 = selectedBottom1618?.Level ?? double.NaN,
            Top1382 = selectedTop1382?.Level ?? double.NaN,
            Top1618 = selectedTop1618?.Level ?? double.NaN,
            ActualLookback = validFiboLevels.Any() ? validFiboLevels.Min(f => f.Lookback) : -1
        };

        this.Log($"選擇最佳FIBO位置:", StrategyLoggingLevel.Trading);
        this.Log($"  B1382={result.Bottom1382:F5} (lookback:{selectedBottom1382?.Lookback ?? -1})", StrategyLoggingLevel.Trading);
        this.Log($"  B1618={result.Bottom1618:F5} (lookback:{selectedBottom1618?.Lookback ?? -1})", StrategyLoggingLevel.Trading);
        this.Log($"  T1382={result.Top1382:F5} (lookback:{selectedTop1382?.Lookback ?? -1})", StrategyLoggingLevel.Trading);
        this.Log($"  T1618={result.Top1618:F5} (lookback:{selectedTop1618?.Lookback ?? -1})", StrategyLoggingLevel.Trading);

        return result;
    }

        #endregion
    }
}
